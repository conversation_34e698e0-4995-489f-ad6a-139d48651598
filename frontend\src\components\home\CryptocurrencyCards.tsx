import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  SimpleGrid,
  VStack,
  HStack,
  Text,
  Button,
  Icon,
  Badge,
  Flex,
  useToast,
  Spinner,
  Center,
  useDisclosure,
  Tooltip,
  Image
} from '@chakra-ui/react';
import {
  FaBitcoin,
  FaEthereum,
  FaWallet,
  FaArrowUp,
  FaArrowDown,
  FaCopy,
  FaEye,
  FaEyeSlash
} from 'react-icons/fa';
import { SiDogecoin, SiTether } from 'react-icons/si';
import { useTranslation } from 'react-i18next';
import useAuth from '../../hooks/useAuth';
import useMobileResponsive from '../../hooks/useMobileResponsive';
import { SocketService } from '../../utils/socketService';

// Import modals
import DepositModal from '../modals/DepositModal';
import ThreeStepWithdrawModal from '../modals/ThreeStepWithdrawModal';

interface CryptocurrencyAddress {
  _id: string;
  cryptocurrency: string;
  address: string;
  network: string;
  isActive: boolean;
  isMainAddress: boolean;
  createdAt: string;
  updatedAt: string;
}

interface CryptocurrencyInfo {
  symbol: string;
  name: string;
  icon: React.ComponentType;
  color: string;
  bgColor: string;
}

const cryptocurrencyInfo: Record<string, CryptocurrencyInfo> = {
  BTC: {
    symbol: 'BTC',
    name: 'Bitcoin',
    icon: FaBitcoin,
    color: '#F7931A',
    bgColor: '#F7931A22'
  },
  ETH: {
    symbol: 'ETH',
    name: 'Ethereum',
    icon: FaEthereum,
    color: '#627EEA',
    bgColor: '#627EEA22'
  },
  USDT: {
    symbol: 'USDT',
    name: 'Tether',
    icon: SiTether,
    color: '#26A17B',
    bgColor: '#26A17B22'
  },
  TRX: {
    symbol: 'TRX',
    name: 'TRON',
    icon: () => <Text fontWeight="bold" fontSize="lg">T</Text>,
    color: '#FF060A',
    bgColor: '#FF060A22'
  },
  DOGE: {
    symbol: 'DOGE',
    name: 'Dogecoin',
    icon: SiDogecoin,
    color: '#C2A633',
    bgColor: '#C2A63322'
  }
};

const CryptocurrencyCards: React.FC = () => {
  const { t } = useTranslation();
  const toast = useToast();
  const { user } = useAuth();
  const { buttonHeight, shouldUseHoverEffects } = useMobileResponsive();

  // State
  const [addresses, setAddresses] = useState<CryptocurrencyAddress[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCrypto, setSelectedCrypto] = useState<string>('');
  const [hiddenAddresses, setHiddenAddresses] = useState<Set<string>>(new Set());

  // Modal states
  const { isOpen: isDepositOpen, onOpen: onDepositOpen, onClose: onDepositClose } = useDisclosure();
  const { isOpen: isWithdrawOpen, onOpen: onWithdrawOpen, onClose: onWithdrawClose } = useDisclosure();

  // Fetch cryptocurrency addresses
  const fetchAddresses = useCallback(async () => {
    try {
      setLoading(true);

      // Try to fetch from API first
      try {
        const response = await fetch('/api/admin/crypto-addresses');
        if (response.ok) {
          const data = await response.json();
          setAddresses(data.data || []);
          setLoading(false);
          return;
        }
      } catch (apiError) {
        console.warn('API not available, using mock data:', apiError);
      }

      // Fallback to mock data if API is not available
      const mockAddresses = [
        {
          _id: 'btc_1',
          cryptocurrency: 'BTC',
          address: '**********************************',
          network: 'mainnet',
          isActive: true,
          isMainAddress: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          _id: 'eth_1',
          cryptocurrency: 'ETH',
          address: '******************************************',
          network: 'mainnet',
          isActive: true,
          isMainAddress: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          _id: 'usdt_1',
          cryptocurrency: 'USDT',
          address: 'TKWLzPKNdgzVwYbSYnFVBcL1uEE9CfTQbX',
          network: 'mainnet',
          isActive: true,
          isMainAddress: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          _id: 'trx_1',
          cryptocurrency: 'TRX',
          address: 'TLPuNinqS5qHuVMHWadqA7RZ2LcxdjCWzb',
          network: 'mainnet',
          isActive: true,
          isMainAddress: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          _id: 'doge_1',
          cryptocurrency: 'DOGE',
          address: 'DH5yaieqoZN36fDVciNyRueRGvGLR3mr7L',
          network: 'mainnet',
          isActive: true,
          isMainAddress: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];

      setAddresses(mockAddresses);
    } catch (error) {
      console.error('Error fetching cryptocurrency addresses:', error);
      toast({
        title: t('common.error', 'Error'),
        description: t('home.cryptoCards.fetchError', 'Failed to load cryptocurrency addresses'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  }, [t, toast]);

  // Handle real-time crypto address updates
  const handleCryptoAddressUpdate = useCallback((data: any) => {
    console.log('Received crypto address update:', data);

    if (data.payload && data.payload.address) {
      const updatedAddress = data.payload.address;

      setAddresses(prevAddresses => {
        const existingIndex = prevAddresses.findIndex(
          addr => addr.cryptocurrency === updatedAddress.cryptocurrency
        );

        if (existingIndex !== -1) {
          // Update existing address
          const newAddresses = [...prevAddresses];
          newAddresses[existingIndex] = updatedAddress;
          return newAddresses;
        } else {
          // Add new address
          return [...prevAddresses, updatedAddress];
        }
      });

      toast({
        title: t('home.cryptoCards.updated', 'Address Updated'),
        description: t('home.cryptoCards.addressUpdated', `${updatedAddress.cryptocurrency} address has been updated`),
        status: 'info',
        duration: 3000,
        isClosable: true,
      });
    }
  }, [t, toast]);

  useEffect(() => {
    fetchAddresses();

    // Set up WebSocket connection for real-time updates
    let unsubscribe: (() => void) | null = null;

    const setupWebSocket = async () => {
      try {
        // Get SocketService instance
        const socketService = SocketService.getInstance();

        // Connect to WebSocket service
        await socketService.connect();

        // Subscribe to crypto address updates
        unsubscribe = socketService.subscribe('crypto_address_updated', handleCryptoAddressUpdate);

        console.log('WebSocket connected for crypto address updates');
      } catch (error) {
        console.error('Failed to connect to WebSocket:', error);
        // Continue without WebSocket - addresses will still work without real-time updates
      }
    };

    setupWebSocket();

    // Cleanup function
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
      // Don't disconnect as other components might be using the socket
    };
  }, [fetchAddresses, handleCryptoAddressUpdate]);

  // Handle deposit button click
  const handleDepositClick = (crypto: string) => {
    if (!user) {
      toast({
        title: t('home.deposit.loginRequired', 'Login Required'),
        description: t('home.deposit.loginRequiredDesc', 'Please log in to make a deposit.'),
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }
    setSelectedCrypto(crypto);
    onDepositOpen();
  };

  // Handle withdraw button click
  const handleWithdrawClick = (crypto: string) => {
    if (!user) {
      toast({
        title: t('home.withdraw.loginRequired', 'Login Required'),
        description: t('home.withdraw.loginRequiredDesc', 'Please log in to withdraw funds.'),
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }
    setSelectedCrypto(crypto);
    onWithdrawOpen();
  };

  // Copy address to clipboard
  const copyAddress = (address: string, crypto: string) => {
    navigator.clipboard.writeText(address);
    toast({
      title: t('common.copied', 'Copied!'),
      description: t('home.cryptoCards.addressCopied', `${crypto} address copied to clipboard`),
      status: 'success',
      duration: 2000,
      isClosable: true,
    });
  };

  // Toggle address visibility
  const toggleAddressVisibility = (addressId: string) => {
    const newHidden = new Set(hiddenAddresses);
    if (newHidden.has(addressId)) {
      newHidden.delete(addressId);
    } else {
      newHidden.add(addressId);
    }
    setHiddenAddresses(newHidden);
  };

  // Format address for display
  const formatAddress = (address: string, isHidden: boolean) => {
    if (isHidden) {
      return '••••••••••••••••••••••••••••••••••••';
    }
    return `${address.slice(0, 8)}...${address.slice(-8)}`;
  };

  if (loading) {
    return (
      <Box py={8}>
        <Center>
          <VStack spacing={4}>
            <Spinner color="#F0B90B" size="xl" thickness="4px" />
            <Text color="#848E9C">
              {t('home.cryptoCards.loading', 'Loading cryptocurrency addresses...')}
            </Text>
          </VStack>
        </Center>
      </Box>
    );
  }

  // Create a map of addresses by cryptocurrency
  const addressMap = addresses.reduce((acc, address) => {
    acc[address.cryptocurrency] = address;
    return acc;
  }, {} as Record<string, CryptocurrencyAddress>);

  return (
    <Box py={{ base: 2, md: 3 }}>
      <VStack spacing={{ base: 3, md: 4 }} align="stretch">
        <Box textAlign="center">
          <Text
            fontSize={{ base: "lg", md: "xl" }}
            fontWeight="bold"
            color="#F0B90B"
            mb={0.5}
          >
            {t('home.cryptoCards.title', 'Supported Cryptocurrencies')}
          </Text>
          <Text color="#848E9C" fontSize={{ base: "xs", md: "sm" }}>
            {t('home.cryptoCards.description', 'Deposit and withdraw your favorite cryptocurrencies')}
          </Text>
        </Box>

        <SimpleGrid
          columns={{ base: 1, sm: 2, lg: 3, xl: 5 }}
          spacing={{ base: 3, md: 4 }}
          w="100%"
          sx={{
            // Mobile-specific grid optimizations
            '@media (max-width: 479px)': {
              // Single column on very small screens
              gridTemplateColumns: '1fr',
              gap: '12px',
            },
            '@media (min-width: 480px) and (max-width: 767px)': {
              // Two columns on larger mobile screens
              gridTemplateColumns: 'repeat(2, 1fr)',
              gap: '16px',
            },
            '@media (max-width: 767px)': {
              // Prevent horizontal overflow
              width: '100%',
              maxWidth: '100%',
              overflow: 'hidden',
            }
          }}
        >
          {Object.entries(cryptocurrencyInfo).map(([crypto, info]) => {
            const address = addressMap[crypto];
            if (!address || !address.isActive) return null;

            const isHidden = hiddenAddresses.has(address._id);

            return (
              <Box
                key={crypto}
                bg="#1E2026"
                p={{ base: 3, md: 4 }}
                borderRadius="md"
                borderWidth="1px"
                borderColor="#2B3139"
                _hover={shouldUseHoverEffects() ? {
                  borderColor: info.color,
                  transform: "translateY(-1px)",
                  boxShadow: `0 2px 12px ${info.color}22`
                } : {}}
                transition="all 0.3s ease"
                position="relative"
                overflow="hidden"
                minH="auto"
                className="crypto-card"
                sx={{
                  // Mobile-specific optimizations
                  '@media (max-width: 767px)': {
                    touchAction: 'manipulation',
                    WebkitTapHighlightColor: 'transparent',
                    WebkitTouchCallout: 'none',
                    WebkitUserSelect: 'none',
                    userSelect: 'none',
                    // Prevent layout shift on touch
                    transform: 'translateZ(0)',
                    backfaceVisibility: 'hidden',
                    // Optimize for mobile performance
                    willChange: 'transform',
                  }
                }}
              >
                {/* Background gradient */}
                <Box
                  position="absolute"
                  top={0}
                  left={0}
                  right={0}
                  bottom={0}
                  bg={`linear-gradient(135deg, ${info.bgColor} 0%, transparent 50%)`}
                  opacity={0.3}
                  zIndex={0}
                />

                <VStack spacing={3} position="relative" zIndex={1}>
                  {/* Cryptocurrency header */}
                  <HStack spacing={2} w="100%">
                    <Flex
                      align="center"
                      justify="center"
                      w={10}
                      h={10}
                      bg={info.bgColor}
                      borderRadius="full"
                      border="2px solid"
                      borderColor={info.color}
                    >
                      <Icon as={info.icon} color={info.color} boxSize={5} />
                    </Flex>
                    <VStack align="start" spacing={0} flex={1}>
                      <Text
                        fontWeight="bold"
                        color="#EAECEF"
                        fontSize={{ base: "sm", md: "md" }}
                      >
                        {info.symbol}
                      </Text>
                      <Text
                        color="#848E9C"
                        fontSize={{ base: "2xs", md: "xs" }}
                      >
                        {info.name}
                      </Text>
                    </VStack>
                    <Badge
                      bg="#02C076"
                      color="white"
                      px={1.5}
                      py={0.5}
                      borderRadius="full"
                      fontSize="2xs"
                    >
                      {t('home.cryptoCards.active', 'Active')}
                    </Badge>
                  </HStack>

                  {/* Address display */}
                  <Box w="100%" bg="#0B0E11" p={2} borderRadius="sm" borderWidth="1px" borderColor="#2B3139">
                    <VStack spacing={1}>
                      <HStack justify="space-between" w="100%">
                        <Text color="#848E9C" fontSize="2xs">
                          {t('home.cryptoCards.address', 'Address')}
                        </Text>
                        <HStack spacing={0.5}>
                          <Tooltip label={isHidden ? t('common.show', 'Show') : t('common.hide', 'Hide')}>
                            <Button
                              size="xs"
                              variant="ghost"
                              color="#848E9C"
                              _hover={{ color: "#F0B90B" }}
                              _active={{
                                color: "#F0B90B",
                                transform: 'scale(0.95)',
                              }}
                              onClick={() => toggleAddressVisibility(address._id)}
                              minW="auto"
                              h="auto"
                              p={0.5}
                              sx={{
                                // Mobile-specific optimizations
                                '@media (max-width: 767px)': {
                                  touchAction: 'manipulation',
                                  WebkitTapHighlightColor: 'transparent',
                                  minHeight: '32px',
                                  minWidth: '32px',
                                  padding: '8px',
                                }
                              }}
                            >
                              <Icon as={isHidden ? FaEye : FaEyeSlash} boxSize={2.5} />
                            </Button>
                          </Tooltip>
                          <Tooltip label={t('common.copy', 'Copy')}>
                            <Button
                              size="xs"
                              variant="ghost"
                              color="#848E9C"
                              _hover={{ color: "#F0B90B" }}
                              _active={{
                                color: "#F0B90B",
                                transform: 'scale(0.95)',
                              }}
                              onClick={() => copyAddress(address.address, crypto)}
                              minW="auto"
                              h="auto"
                              p={0.5}
                              sx={{
                                // Mobile-specific optimizations
                                '@media (max-width: 767px)': {
                                  touchAction: 'manipulation',
                                  WebkitTapHighlightColor: 'transparent',
                                  minHeight: '32px',
                                  minWidth: '32px',
                                  padding: '8px',
                                }
                              }}
                            >
                              <Icon as={FaCopy} boxSize={2.5} />
                            </Button>
                          </Tooltip>
                        </HStack>
                      </HStack>
                      <Text
                        color="#EAECEF"
                        fontSize="2xs"
                        fontFamily="mono"
                        wordBreak="break-all"
                        textAlign="center"
                        w="100%"
                      >
                        {formatAddress(address.address, isHidden)}
                      </Text>
                    </VStack>
                  </Box>

                  {/* Action buttons */}
                  <HStack spacing={1.5} w="100%">
                    <Button
                      leftIcon={<Icon as={FaArrowDown} boxSize={3} />}
                      bg={info.color}
                      color="white"
                      _hover={{ bg: `${info.color}DD` }}
                      _active={{
                        bg: `${info.color}BB`,
                        transform: 'scale(0.98)',
                      }}
                      size="xs"
                      flex={1}
                      minH={buttonHeight}
                      fontSize="2xs"
                      px={2}
                      py={1}
                      onClick={() => handleDepositClick(crypto)}
                      className="crypto-button"
                      sx={{
                        // Mobile-specific button optimizations
                        '@media (max-width: 767px)': {
                          touchAction: 'manipulation',
                          WebkitTapHighlightColor: 'transparent',
                          WebkitTouchCallout: 'none',
                          // Optimize touch response
                          cursor: 'pointer',
                          // Ensure minimum touch target
                          minHeight: '44px',
                          minWidth: '44px',
                        }
                      }}
                    >
                      {t('common.deposit', 'Deposit')}
                    </Button>
                    <Button
                      leftIcon={<Icon as={FaArrowUp} boxSize={3} />}
                      variant="outline"
                      borderColor={info.color}
                      color={info.color}
                      _hover={{ bg: `${info.color}22` }}
                      _active={{
                        bg: `${info.color}33`,
                        transform: 'scale(0.98)',
                        borderColor: info.color,
                      }}
                      size="xs"
                      flex={1}
                      minH={buttonHeight}
                      fontSize="2xs"
                      px={2}
                      py={1}
                      onClick={() => handleWithdrawClick(crypto)}
                      className="crypto-button"
                      sx={{
                        // Mobile-specific button optimizations
                        '@media (max-width: 767px)': {
                          touchAction: 'manipulation',
                          WebkitTapHighlightColor: 'transparent',
                          WebkitTouchCallout: 'none',
                          // Optimize touch response
                          cursor: 'pointer',
                          // Ensure minimum touch target
                          minHeight: '44px',
                          minWidth: '44px',
                        }
                      }}
                    >
                      {t('common.withdraw', 'Withdraw')}
                    </Button>
                  </HStack>
                </VStack>
              </Box>
            );
          })}
        </SimpleGrid>
      </VStack>

      {/* Modals */}
      <DepositModal
        isOpen={isDepositOpen}
        onClose={onDepositClose}
        defaultAsset={selectedCrypto}
      />
      <ThreeStepWithdrawModal
        isOpen={isWithdrawOpen}
        onClose={onWithdrawClose}
        initialCrypto={selectedCrypto}
      />
    </Box>
  );
};

export default CryptocurrencyCards;
