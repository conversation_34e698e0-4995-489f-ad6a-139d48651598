import React from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Heading,
  Badge,
  Button,
  Icon,
  Flex,
  useToast,
} from '@chakra-ui/react';
import {
  FaClock,
  FaFileExport,
  FaCopy,
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

interface InterestDistribution {
  id: string;
  packageId: string;
  cryptocurrency: string;
  amount: number;
  usdValue: number;
  distributionDate: string;
  type: 'daily' | 'bonus' | 'completion';
  status: 'completed' | 'pending' | 'failed';
  transactionId: string;
}

interface InterestDistributionHistoryProps {
  interestDistributions: InterestDistribution[];
  formatCryptocurrencyAmount: (amount: number, currency: string) => string;
  formatUSDValue: (amount: number) => string;
  handleCopyToClipboard: (text: string) => void;
}

const InterestDistributionHistory: React.FC<InterestDistributionHistoryProps> = ({
  interestDistributions,
  formatCryptocurrencyAmount,
  formatUSDValue,
  handleCopyToClipboard,
}) => {
  const { t } = useTranslation();
  const toast = useToast();

  return (
    <Box
      bg="#1E2026"
      p={6}
      borderRadius="lg"
      borderWidth="1px"
      borderColor="#2B3139"
      mb={6}
    >
      <Flex justify="space-between" align="center" mb={4}>
        <Heading size="md" color="#F0B90B">
          {t('investments.distributionHistory', 'Interest Distribution History')}
        </Heading>
        <HStack spacing={2}>
          <Badge bg="#02C076" color="white" px={3} py={1} borderRadius="full">
            {interestDistributions.filter(dist => dist.status === 'completed').length} {t('investments.completed', 'Completed')}
          </Badge>
          <Button
            size="sm"
            variant="outline"
            borderColor="#F0B90B"
            color="#F0B90B"
            _hover={{ bg: "#F0B90B22" }}
            leftIcon={<Icon as={FaFileExport} />}
          >
            {t('investments.exportDistributions', 'Export')}
          </Button>
        </HStack>
      </Flex>

      <Box overflowX="auto">
        <Box as="table" w="100%" color="#EAECEF">
          <Box as="thead" bg="#0B0E11">
            <Box as="tr">
              <Box as="th" textAlign="left" p={3}>
                <Text fontSize="sm" fontWeight="bold">
                  {t('investments.distributionTable.date', 'Distribution Date')}
                </Text>
              </Box>
              <Box as="th" textAlign="left" p={3}>
                <Text fontSize="sm" fontWeight="bold">
                  {t('investments.distributionTable.package', 'Package')}
                </Text>
              </Box>
              <Box as="th" textAlign="left" p={3}>
                <Text fontSize="sm" fontWeight="bold">
                  {t('investments.distributionTable.amount', 'Interest Amount')}
                </Text>
              </Box>
              <Box as="th" textAlign="left" p={3}>
                <Text fontSize="sm" fontWeight="bold">
                  {t('investments.distributionTable.type', 'Type')}
                </Text>
              </Box>
              <Box as="th" textAlign="left" p={3}>
                <Text fontSize="sm" fontWeight="bold">
                  {t('investments.distributionTable.status', 'Status')}
                </Text>
              </Box>
              <Box as="th" textAlign="left" p={3}>
                <Text fontSize="sm" fontWeight="bold">
                  {t('investments.distributionTable.txHash', 'Transaction')}
                </Text>
              </Box>
            </Box>
          </Box>
          <Box as="tbody">
            {interestDistributions.length === 0 ? (
              <Box as="tr">
                <Box as="td" colSpan={6} textAlign="center" p={8}>
                  <VStack spacing={3}>
                    <Icon as={FaClock} color="#848E9C" boxSize={12} />
                    <Text color="#848E9C">
                      {t('investments.noDistributions', 'No interest distributions yet')}
                    </Text>
                    <Text color="#848E9C" fontSize="sm">
                      {t('investments.distributionsWillAppear', 'Interest distributions will appear here after 03:00 UTC+3 daily')}
                    </Text>
                  </VStack>
                </Box>
              </Box>
            ) : interestDistributions.map((distribution, index) => (
              <Box as="tr" key={distribution.id} _hover={{ bg: "#2B3139" }}>
                <Box as="td" p={3}>
                  <VStack align="start" spacing={0}>
                    <Text fontSize="sm">
                      {new Date(distribution.distributionDate).toLocaleDateString()}
                    </Text>
                    <Text color="#848E9C" fontSize="xs">
                      {new Date(distribution.distributionDate).toLocaleTimeString()}
                    </Text>
                  </VStack>
                </Box>
                <Box as="td" p={3}>
                  <VStack align="start" spacing={0}>
                    <Text fontSize="sm" fontWeight="medium">
                      {distribution.cryptocurrency}
                    </Text>
                    <Text color="#848E9C" fontSize="xs">
                      {distribution.packageId.slice(0, 8)}...
                    </Text>
                  </VStack>
                </Box>
                <Box as="td" p={3}>
                  <VStack align="start" spacing={0}>
                    <Text fontSize="sm" fontWeight="bold" color="#02C076">
                      {formatCryptocurrencyAmount(distribution.amount, distribution.cryptocurrency)}
                    </Text>
                    {distribution.usdValue > 0 && (
                      <Text color="#848E9C" fontSize="xs">
                        {formatUSDValue(distribution.usdValue)}
                      </Text>
                    )}
                  </VStack>
                </Box>
                <Box as="td" p={3}>
                  <Badge
                    bg={distribution.type === 'daily' ? '#F0B90B22' :
                        distribution.type === 'bonus' ? '#02C07622' : '#848E9C22'}
                    color={distribution.type === 'daily' ? '#F0B90B' :
                           distribution.type === 'bonus' ? '#02C076' : '#848E9C'}
                    px={2}
                    py={1}
                    borderRadius="full"
                    fontSize="xs"
                    textTransform="capitalize"
                  >
                    {distribution.type}
                  </Badge>
                </Box>
                <Box as="td" p={3}>
                  <Badge
                    bg={distribution.status === 'completed' ? '#02C076' :
                        distribution.status === 'pending' ? '#F0B90B' : '#F84960'}
                    color="white"
                    px={2}
                    py={1}
                    borderRadius="full"
                    fontSize="xs"
                    textTransform="capitalize"
                  >
                    {distribution.status}
                  </Badge>
                </Box>
                <Box as="td" p={3}>
                  <HStack spacing={2}>
                    <Text fontSize="xs" color="#848E9C" maxW="100px" isTruncated>
                      {distribution.transactionId}
                    </Text>
                    <Button
                      size="xs"
                      variant="ghost"
                      color="#848E9C"
                      _hover={{ color: "#F0B90B" }}
                      onClick={() => {
                        handleCopyToClipboard(distribution.transactionId);
                        toast({
                          title: t('common.copied', 'Transaction ID copied!'),
                          status: "success",
                          duration: 2000,
                          isClosable: true,
                        });
                      }}
                    >
                      <Icon as={FaCopy} />
                    </Button>
                  </HStack>
                </Box>
              </Box>
            ))}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default InterestDistributionHistory;
