import { useEffect, useState } from 'react';
import axios from 'axios';
import { API_URL } from '../config';

// Define minimal system config interface for title management
interface SystemConfigBasic {
  siteName: string;
  siteDescription: string;
}

/**
 * Component to manage the document title based on system configuration
 * This component doesn't render anything, it just updates the document title
 * It uses a direct API call to the public endpoint to avoid authentication issues
 */
const TitleManager: React.FC = () => {
  const [config, setConfig] = useState<SystemConfigBasic | null>(null);
  const [loading, setLoading] = useState(true);

  // Fetch system config from public endpoint
  useEffect(() => {
    const fetchConfig = async () => {
      try {
        setLoading(true);
        // Use the new public endpoint that doesn't require authentication
        const response = await axios.get(`${API_URL}/user-system/public`);

        if (response.data && response.data.data) {
          setConfig(response.data.data);
        }
      } catch (error) {
        console.error('Error fetching system config for title:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchConfig();
  }, []);

  // Update document title when config is loaded
  useEffect(() => {
    if (!loading && config) {
      // Get the site name from system config
      const siteName = config.siteName || 'Shipping Finance';

      // Update the document title
      document.title = `${siteName} | Crypto Investment Platform`;

      // Also update the meta description if available
      if (config.siteDescription) {
        const metaDescription = document.querySelector('meta[name="description"]');
        if (metaDescription) {
          metaDescription.setAttribute('content', config.siteDescription);
        }
      }

      console.log('Updated document title to:', document.title);
    }
  }, [config, loading]);

  // This component doesn't render anything
  return null;
};

export default TitleManager;
