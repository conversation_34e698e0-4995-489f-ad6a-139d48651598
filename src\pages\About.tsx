import React from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  Grid,
  GridItem,
  Image,
  Flex,
  Icon,
  Divider,
  List,
  ListItem,
  ListIcon,
  Button,
  HStack,
} from '@chakra-ui/react';
import { FaShip, FaGlobe, FaChartLine, FaCheckCircle, FaUsers, FaHandshake, FaArrowRight } from 'react-icons/fa';
import { Link as RouterLink } from 'react-router-dom';

const About = () => {
  // Colors
  const bgColor = "#0B0E11";
  const cardBgColor = "#1E2329";
  const primaryColor = "#F0B90B";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const borderColor = "#2B3139";
  
  return (
    <Box bg={bgColor} minH="100vh">
      {/* Hero Section */}
      <Box 
        bg={`linear-gradient(rgba(11, 14, 17, 0.8), rgba(11, 14, 17, 0.9)), url('/images/global-trade.jpg')`}
        bgSize="cover"
        bgPosition="center"
        py={20}
      >
        <Container maxW="container.xl">
          <VStack spacing={6} align="center" textAlign="center" maxW="800px" mx="auto">
            <Heading 
              as="h1" 
              size="2xl" 
              color={primaryColor}
              lineHeight="1.2"
            >
              Hakkımızda
            </Heading>
            
            <Text fontSize="xl" color={textColor}>
              Shipping Finance, uluslararası ticaretin sunduğu fırsatları değerlendirerek yatırımcılara
              istikrarlı ve sürdürülebilir kazanç sunan yenilikçi bir Finance platformudur.
            </Text>
          </VStack>
        </Container>
      </Box>
      
      {/* Company Info Section */}
      <Box py={16}>
        <Container maxW="container.xl">
          <Grid templateColumns={{ base: "1fr", lg: "1fr 1fr" }} gap={10}>
            <GridItem>
              <VStack align="flex-start" spacing={6}>
                <Heading color={textColor}>Vizyonumuz</Heading>
                <Text fontSize="lg" color={secondaryTextColor}>
                  Shipping Finance olarak vizyonumuz, uluslararası ticaretin sunduğu fırsatları 
                  teknoloji ve Finance alanındaki yeniliklerle birleştirerek, yatırımcılarımıza 
                  güvenilir, şeffaf ve yüksek getirili bir yatırım platformu sunmaktır.
                </Text>
                
                <Heading color={textColor} size="md" pt={4}>Misyonumuz</Heading>
                <Text fontSize="lg" color={secondaryTextColor}>
                  Misyonumuz, küresel ticaret ağımız ve profesyonel ekibimizle, kripto para 
                  yatırımcılarına alternatif ve sürdürülebilir bir kazanç modeli sunarak, 
                  Financeal özgürlük yolculuklarında güvenilir bir ortak olmaktır.
                </Text>
                
                <Heading color={textColor} size="md" pt={4}>Değerlerimiz</Heading>
                <List spacing={3}>
                  <ListItem color={secondaryTextColor}>
                    <ListIcon as={FaCheckCircle} color={primaryColor} />
                    Şeffaflık ve güven odaklı iş modeli
                  </ListItem>
                  <ListItem color={secondaryTextColor}>
                    <ListIcon as={FaCheckCircle} color={primaryColor} />
                    Yatırımcı memnuniyeti ve sürdürülebilir kazanç
                  </ListItem>
                  <ListItem color={secondaryTextColor}>
                    <ListIcon as={FaCheckCircle} color={primaryColor} />
                    Yenilikçi ve teknoloji odaklı yaklaşım
                  </ListItem>
                  <ListItem color={secondaryTextColor}>
                    <ListIcon as={FaCheckCircle} color={primaryColor} />
                    Uluslararası standartlarda operasyon yönetimi
                  </ListItem>
                  <ListItem color={secondaryTextColor}>
                    <ListIcon as={FaCheckCircle} color={primaryColor} />
                    Sosyal ve çevresel sorumluluk
                  </ListItem>
                </List>
              </VStack>
            </GridItem>
            
            <GridItem>
              <Box 
                bg={cardBgColor} 
                p={8} 
                borderRadius="lg" 
                borderWidth="1px" 
                borderColor={borderColor}
                height="100%"
              >
                <VStack spacing={6} align="flex-start">
                  <Heading size="md" color={primaryColor}>Faaliyet Modelimiz</Heading>
                  
                  <Text color={textColor}>
                    Shipping Finance, düşük maliyetli ülkelerden (örneğin Çin, Mısır gibi) temin edilen
                    ürünlerin yüksek talep ve fiyat avantajı sunan pazarlarda (özellikle Avrupa ve diğer
                    gelişmiş bölgelerde) satılması prensibine dayanan bir iş modeli ile çalışmaktadır.
                  </Text>
                  
                  <Text color={textColor}>
                    Bu ticaret hacmi sayesinde yüksek kârlılık elde edilmekte ve yatırımcılarımıza günlük 
                    ortalama %1 oranında getiri sağlanmaktadır.
                  </Text>
                  
                  <Divider borderColor={borderColor} />
                  
                  <Heading size="md" color={primaryColor}>Nasıl Çalışır?</Heading>
                  
                  <List spacing={4}>
                    <ListItem color={textColor}>
                      <HStack align="flex-start" spacing={4}>
                        <Flex 
                          bg={`${primaryColor}20`} 
                          p={2} 
                          borderRadius="md"
                          mt={1}
                        >
                          <Icon as={FaShip} color={primaryColor} boxSize={4} />
                        </Flex>
                        <VStack align="flex-start" spacing={1}>
                          <Text color={textColor} fontWeight="bold">Ürün Tedariki</Text>
                          <Text color={secondaryTextColor}>
                            Düşük maliyetli üretim bölgelerinden kaliteli ürünlerin tedarik edilmesi
                          </Text>
                        </VStack>
                      </HStack>
                    </ListItem>
                    
                    <ListItem color={textColor}>
                      <HStack align="flex-start" spacing={4}>
                        <Flex 
                          bg={`${primaryColor}20`} 
                          p={2} 
                          borderRadius="md"
                          mt={1}
                        >
                          <Icon as={FaGlobe} color={primaryColor} boxSize={4} />
                        </Flex>
                        <VStack align="flex-start" spacing={1}>
                          <Text color={textColor} fontWeight="bold">Lojistik ve Dağıtım</Text>
                          <Text color={secondaryTextColor}>
                            Global lojistik ağımız ile ürünlerin hedef pazarlara ulaştırılması
                          </Text>
                        </VStack>
                      </HStack>
                    </ListItem>
                    
                    <ListItem color={textColor}>
                      <HStack align="flex-start" spacing={4}>
                        <Flex 
                          bg={`${primaryColor}20`} 
                          p={2} 
                          borderRadius="md"
                          mt={1}
                        >
                          <Icon as={FaChartLine} color={primaryColor} boxSize={4} />
                        </Flex>
                        <VStack align="flex-start" spacing={1}>
                          <Text color={textColor} fontWeight="bold">Satış ve Kâr</Text>
                          <Text color={secondaryTextColor}>
                            Yüksek talep bölgelerinde optimum fiyatlandırma ile satış ve kâr elde edilmesi
                          </Text>
                        </VStack>
                      </HStack>
                    </ListItem>
                    
                    <ListItem color={textColor}>
                      <HStack align="flex-start" spacing={4}>
                        <Flex 
                          bg={`${primaryColor}20`} 
                          p={2} 
                          borderRadius="md"
                          mt={1}
                        >
                          <Icon as={FaUsers} color={primaryColor} boxSize={4} />
                        </Flex>
                        <VStack align="flex-start" spacing={1}>
                          <Text color={textColor} fontWeight="bold">Yatırımcı Kazancı</Text>
                          <Text color={secondaryTextColor}>
                            Elde edilen kârın günlük %1 oranında yatırımcılarla paylaşılması
                          </Text>
                        </VStack>
                      </HStack>
                    </ListItem>
                  </List>
                </VStack>
              </Box>
            </GridItem>
          </Grid>
        </Container>
      </Box>
      
      {/* Team Section */}
      <Box py={16} bg={cardBgColor}>
        <Container maxW="container.xl">
          <VStack spacing={12}>
            <VStack spacing={4} textAlign="center" maxW="800px" mx="auto">
              <Heading color={textColor}>Profesyonel Ekibimiz</Heading>
              <Text fontSize="lg" color={secondaryTextColor}>
                Shipping Finance, uluslararası ticaret, Finance ve teknoloji alanlarında uzman 
                profesyonellerden oluşan deneyimli bir ekip tarafından yönetilmektedir.
              </Text>
            </VStack>
            
            <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)", lg: "repeat(3, 1fr)" }} gap={8}>
              {/* Team Member 1 */}
              <GridItem>
                <Box 
                  bg={bgColor} 
                  p={6} 
                  borderRadius="md" 
                  borderWidth="1px" 
                  borderColor={borderColor}
                  transition="transform 0.3s"
                  _hover={{ transform: "translateY(-5px)" }}
                >
                  <VStack spacing={4} align="center">
                    <Box 
                      w="120px" 
                      h="120px" 
                      borderRadius="full" 
                      bg={`${primaryColor}30`}
                      display="flex"
                      alignItems="center"
                      justifyContent="center"
                    >
                      <Icon as={FaUsers} color={primaryColor} boxSize={10} />
                    </Box>
                    
                    <Heading size="md" color={textColor}>Ahmet Yılmaz</Heading>
                    <Text color={primaryColor} fontWeight="medium">CEO & Kurucu</Text>
                    
                    <Text color={secondaryTextColor} textAlign="center">
                      15+ yıllık uluslararası ticaret ve Finance deneyimi ile şirketin vizyoner lideri.
                    </Text>
                  </VStack>
                </Box>
              </GridItem>
              
              {/* Team Member 2 */}
              <GridItem>
                <Box 
                  bg={bgColor} 
                  p={6} 
                  borderRadius="md" 
                  borderWidth="1px" 
                  borderColor={borderColor}
                  transition="transform 0.3s"
                  _hover={{ transform: "translateY(-5px)" }}
                >
                  <VStack spacing={4} align="center">
                    <Box 
                      w="120px" 
                      h="120px" 
                      borderRadius="full" 
                      bg={`${primaryColor}30`}
                      display="flex"
                      alignItems="center"
                      justifyContent="center"
                    >
                      <Icon as={FaChartLine} color={primaryColor} boxSize={10} />
                    </Box>
                    
                    <Heading size="md" color={textColor}>Mehmet Kaya</Heading>
                    <Text color={primaryColor} fontWeight="medium">Finance Direktörü</Text>
                    
                    <Text color={secondaryTextColor} textAlign="center">
                      Kripto para ve geleneksel Finance alanında uzman, yatırım stratejilerinin mimarı.
                    </Text>
                  </VStack>
                </Box>
              </GridItem>
              
              {/* Team Member 3 */}
              <GridItem>
                <Box 
                  bg={bgColor} 
                  p={6} 
                  borderRadius="md" 
                  borderWidth="1px" 
                  borderColor={borderColor}
                  transition="transform 0.3s"
                  _hover={{ transform: "translateY(-5px)" }}
                >
                  <VStack spacing={4} align="center">
                    <Box 
                      w="120px" 
                      h="120px" 
                      borderRadius="full" 
                      bg={`${primaryColor}30`}
                      display="flex"
                      alignItems="center"
                      justifyContent="center"
                    >
                      <Icon as={FaHandshake} color={primaryColor} boxSize={10} />
                    </Box>
                    
                    <Heading size="md" color={textColor}>Ayşe Demir</Heading>
                    <Text color={primaryColor} fontWeight="medium">Operasyon Müdürü</Text>
                    
                    <Text color={secondaryTextColor} textAlign="center">
                      Global lojistik ve tedarik zinciri yönetiminde uzman, operasyonel süreçlerin lideri.
                    </Text>
                  </VStack>
                </Box>
              </GridItem>
            </Grid>
          </VStack>
        </Container>
      </Box>
      
      {/* CTA Section */}
      <Box py={16}>
        <Container maxW="container.xl">
          <Box 
            bg={cardBgColor} 
            p={10} 
            borderRadius="lg" 
            borderWidth="1px" 
            borderColor={borderColor}
            position="relative"
            overflow="hidden"
            _before={{
              content: '""',
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              bgGradient: "linear(to-r, #F0B90B10, #F0B90B30)",
              opacity: 0.1,
              zIndex: 0
            }}
          >
            <Grid templateColumns={{ base: "1fr", md: "2fr 1fr" }} gap={8} position="relative" zIndex={1}>
              <GridItem>
                <VStack align="flex-start" spacing={4}>
                  <Heading color={primaryColor}>
                    Shipping Finance Ailesine Katılın
                  </Heading>
                  
                  <Text fontSize="lg" color={textColor}>
                    Kripto varlıklarınızı değerlendirmek ve günlük %1 kazanç elde etmek için 
                    hemen üye olun ve yatırım yapmaya başlayın.
                  </Text>
                </VStack>
              </GridItem>
              
              <GridItem display="flex" alignItems="center" justifyContent={{ base: "flex-start", md: "flex-end" }}>
                <Button 
                  as={RouterLink} 
                  to="/register" 
                  size="lg" 
                  bg={primaryColor}
                  color="#0B0E11"
                  _hover={{ bg: "#F8D12F" }}
                  px={8}
                  rightIcon={<FaArrowRight />}
                >
                  Hemen Üye Olun
                </Button>
              </GridItem>
            </Grid>
          </Box>
        </Container>
      </Box>
    </Box>
  );
};

export default About;
