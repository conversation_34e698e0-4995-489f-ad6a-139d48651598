import React, { useState, useEffect } from 'react';
import {
  Box,
  Heading,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  <PERSON>ge,
  Button,
  Flex,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  HStack,
  useToast,
  Text,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Grid,
  Card,
  CardBody,
  Icon,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Spinner,
  Center
} from '@chakra-ui/react';
import { SearchIcon } from '@chakra-ui/icons';
import { FaUsers, FaMoneyBillWave, FaLink, FaUserPlus, FaEye } from 'react-icons/fa';
import { adminApiService } from '../../services/adminApi';
import { formatDate } from '../../utils/formatters';

// Define interfaces for referral data
interface ReferredUser {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  createdAt: string;
  commission?: number;
}

interface Referral {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  referralCode: string;
  referralCount: number;
  referralEarnings: number;
  referredUsers?: ReferredUser[];
}

interface ReferralStats {
  totalReferrals: number;
  totalCommissions: number;
  usersWithReferrals: number;
  topReferrers: Referral[];
}

// Mock data for fallback
const mockReferrals: Referral[] = [
  {
    _id: '1',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    referralCode: 'JOHN123',
    referralCount: 12,
    referralEarnings: 1200,
    referredUsers: [
      { _id: '101', firstName: 'Alice', lastName: 'Smith', email: '<EMAIL>', createdAt: '2023-03-15', commission: 100 },
      { _id: '102', firstName: 'Bob', lastName: 'Johnson', email: '<EMAIL>', createdAt: '2023-03-20', commission: 150 },
      { _id: '103', firstName: 'Charlie', lastName: 'Brown', email: '<EMAIL>', createdAt: '2023-04-01', commission: 200 }
    ]
  },
  {
    _id: '2',
    firstName: 'Jane',
    lastName: 'Smith',
    email: '<EMAIL>',
    referralCode: 'JANE456',
    referralCount: 8,
    referralEarnings: 800,
    referredUsers: [
      { _id: '201', firstName: 'David', lastName: 'Wilson', email: '<EMAIL>', createdAt: '2023-02-10', commission: 100 },
      { _id: '202', firstName: 'Eva', lastName: 'Martinez', email: '<EMAIL>', createdAt: '2023-02-15', commission: 100 }
    ]
  }
];

const AdminReferrals = () => {
  const toast = useToast();
  const [referrals, setReferrals] = useState<Referral[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('earnings');
  const [selectedReferral, setSelectedReferral] = useState<Referral | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<ReferralStats>({
    totalReferrals: 0,
    totalCommissions: 0,
    usersWithReferrals: 0,
    topReferrers: []
  });

  const { isOpen, onOpen, onClose } = useDisclosure();

  // Colors
  const bgColor = "#1E2329";
  const cardBgColor = "#0B0E11";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";

  // Fetch referral stats from API
  useEffect(() => {
    fetchReferralStats();
  }, []);

  const fetchReferralStats = async () => {
    setLoading(true);
    setError(null);

    try {
      console.log('Fetching referral stats from API...');
      const response = await adminApiService.getReferralStats();
      console.log('API response:', response.data);

      if (response.data) {
        // Update stats
        const statsData = response.data.stats || {};
        setStats({
          totalReferrals: statsData.totalReferrals || 0,
          totalCommissions: statsData.totalCommissions || 0,
          usersWithReferrals: statsData.usersWithReferrals || 0,
          topReferrers: []
        });

        // Update referrals list
        if (response.data.referrals && Array.isArray(response.data.referrals)) {
          // Transform data to match our interface if needed
          const referralsData = response.data.referrals.map((referral: any) => ({
            _id: referral._id,
            firstName: referral.firstName || '',
            lastName: referral.lastName || '',
            email: referral.email || '',
            referralCode: referral.referralCode || '',
            referralCount: referral.referralCount || 0,
            referralEarnings: referral.referralEarnings || 0,
            referredUsers: referral.referredUsers || []
          }));

          setReferrals(referralsData);
          console.log('Referrals data loaded:', referralsData);
        } else {
          console.warn('No referrals array found in API response');
          // Fallback to mock data
          setReferrals(mockReferrals);
        }
      } else {
        console.warn('No data found in API response');
        // Fallback to mock data
        setReferrals(mockReferrals);
      }
    } catch (err) {
      console.error('Error fetching referral stats:', err);
      setError('Failed to load referral statistics');
      toast({
        title: 'Error',
        description: 'Failed to load referral statistics',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });

      // Fallback to mock data
      setReferrals(mockReferrals);
    } finally {
      setLoading(false);
    }
  };

  // Calculate total stats (use API data or calculated values)
  const totalReferrals = stats.totalReferrals || referrals.reduce((sum, user) => sum + user.referralCount, 0);
  const totalEarnings = stats.totalCommissions || referrals.reduce((sum, user) => sum + user.referralEarnings, 0);
  const totalUsers = referrals.length;
  const activeReferrers = stats.usersWithReferrals || referrals.filter(user => user.referralCount > 0).length;

  // Filter and sort referrals
  const filteredReferrals = referrals
    .filter(referral => {
      const fullName = `${referral.firstName} ${referral.lastName}`.toLowerCase();
      return fullName.includes(searchQuery.toLowerCase()) ||
        referral.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        referral.referralCode.toLowerCase().includes(searchQuery.toLowerCase());
    })
    .sort((a, b) => {
      if (sortBy === 'earnings') {
        return b.referralEarnings - a.referralEarnings;
      } else if (sortBy === 'count') {
        return b.referralCount - a.referralCount;
      } else {
        // Sort by name
        const nameA = `${a.firstName} ${a.lastName}`;
        const nameB = `${b.firstName} ${b.lastName}`;
        return nameA.localeCompare(nameB);
      }
    });

  const handleViewReferral = async (referral: Referral) => {
    try {
      console.log('Fetching referral details for user ID:', referral._id);
      // Fetch detailed referral information including referred users
      const response = await adminApiService.getReferralDetails(referral._id);
      console.log('Referral details response:', response.data);

      if (response.data) {
        // Update the selected referral with detailed information
        const updatedReferral = {
          ...referral,
          referredUsers: response.data.referredUsers || []
        };
        console.log('Updated referral with details:', updatedReferral);
        setSelectedReferral(updatedReferral);
      } else {
        console.warn('No data found in referral details response');
        // If no detailed data is available, use the basic referral info
        setSelectedReferral(referral);
      }

      onOpen();
    } catch (err) {
      console.error('Error fetching referral details:', err);
      toast({
        title: 'Error',
        description: 'Failed to load referral details',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });

      // Still open the modal with basic info
      setSelectedReferral(referral);
      onOpen();
    }
  };

  return (
    <Box>
      <Heading size="lg" color="#F0B90B" mb={6}>Referral Management</Heading>

      {/* Stats Overview */}
      <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)", lg: "repeat(4, 1fr)" }} gap={6} mb={8}>
        <Card bg={cardBgColor} borderColor={borderColor} borderWidth="1px">
          <CardBody>
            <Flex align="center">
              <Icon as={FaUsers} boxSize={10} color="#F0B90B" mr={4} />
              <Stat>
                <StatLabel color={secondaryTextColor}>Total Referrers</StatLabel>
                <StatNumber color={textColor}>{activeReferrers}</StatNumber>
                <StatHelpText color={secondaryTextColor}>Out of {totalUsers} users</StatHelpText>
              </Stat>
            </Flex>
          </CardBody>
        </Card>

        <Card bg={cardBgColor} borderColor={borderColor} borderWidth="1px">
          <CardBody>
            <Flex align="center">
              <Icon as={FaUserPlus} boxSize={10} color="#F0B90B" mr={4} />
              <Stat>
                <StatLabel color={secondaryTextColor}>Total Referrals</StatLabel>
                <StatNumber color={textColor}>{totalReferrals}</StatNumber>
                <StatHelpText color={secondaryTextColor}>New users joined</StatHelpText>
              </Stat>
            </Flex>
          </CardBody>
        </Card>

        <Card bg={cardBgColor} borderColor={borderColor} borderWidth="1px">
          <CardBody>
            <Flex align="center">
              <Icon as={FaMoneyBillWave} boxSize={10} color="#F0B90B" mr={4} />
              <Stat>
                <StatLabel color={secondaryTextColor}>Total Earnings</StatLabel>
                <StatNumber color={textColor}>${totalEarnings}</StatNumber>
                <StatHelpText color={secondaryTextColor}>Paid to referrers</StatHelpText>
              </Stat>
            </Flex>
          </CardBody>
        </Card>

        <Card bg={cardBgColor} borderColor={borderColor} borderWidth="1px">
          <CardBody>
            <Flex align="center">
              <Icon as={FaLink} boxSize={10} color="#F0B90B" mr={4} />
              <Stat>
                <StatLabel color={secondaryTextColor}>Conversion Rate</StatLabel>
                <StatNumber color={textColor}>
                  {totalUsers > 0 ? Math.round((totalReferrals / totalUsers) * 100) : 0}%
                </StatNumber>
                <StatHelpText color={secondaryTextColor}>Avg. referrals per user</StatHelpText>
              </Stat>
            </Flex>
          </CardBody>
        </Card>
      </Grid>

      <Box bg={bgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
        <Flex justify="space-between" align="center" mb={4} flexDir={{ base: "column", md: "row" }} gap={4}>
          <InputGroup maxW={{ base: "100%", md: "300px" }}>
            <InputLeftElement pointerEvents="none">
              <SearchIcon color="#848E9C" />
            </InputLeftElement>
            <Input
              placeholder="Search by user, email or referral code"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              bg={cardBgColor}
              borderColor={borderColor}
              color={textColor}
            />
          </InputGroup>

          <Select
            maxW={{ base: "100%", md: "200px" }}
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            bg={cardBgColor}
            borderColor={borderColor}
            color={textColor}
          >
            <option value="earnings">Sort by Earnings</option>
            <option value="count">Sort by Referral Count</option>
            <option value="name">Sort by Name</option>
          </Select>
        </Flex>

        <Box overflowX="auto">
          {loading ? (
            <Center p={8}>
              <Spinner size="xl" color="#F0B90B" />
            </Center>
          ) : error ? (
            <Center p={8}>
              <Text color="red.500">{error}</Text>
            </Center>
          ) : (
            <Table variant="simple" size="md">
              <Thead>
                <Tr>
                  <Th color={secondaryTextColor} borderColor={borderColor}>User</Th>
                  <Th color={secondaryTextColor} borderColor={borderColor}>Referral Code</Th>
                  <Th color={secondaryTextColor} borderColor={borderColor}>Referral Count</Th>
                  <Th color={secondaryTextColor} borderColor={borderColor}>Earnings</Th>
                  <Th color={secondaryTextColor} borderColor={borderColor}>Actions</Th>
                </Tr>
              </Thead>
              <Tbody>
                {filteredReferrals.length > 0 ? (
                  filteredReferrals.map((referral) => (
                    <Tr key={referral._id}>
                      <Td color={textColor} borderColor={borderColor}>
                        <Text>{referral.firstName} {referral.lastName}</Text>
                        <Text fontSize="xs" color={secondaryTextColor}>{referral.email}</Text>
                      </Td>
                      <Td color={textColor} borderColor={borderColor}>
                        <Badge colorScheme="yellow" px={2} py={1}>
                          {referral.referralCode}
                        </Badge>
                      </Td>
                      <Td color={textColor} borderColor={borderColor}>{referral.referralCount}</Td>
                      <Td color={textColor} borderColor={borderColor}>${referral.referralEarnings}</Td>
                      <Td borderColor={borderColor}>
                        <Button
                          size="sm"
                          colorScheme="blue"
                          leftIcon={<FaEye />}
                          onClick={() => handleViewReferral(referral)}
                        >
                          View Details
                        </Button>
                      </Td>
                    </Tr>
                  ))
                ) : (
                  <Tr>
                    <Td colSpan={5} textAlign="center" color={secondaryTextColor} borderColor={borderColor}>
                      No referrals found matching your search criteria.
                    </Td>
                  </Tr>
                )}
              </Tbody>
            </Table>
          )}
        </Box>
      </Box>

      {/* Referral Detail Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent bg={bgColor} color={textColor} borderColor={borderColor} borderWidth="1px">
          <ModalHeader>Referral Details: {selectedReferral ? `${selectedReferral.firstName} ${selectedReferral.lastName}` : ''}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {selectedReferral && (
              <Box>
                <Flex direction={{ base: "column", md: "row" }} gap={6} mb={6}>
                  <Box flex="1">
                    <Text fontWeight="bold" mb={1}>User Information</Text>
                    <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
                      <Text><strong>Name:</strong> {selectedReferral.firstName} {selectedReferral.lastName}</Text>
                      <Text><strong>Email:</strong> {selectedReferral.email}</Text>
                      <Text><strong>Referral Code:</strong> {selectedReferral.referralCode}</Text>
                    </Box>
                  </Box>

                  <Box flex="1">
                    <Text fontWeight="bold" mb={1}>Referral Statistics</Text>
                    <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
                      <Text><strong>Total Referrals:</strong> {selectedReferral.referralCount}</Text>
                      <Text><strong>Total Earnings:</strong> ${selectedReferral.referralEarnings}</Text>
                      <Text><strong>Average per Referral:</strong> ${selectedReferral.referralCount > 0 ? (selectedReferral.referralEarnings / selectedReferral.referralCount).toFixed(2) : 0}</Text>
                    </Box>
                  </Box>
                </Flex>

                <Text fontWeight="bold" mb={1}>Referred Users</Text>
                <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
                  {selectedReferral.referredUsers && selectedReferral.referredUsers.length > 0 ? (
                    <Table variant="simple" size="sm">
                      <Thead>
                        <Tr>
                          <Th color={secondaryTextColor} borderColor={borderColor}>Name</Th>
                          <Th color={secondaryTextColor} borderColor={borderColor}>Email</Th>
                          <Th color={secondaryTextColor} borderColor={borderColor}>Date</Th>
                          <Th color={secondaryTextColor} borderColor={borderColor}>Commission</Th>
                        </Tr>
                      </Thead>
                      <Tbody>
                        {selectedReferral.referredUsers.map((ref) => (
                          <Tr key={ref._id}>
                            <Td color={textColor} borderColor={borderColor}>{ref.firstName} {ref.lastName}</Td>
                            <Td color={textColor} borderColor={borderColor}>{ref.email}</Td>
                            <Td color={textColor} borderColor={borderColor}>{formatDate(ref.createdAt)}</Td>
                            <Td color={textColor} borderColor={borderColor}>${ref.commission || 0}</Td>
                          </Tr>
                        ))}
                      </Tbody>
                    </Table>
                  ) : (
                    <Text color={secondaryTextColor}>No referred users found.</Text>
                  )}
                </Box>
              </Box>
            )}
          </ModalBody>

          <ModalFooter>
            <Button colorScheme="blue" mr={3} onClick={() => {
              toast({
                title: "Report Generated",
                description: "Referral report has been generated and is ready for download.",
                status: "success",
                duration: 3000,
                isClosable: true,
              });
            }}>
              Generate Report
            </Button>
            <Button variant="ghost" onClick={onClose}>Close</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default AdminReferrals;
