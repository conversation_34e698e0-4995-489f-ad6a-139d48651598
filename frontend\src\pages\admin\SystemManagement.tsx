import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Ta<PERSON>,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Grid,
  GridItem,
  FormControl,
  FormLabel,
  FormHelperText,
  Input,
  Button,
  Text,
  Flex,
  VStack,
  HStack,
  useToast,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  IconButton,
  Badge,
  Divider,
  useColorModeValue,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  Select,
  Textarea,
  Switch,
  Card,
  CardBody,
  CardHeader,
  Icon,
  Tooltip,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
} from '@chakra-ui/react';
import { AddIcon, DeleteIcon, EditIcon, CheckIcon, CloseIcon, InfoIcon } from '@chakra-ui/icons';
import { FaCoins, FaCog, FaExclamationTriangle, FaGlobe, FaServer } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { API_URL } from "../../config";
import axios from 'axios';
import useAuth from '../../hooks/useAuth';
import { CRYPTO_NETWORKS, getDefaultNetwork } from '../../utils/cryptoNetworks';

// Define address with network interface
interface AddressWithNetwork {
  address: string;
  network: string;
}

// Define crypto address interface
interface CryptoAddress {
  currency: string;
  addresses: AddressWithNetwork[]; // Thay đổi từ string[] sang AddressWithNetwork[]
  currentIndex: number;
  enabled: boolean;
}

// Define system config interface
interface SystemConfig {
  siteName: string;
  siteDescription: string;
  maintenanceMode: boolean;
  commissionRate: number;
  referralRate: number;
  minimumDeposit: number;
  minimumWithdrawal: number;
  withdrawalsEnabled: boolean;
  depositsEnabled: boolean;
  cryptoAddresses: CryptoAddress[];
  supportedCurrencies: string[];
}

const SystemManagement = () => {
  const { t } = useTranslation();
  const toast = useToast();
  const { user, token } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // System config state
  const [systemConfig, setSystemConfig] = useState<SystemConfig>({
    siteName: 'Shipping Finance',
    siteDescription: 'Secure Crypto Investment Platform',
    maintenanceMode: false,
    commissionRate: 1.0,
    referralRate: 3.0,
    minimumDeposit: 100,
    minimumWithdrawal: 50,
    withdrawalsEnabled: true,
    depositsEnabled: true,
    cryptoAddresses: [],
    supportedCurrencies: ['BTC', 'ETH', 'USDT', 'BNB', 'DOGE', 'XRP'],
  });

  // Modal states for crypto address management
  const { isOpen: isAddressModalOpen, onOpen: onAddressModalOpen, onClose: onAddressModalClose } = useDisclosure();
  const [selectedCurrency, setSelectedCurrency] = useState('');
  const [addressInput, setAddressInput] = useState('');
  const [addressList, setAddressList] = useState<AddressWithNetwork[]>([]);
  const [isAddressEnabled, setIsAddressEnabled] = useState(true);
  const [selectedNetwork, setSelectedNetwork] = useState('');

  // Colors
  const bgColor = "#1E2329";
  const cardBgColor = "#0B0E11";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";

  // Fetch system config on component mount
  useEffect(() => {
    fetchSystemConfig();
  }, []);

  // Fetch system configuration from API
  const fetchSystemConfig = async () => {
    try {
      setIsLoading(true);
      const response = await axios.get(`${API_URL}/admin/system/config`, {
        withCredentials: true
      });

      if (response.data.success) {
        setSystemConfig(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching system config:', error);
      toast({
        title: t('admin.system.error'),
        description: t('admin.system.errorFetchingConfig'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Save system configuration
  const saveSystemConfig = async () => {
    try {
      setIsSaving(true);
      const response = await axios.put(
        `${API_URL}/admin/system/config`,
        systemConfig,
        { withCredentials: true }
      );

      if (response.data.success) {
        toast({
          title: t('admin.system.success'),
          description: t('admin.system.configSaved'),
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      }
    } catch (error) {
      console.error('Error saving system config:', error);
      toast({
        title: t('admin.system.error'),
        description: t('admin.system.errorSavingConfig'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Open modal to edit crypto addresses
  const handleEditAddresses = (currency: string) => {
    const cryptoAddress = systemConfig.cryptoAddresses.find(ca => ca.currency === currency);
    const defaultNetwork = getDefaultNetwork(currency);
    const defaultNetworkId = defaultNetwork ? defaultNetwork.id : '';

    if (cryptoAddress) {
      setSelectedCurrency(currency);

      // Kiểm tra nếu addresses là mảng chuỗi (cấu trúc cũ) thì chuyển đổi sang cấu trúc mới
      if (cryptoAddress.addresses.length > 0 && typeof cryptoAddress.addresses[0] === 'string') {
        // Chuyển đổi từ cấu trúc cũ sang cấu trúc mới
        const convertedAddresses = (cryptoAddress.addresses as unknown as string[]).map(address => ({
          address,
          network: cryptoAddress.network || defaultNetworkId
        }));
        setAddressList(convertedAddresses);
      } else {
        // Đã là cấu trúc mới
        setAddressList(cryptoAddress.addresses as AddressWithNetwork[]);
      }

      setIsAddressEnabled(cryptoAddress.enabled);
      setSelectedNetwork(defaultNetworkId); // Đặt network mặc định cho địa chỉ mới
    } else {
      setSelectedCurrency(currency);
      setAddressList([]);
      setIsAddressEnabled(true);
      setSelectedNetwork(defaultNetworkId);
    }

    onAddressModalOpen();
  };

  // Add address to the list
  const addAddress = () => {
    if (!addressInput.trim()) {
      toast({
        title: t('admin.system.error'),
        description: t('admin.system.errorEmptyAddress'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    // Thêm địa chỉ mới với network đã chọn
    const newAddress: AddressWithNetwork = {
      address: addressInput.trim(),
      network: selectedNetwork
    };

    setAddressList([...addressList, newAddress]);
    setAddressInput('');
  };

  // Remove address from the list
  const removeAddress = (index: number) => {
    const newList = [...addressList];
    newList.splice(index, 1);
    setAddressList(newList);
  };

  // Update network for a specific address
  const updateAddressNetwork = (index: number, network: string) => {
    const newList = [...addressList];
    newList[index] = {
      ...newList[index],
      network
    };
    setAddressList(newList);
  };

  // Save addresses for the selected currency
  const saveAddresses = async () => {
    try {
      setIsSaving(true);

      const response = await axios.put(
        `${API_URL}/admin/system/crypto-addresses/${selectedCurrency}`,
        {
          addresses: addressList,
          enabled: isAddressEnabled,
        },
        { withCredentials: true }
      );

      if (response.data.success) {
        // Update local state
        const updatedAddresses = [...systemConfig.cryptoAddresses];
        const existingIndex = updatedAddresses.findIndex(ca => ca.currency === selectedCurrency);

        if (existingIndex !== -1) {
          updatedAddresses[existingIndex] = {
            ...updatedAddresses[existingIndex],
            addresses: addressList,
            enabled: isAddressEnabled,
          };
        } else {
          updatedAddresses.push({
            currency: selectedCurrency,
            addresses: addressList,
            currentIndex: 0,
            enabled: isAddressEnabled,
          });
        }

        setSystemConfig({
          ...systemConfig,
          cryptoAddresses: updatedAddresses,
        });

        toast({
          title: t('admin.system.success'),
          description: t('admin.system.addressesSaved', { currency: selectedCurrency }),
          status: 'success',
          duration: 3000,
          isClosable: true,
        });

        onAddressModalClose();
      }
    } catch (error) {
      console.error('Error saving addresses:', error);
      toast({
        title: t('admin.system.error'),
        description: t('admin.system.errorSavingAddresses'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Box p={4}>
      <Heading size="lg" mb={6} color={textColor}>{t('admin.system.title')}</Heading>

      <Tabs variant="enclosed" colorScheme="yellow">
        <TabList borderColor={borderColor}>
          <Tab color={textColor} _selected={{ color: "#F0B90B", bg: cardBgColor, borderColor: borderColor }}>
            <Icon as={FaServer} mr={2} />
            {t('admin.system.generalSettings')}
          </Tab>
          <Tab color={textColor} _selected={{ color: "#F0B90B", bg: cardBgColor, borderColor: borderColor }}>
            <Icon as={FaCoins} mr={2} />
            {t('admin.system.cryptoAddresses')}
          </Tab>
        </TabList>

        <TabPanels>
          {/* General Settings Tab */}
          <TabPanel p={0} pt={4}>
            <Grid templateColumns={{ base: "1fr", lg: "repeat(2, 1fr)" }} gap={6}>
              {/* Site Settings */}
              <GridItem>
                <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                  <CardHeader>
                    <Heading size="md" color={textColor}>{t('admin.system.siteName')}</Heading>
                  </CardHeader>
                  <Divider borderColor={borderColor} />
                  <CardBody>
                    <VStack spacing={6} align="stretch">
                      <FormControl>
                        <FormLabel color={secondaryTextColor}>{t('admin.system.siteName')}</FormLabel>
                        <Input
                          value={systemConfig.siteName}
                          onChange={(e) => setSystemConfig({...systemConfig, siteName: e.target.value})}
                          bg={cardBgColor}
                          borderColor={borderColor}
                          color={textColor}
                        />
                      </FormControl>

                      <FormControl>
                        <FormLabel color={secondaryTextColor}>{t('admin.system.siteDescription')}</FormLabel>
                        <Input
                          value={systemConfig.siteDescription}
                          onChange={(e) => setSystemConfig({...systemConfig, siteDescription: e.target.value})}
                          bg={cardBgColor}
                          borderColor={borderColor}
                          color={textColor}
                        />
                      </FormControl>

                      <FormControl display="flex" alignItems="center">
                        <FormLabel htmlFor="maintenance-mode" mb="0" color={secondaryTextColor}>
                          {t('admin.system.maintenanceMode')}
                        </FormLabel>
                        <Switch
                          id="maintenance-mode"
                          isChecked={systemConfig.maintenanceMode}
                          onChange={(e) => setSystemConfig({...systemConfig, maintenanceMode: e.target.checked})}
                          colorScheme="yellow"
                        />
                      </FormControl>
                    </VStack>
                  </CardBody>
                </Card>
              </GridItem>

              {/* Financial Settings */}
              <GridItem>
                <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                  <CardHeader>
                    <Heading size="md" color={textColor}>{t('admin.system.financialSettings')}</Heading>
                  </CardHeader>
                  <Divider borderColor={borderColor} />
                  <CardBody>
                    <VStack spacing={6} align="stretch">
                      <FormControl>
                        <FormLabel color={secondaryTextColor}>{t('admin.system.commissionRate')}</FormLabel>
                        <Flex align="center">
                          <Input
                            type="number"
                            value={systemConfig.commissionRate}
                            onChange={(e) => setSystemConfig({...systemConfig, commissionRate: parseFloat(e.target.value)})}
                            bg={cardBgColor}
                            borderColor={borderColor}
                            color={textColor}
                            maxW="100px"
                          />
                          <Text ml={2} color={secondaryTextColor}>%</Text>
                        </Flex>
                      </FormControl>

                      <FormControl>
                        <FormLabel color={secondaryTextColor}>{t('admin.system.referralRate')}</FormLabel>
                        <Flex align="center">
                          <Input
                            type="number"
                            value={systemConfig.referralRate}
                            onChange={(e) => setSystemConfig({...systemConfig, referralRate: parseFloat(e.target.value)})}
                            bg={cardBgColor}
                            borderColor={borderColor}
                            color={textColor}
                            maxW="100px"
                          />
                          <Text ml={2} color={secondaryTextColor}>%</Text>
                        </Flex>
                      </FormControl>

                      <FormControl>
                        <FormLabel color={secondaryTextColor}>{t('admin.system.minimumDeposit')}</FormLabel>
                        <Input
                          type="number"
                          value={systemConfig.minimumDeposit}
                          onChange={(e) => setSystemConfig({...systemConfig, minimumDeposit: parseFloat(e.target.value)})}
                          bg={cardBgColor}
                          borderColor={borderColor}
                          color={textColor}
                        />
                      </FormControl>

                      <FormControl>
                        <FormLabel color={secondaryTextColor}>{t('admin.system.minimumWithdrawal')}</FormLabel>
                        <Input
                          type="number"
                          value={systemConfig.minimumWithdrawal}
                          onChange={(e) => setSystemConfig({...systemConfig, minimumWithdrawal: parseFloat(e.target.value)})}
                          bg={cardBgColor}
                          borderColor={borderColor}
                          color={textColor}
                        />
                      </FormControl>

                      <FormControl display="flex" alignItems="center">
                        <FormLabel htmlFor="deposits-enabled" mb="0" color={secondaryTextColor}>
                          {t('admin.system.depositsEnabled')}
                        </FormLabel>
                        <Switch
                          id="deposits-enabled"
                          isChecked={systemConfig.depositsEnabled}
                          onChange={(e) => setSystemConfig({...systemConfig, depositsEnabled: e.target.checked})}
                          colorScheme="yellow"
                        />
                      </FormControl>

                      <FormControl display="flex" alignItems="center">
                        <FormLabel htmlFor="withdrawals-enabled" mb="0" color={secondaryTextColor}>
                          {t('admin.system.withdrawalsEnabled')}
                        </FormLabel>
                        <Switch
                          id="withdrawals-enabled"
                          isChecked={systemConfig.withdrawalsEnabled}
                          onChange={(e) => setSystemConfig({...systemConfig, withdrawalsEnabled: e.target.checked})}
                          colorScheme="yellow"
                        />
                      </FormControl>
                    </VStack>
                  </CardBody>
                </Card>
              </GridItem>
            </Grid>

            <Flex justify="flex-end" mt={8}>
              <Button
                colorScheme="yellow"
                size="lg"
                onClick={saveSystemConfig}
                isLoading={isSaving}
                loadingText={t('admin.system.saving')}
              >
                {t('admin.system.saveSettings')}
              </Button>
            </Flex>
          </TabPanel>

          {/* Crypto Addresses Tab */}
          <TabPanel p={0} pt={4}>
            <Box bg={bgColor} p={6} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
              <Heading size="md" mb={4} color={textColor}>{t('admin.system.cryptoAddresses')}</Heading>

              <Table variant="simple" size="md">
                <Thead>
                  <Tr>
                    <Th color={secondaryTextColor} borderColor={borderColor}>{t('admin.system.currency')}</Th>
                    <Th color={secondaryTextColor} borderColor={borderColor}>{t('admin.system.addresses')}</Th>
                    <Th color={secondaryTextColor} borderColor={borderColor}>{t('admin.system.network', 'Network')}</Th>
                    <Th color={secondaryTextColor} borderColor={borderColor}>{t('admin.system.status')}</Th>
                    <Th color={secondaryTextColor} borderColor={borderColor}>{t('admin.system.actions')}</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {systemConfig.supportedCurrencies.map((currency) => {
                    const cryptoAddress = systemConfig.cryptoAddresses.find(ca => ca.currency === currency);
                    return (
                      <Tr key={currency}>
                        <Td color={textColor} borderColor={borderColor}>{currency}</Td>
                        <Td color={textColor} borderColor={borderColor}>
                          {cryptoAddress ? cryptoAddress.addresses.length : 0} {t('admin.system.addresses').toLowerCase()}
                        </Td>
                        <Td color={textColor} borderColor={borderColor}>
                          {cryptoAddress?.network ? (
                            <Badge colorScheme="blue" borderRadius="full" px={2}>
                              {CRYPTO_NETWORKS[currency]?.find(n => n.id === cryptoAddress.network)?.name || cryptoAddress.network}
                            </Badge>
                          ) : (
                            <Badge colorScheme="gray" borderRadius="full" px={2}>
                              {t('admin.system.defaultNetwork', 'Default')}
                            </Badge>
                          )}
                        </Td>
                        <Td borderColor={borderColor}>
                          <Badge
                            colorScheme={cryptoAddress?.enabled ? 'green' : 'red'}
                            borderRadius="full"
                            px={2}
                          >
                            {cryptoAddress?.enabled ? t('admin.system.enabled') : t('admin.system.disabled')}
                          </Badge>
                        </Td>
                        <Td borderColor={borderColor}>
                          <Button
                            size="sm"
                            leftIcon={<EditIcon />}
                            colorScheme="blue"
                            onClick={() => handleEditAddresses(currency)}
                          >
                            {t('admin.system.edit')}
                          </Button>
                        </Td>
                      </Tr>
                    );
                  })}
                </Tbody>
              </Table>
            </Box>
          </TabPanel>
        </TabPanels>
      </Tabs>

      {/* Crypto Address Modal */}
      <Modal isOpen={isAddressModalOpen} onClose={onAddressModalClose} size="xl">
        <ModalOverlay />
        <ModalContent bg={bgColor} color={textColor}>
          <ModalHeader>{t('admin.system.editAddresses')} {selectedCurrency}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4} align="stretch">
              <FormControl display="flex" alignItems="center">
                <FormLabel htmlFor="address-enabled" mb="0">
                  {t('admin.system.enable')} {selectedCurrency}
                </FormLabel>
                <Switch
                  id="address-enabled"
                  isChecked={isAddressEnabled}
                  onChange={(e) => setIsAddressEnabled(e.target.checked)}
                  colorScheme="yellow"
                />
              </FormControl>

              {/* Network Selector for new addresses */}
              <FormControl>
                <FormLabel>{t('admin.system.networkForNewAddresses', 'Network for new addresses')}</FormLabel>
                <Select
                  value={selectedNetwork}
                  onChange={(e) => setSelectedNetwork(e.target.value)}
                  bg={cardBgColor}
                  borderColor={borderColor}
                  color={textColor}
                >
                  {CRYPTO_NETWORKS[selectedCurrency]?.map((network) => (
                    <option key={network.id} value={network.id}>
                      {network.name} {network.isDefault ? '(Recommended)' : ''}
                    </option>
                  ))}
                </Select>
                <FormHelperText>
                  {t('admin.system.networkHelperText', 'This network will be used for new addresses you add')}
                </FormHelperText>
              </FormControl>

              <Divider borderColor={borderColor} />

              <FormControl>
                <FormLabel>{t('admin.system.addNewAddress')}</FormLabel>
                <Flex>
                  <Input
                    value={addressInput}
                    onChange={(e) => setAddressInput(e.target.value)}
                    placeholder={`${t('admin.system.add')} ${selectedCurrency} ${t('admin.system.addresses').toLowerCase()}`}
                    bg={cardBgColor}
                    borderColor={borderColor}
                    mr={2}
                  />
                  <Button colorScheme="green" onClick={addAddress}>
                    {t('admin.system.add')}
                  </Button>
                </Flex>
              </FormControl>

              <Box>
                <Text fontWeight="bold" mb={2}>{t('admin.system.currentAddresses')}</Text>
                {addressList.length === 0 ? (
                  <Text color={secondaryTextColor}>{t('admin.system.noAddresses')}</Text>
                ) : (
                  <VStack spacing={2} align="stretch">
                    {addressList.map((addressItem, index) => (
                      <Flex key={index} justify="space-between" align="center" p={2} bg={cardBgColor} borderRadius="md" flexWrap="wrap">
                        <Flex flex="1" direction={{ base: "column", md: "row" }} alignItems={{ base: "flex-start", md: "center" }} mb={{ base: 2, md: 0 }}>
                          <Text fontSize="sm" isTruncated maxW={{ base: "100%", md: "60%" }} mr={2}>{addressItem.address}</Text>
                          <Select
                            size="sm"
                            value={addressItem.network}
                            onChange={(e) => updateAddressNetwork(index, e.target.value)}
                            bg={cardBgColor}
                            borderColor={borderColor}
                            color={textColor}
                            maxW={{ base: "100%", md: "150px" }}
                            mt={{ base: 2, md: 0 }}
                          >
                            {CRYPTO_NETWORKS[selectedCurrency]?.map((network) => (
                              <option key={network.id} value={network.id}>
                                {network.name}
                              </option>
                            ))}
                          </Select>
                        </Flex>
                        <IconButton
                          aria-label={t('admin.system.removeAddress')}
                          icon={<DeleteIcon />}
                          size="sm"
                          colorScheme="red"
                          variant="ghost"
                          onClick={() => removeAddress(index)}
                          ml={2}
                        />
                      </Flex>
                    ))}
                  </VStack>
                )}
              </Box>
            </VStack>
          </ModalBody>

          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onAddressModalClose}>
              {t('common.cancel')}
            </Button>
            <Button
              colorScheme="yellow"
              onClick={saveAddresses}
              isLoading={isSaving}
              loadingText={t('common.saving')}
            >
              {t('admin.system.saveAddresses')}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default SystemManagement;
