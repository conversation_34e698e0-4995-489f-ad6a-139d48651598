import { Request, Response } from 'express';
import User from '../models/userModel';
import mongoose from 'mongoose';

// Get user's referral information
export const getReferralInfo = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;

    if (!userId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    const user = await User.findById(userId).select('referralCode referralCount referralEarnings');

    if (!user) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    // Get referred users (only basic info)
    const referredUsers = await User.find({ referredBy: user.referralCode })
      .select('firstName lastName email createdAt')
      .sort({ createdAt: -1 });

    res.status(200).json({
      referralCode: user.referralCode,
      referralCount: user.referralCount,
      referralEarnings: user.referralEarnings,
      referralLink: `${process.env.FRONTEND_URL || 'http://localhost:3000'}/register?ref=${user.referralCode}`,
      referredUsers: referredUsers
    });
  } catch (error) {
    console.error('Error getting referral info:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Apply referral code during registration
export const applyReferralCode = async (req: Request, res: Response) => {
  try {
    const { referralCode } = req.body;
    const userId = req.user?._id;

    if (!userId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    // Check if user already has a referrer
    const user = await User.findById(userId);

    if (!user) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    if (user.referredBy) {
      res.status(400).json({ message: 'You already have a referrer' });
      return;
    }

    // Check if referral code exists and is not user's own code
    if (referralCode === user.referralCode) {
      res.status(400).json({ message: 'You cannot refer yourself' });
      return;
    }

    const referrer = await User.findOne({ referralCode });

    if (!referrer) {
      res.status(404).json({ message: 'Invalid referral code' });
      return;
    }

    // Update user with referrer
    user.referredBy = referralCode;
    await user.save();

    // Update referrer's stats
    referrer.referralCount += 1;
    await referrer.save();

    res.status(200).json({ message: 'Referral code applied successfully' });
  } catch (error) {
    console.error('Error applying referral code:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Process referral commission when a deposit is made
export const processReferralCommission = async (
  userId: mongoose.Types.ObjectId | string,
  depositAmount: number
): Promise<void> => {
  try {
    const user = await User.findById(userId);

    if (!user || !user.referredBy) {
      return; // No referrer, no commission
    }

    const referrer = await User.findOne({ referralCode: user.referredBy });

    if (!referrer) {
      return; // Referrer not found
    }

    // Calculate commission (1% of deposit)
    const commissionRate = 0.01; // 1%
    const commissionAmount = depositAmount * commissionRate;

    // Update referrer's earnings
    referrer.referralEarnings += commissionAmount;
    await referrer.save();

    // Here you would also create a transaction record for the commission
    // and potentially notify the referrer about the commission

  } catch (error) {
    console.error('Error processing referral commission:', error);
    // Log error but don't throw - this is a background process
  }
};

// Get referral statistics for admin
export const getReferralStats = async (req: Request, res: Response) => {
  try {
    // Check if user is admin (implement your admin check)

    // Get top referrers
    const topReferrers = await User.find({ referralCount: { $gt: 0 } })
      .select('firstName lastName email referralCode referralCount referralEarnings')
      .sort({ referralCount: -1 })
      .limit(10);

    // Get total stats
    const stats = await User.aggregate([
      {
        $group: {
          _id: null,
          totalReferrals: { $sum: '$referralCount' },
          totalCommissions: { $sum: '$referralEarnings' },
          usersWithReferrals: {
            $sum: {
              $cond: [{ $gt: ['$referralCount', 0] }, 1, 0]
            }
          }
        }
      }
    ]);

    res.status(200).json({
      topReferrers,
      stats: stats[0] || { totalReferrals: 0, totalCommissions: 0, usersWithReferrals: 0 }
    });
  } catch (error) {
    console.error('Error getting referral stats:', error);
    res.status(500).json({ message: 'Server error' });
  }
};
