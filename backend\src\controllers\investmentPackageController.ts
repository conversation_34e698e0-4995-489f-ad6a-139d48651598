import { Request, Response } from 'express';
import InvestmentPackage from '../models/investmentPackageModel';
import InterestDistribution from '../models/interestDistributionModel';
import InvestmentPackageService from '../services/investmentPackageService';
import AuditTrail from '../models/auditTrailModel';
import DepositTransaction from '../models/depositTransactionModel';
import Transaction from '../models/transactionModel';
import cryptoApiService from '../services/cryptoApiService';
import timeService from '../services/timeService';
import { logger } from '../utils/logger';

// @desc    Create a new investment package
// @route   POST /api/investments/create
// @access  Private
export const createInvestmentPackage = async (req: Request, res: Response): Promise<void> => {
  try {
    const { amount, currency = 'USDT', compoundEnabled = false } = req.body;

    // Validate input
    if (!amount || amount <= 0) {
      res.status(400).json({
        status: 'error',
        message: 'Invalid amount',
        errors: {
          amount: 'Amount must be greater than 0'
        }
      });
      return;
    }

    // Check minimum investment amount (1 USDT equivalent)
    let usdtValue;
    try {
      usdtValue = await cryptoApiService.convertToUSDT(amount, currency);
    } catch (apiError) {
      console.log('API error, using mock conversion');
      // Mock conversion rates
      const rates: { [key: string]: number } = {
        'USDT': 1,
        'BTC': 45000,
        'ETH': 3000,
        'BNB': 300,
        'ADA': 0.5,
        'DOT': 8,
        'LINK': 15,
        'UNI': 7
      };
      usdtValue = amount * (rates[currency.toUpperCase()] || 1);
    }

    if (usdtValue < 1) {
      res.status(400).json({
        status: 'error',
        message: 'Minimum investment is 1 USDT equivalent'
      });
      return;
    }

    // Check time lock status
    const timeLockStatus = await timeService.getTimeLockStatus();
    if (timeLockStatus.isLocked && timeLockStatus.lockType === 'emergency') {
      res.status(423).json({
        status: 'error',
        message: 'Investment creation is temporarily locked',
        lockStatus: timeLockStatus
      });
      return;
    }

    // Create investment package
    let investmentPackage;
    try {
      investmentPackage = new InvestmentPackage({
        userId: req.user?._id || 'mock-user-id',
        amount,
        currency: currency.toUpperCase(),
        compoundEnabled,
        status: 'pending'
      });

      // Activate the package (sets activation time to next 03:00)
      await investmentPackage.activate();
    } catch (dbError) {
      console.log('Database error, creating mock package');
      // Mock package creation
      const nextActivation = new Date();
      nextActivation.setHours(3, 0, 0, 0);
      if (nextActivation <= new Date()) {
        nextActivation.setDate(nextActivation.getDate() + 1);
      }

      investmentPackage = {
        _id: 'mock-package-' + Date.now(),
        userId: req.user?._id || 'mock-user-id',
        amount,
        currency: currency.toUpperCase(),
        compoundEnabled,
        status: 'pending',
        activatedAt: nextActivation,
        createdAt: new Date(),
        save: async () => investmentPackage
      };
    }

    // Create audit trail
    try {
      await AuditTrail.createAuditLog({
        userId: req.user?._id || 'mock-user-id',
        action: 'INVESTMENT_CREATED',
        packageId: investmentPackage._id as any,
        amount,
        currency: currency.toUpperCase(),
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        details: {
          compoundEnabled,
          activationTime: investmentPackage.activatedAt,
          usdtValue
        }
      });
    } catch (auditError) {
      console.log('Audit trail error, continuing without audit log');
    }

    console.log(`New investment package created: ${investmentPackage._id} for user: ${req.user?._id || 'mock-user'}`);

    res.status(201).json({
      status: 'success',
      message: 'Investment package created successfully',
      data: {
        package: investmentPackage,
        activationTime: investmentPackage.activatedAt,
        nextCalculationTime: timeService.getNextUnlockTime()
      }
    });

  } catch (error: any) {
    console.error('Investment package creation error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to create investment package',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Get user's investment packages
// @route   GET /api/investments/packages
// @access  Private
export const getInvestmentPackages = async (req: Request, res: Response): Promise<void> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const status = req.query.status as string;
    const currency = req.query.currency as string;

    const userId = req.user?._id || 'mock-user-id';
    const query: any = { userId };

    // Apply filters
    if (status) query.status = status;
    if (currency) query.currency = currency.toUpperCase();

    let packages = [];
    let total = 0;

    try {
      const [packagesResult, totalResult] = await Promise.all([
        InvestmentPackage.find(query)
          .sort({ createdAt: -1 })
          .skip((page - 1) * limit)
          .limit(limit),
        InvestmentPackage.countDocuments(query)
      ]);
      packages = packagesResult;
      total = totalResult;
    } catch (dbError) {
      console.log('Database error, using mock packages data');
      // Mock packages data
      packages = [
        {
          _id: '1',
          amount: 1000,
          currency: 'USDT',
          status: 'active',
          createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
          activatedAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000),
          totalEarned: 40,
          dailyInterest: 10,
          interestRate: 0.01,
          activeDays: 4,
          compoundEnabled: false,
          calculateDailyInterest: () => 10,
          toObject: () => ({
            _id: '1',
            amount: 1000,
            currency: 'USDT',
            status: 'active',
            createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
            activatedAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000),
            totalEarned: 40,
            dailyInterest: 10,
            interestRate: 0.01,
            activeDays: 4,
            compoundEnabled: false
          })
        },
        {
          _id: '2',
          amount: 2500,
          currency: 'USDT',
          status: 'active',
          createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
          activatedAt: new Date(Date.now() - 9 * 24 * 60 * 60 * 1000),
          totalEarned: 225,
          dailyInterest: 25,
          interestRate: 0.01,
          activeDays: 9,
          compoundEnabled: true,
          calculateDailyInterest: () => 25,
          toObject: () => ({
            _id: '2',
            amount: 2500,
            currency: 'USDT',
            status: 'active',
            createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
            activatedAt: new Date(Date.now() - 9 * 24 * 60 * 60 * 1000),
            totalEarned: 225,
            dailyInterest: 25,
            interestRate: 0.01,
            activeDays: 9,
            compoundEnabled: true
          })
        }
      ];
      total = packages.length;
    }

    // Calculate current earnings for each package
    const packagesWithEarnings = packages.map(pkg => {
      const currentEarnings = pkg.calculateDailyInterest();
      return {
        ...pkg.toObject(),
        currentDailyEarnings: currentEarnings,
        projectedValue: pkg.amount + pkg.totalEarned + currentEarnings
      };
    });

    res.json({
      status: 'success',
      data: {
        packages: packagesWithEarnings,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error: any) {
    console.error('Investment packages fetch error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch investment packages',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Get total earnings summary
// @route   GET /api/investments/earnings
// @access  Private
export const getEarningsSummary = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?._id || 'mock-user-id';

    // Try to get real data, fallback to mock data
    let summary;
    try {
      const earningsData = await InvestmentPackage.getTotalEarnings(userId);
      summary = earningsData[0] || {
        totalEarned: 0,
        totalInvested: 0,
        activePackages: 0
      };
    } catch (dbError) {
      console.log('Database error, using mock data:', dbError.message);
      // Mock data for development
      summary = {
        totalEarned: 125.50,
        totalInvested: 5000,
        activePackages: 3
      };
    }

    // Get active packages for detailed breakdown
    let activePackages = [];
    let totalDailyEarnings = 0;

    try {
      activePackages = await InvestmentPackage.find({
        userId,
        status: 'active'
      });

      // Calculate total daily earnings
      totalDailyEarnings = activePackages.reduce((sum, pkg) => {
        return sum + pkg.calculateDailyInterest();
      }, 0);
    } catch (dbError) {
      console.log('Database error for active packages, using mock data');
      // Mock active packages
      activePackages = [];
      totalDailyEarnings = 50.25;
    }

    // Check if total earnings meet withdrawal minimum
    let withdrawalCheck;
    try {
      withdrawalCheck = await cryptoApiService.checkMinimumWithdrawal(
        summary.totalEarned,
        'USDT'
      );
    } catch (apiError) {
      console.log('API error, using mock withdrawal check');
      withdrawalCheck = {
        meetsMinimum: summary.totalEarned >= 50,
        minimumRequired: 50,
        usdtValue: summary.totalEarned
      };
    }

    // Get time until next calculation
    const timeUntilNext = timeService.getTimeUntilUnlock();

    res.json({
      status: 'success',
      data: {
        summary: {
          ...summary,
          totalDailyEarnings,
          canWithdraw: withdrawalCheck.meetsMinimum,
          minimumWithdrawal: withdrawalCheck.minimumRequired,
          currentUSDTValue: withdrawalCheck.usdtValue
        },
        activePackages: activePackages.length,
        nextCalculationTime: timeService.getNextUnlockTime(),
        timeUntilNext,
        timeLockStatus: await timeService.getTimeLockStatus()
      }
    });

  } catch (error: any) {
    console.error('Earnings summary fetch error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch earnings summary',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Request withdrawal of earnings
// @route   POST /api/investments/withdraw
// @access  Private
export const requestWithdrawal = async (req: Request, res: Response): Promise<void> => {
  try {
    const { amount, currency = 'USDT', emergency = false } = req.body;
    const userId = req.user._id;

    // Check time lock status
    const timeLockStatus = await timeService.getTimeLockStatus();
    if (timeLockStatus.isLocked && !emergency) {
      res.status(423).json({
        status: 'error',
        message: 'Withdrawals are locked until next trading session',
        lockStatus: timeLockStatus
      });
      return;
    }

    // Get user's total earnings
    const earningsData = await InvestmentPackage.getTotalEarnings(userId);
    const totalEarned = earningsData[0]?.totalEarned || 0;

    if (amount > totalEarned) {
      res.status(400).json({
        status: 'error',
        message: 'Insufficient earnings balance',
        available: totalEarned,
        requested: amount
      });
      return;
    }

    // Check minimum withdrawal threshold
    const withdrawalCheck = await cryptoApiService.checkMinimumWithdrawal(amount, currency);
    if (!withdrawalCheck.meetsMinimum && !emergency) {
      res.status(400).json({
        status: 'error',
        message: 'Amount below minimum withdrawal threshold',
        minimumRequired: withdrawalCheck.minimumRequired,
        currentValue: withdrawalCheck.usdtValue
      });
      return;
    }

    // Calculate fees
    let fee = 0;
    let feePercentage = 0;

    if (emergency) {
      feePercentage = 0.05; // 5% emergency withdrawal fee
      fee = amount * feePercentage;
    }

    const netAmount = amount - fee;

    // Find packages to deduct from (FIFO - oldest first)
    const activePackages = await InvestmentPackage.find({
      userId,
      status: 'active',
      totalEarned: { $gt: 0 }
    }).sort({ activatedAt: 1 });

    let remainingAmount = amount;
    const updatedPackages = [];

    for (const pkg of activePackages) {
      if (remainingAmount <= 0) break;

      const deductAmount = Math.min(remainingAmount, pkg.totalEarned);
      pkg.totalEarned -= deductAmount;
      remainingAmount -= deductAmount;

      await pkg.save();
      updatedPackages.push({
        packageId: pkg._id,
        deductedAmount: deductAmount,
        remainingEarnings: pkg.totalEarned
      });
    }

    // Create audit trail
    await AuditTrail.createAuditLog({
      userId,
      action: emergency ? 'EMERGENCY_WITHDRAWAL' : 'WITHDRAWAL_REQUESTED',
      amount,
      currency: currency.toUpperCase(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      details: {
        netAmount,
        fee,
        feePercentage,
        emergency,
        updatedPackages,
        timeLockStatus
      }
    });

    console.log(`Withdrawal requested: ${amount} ${currency} for user: ${userId} (emergency: ${emergency})`);

    res.json({
      status: 'success',
      message: emergency ? 'Emergency withdrawal processed' : 'Withdrawal request submitted',
      data: {
        requestedAmount: amount,
        fee,
        netAmount,
        currency: currency.toUpperCase(),
        emergency,
        updatedPackages: updatedPackages.length,
        estimatedProcessingTime: emergency ? 'Immediate' : '24-48 hours'
      }
    });

  } catch (error: any) {
    console.error('Withdrawal request error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to process withdrawal request',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Create investment package from deposit
// @route   POST /api/investment-packages/create-from-deposit
// @access  Private
export const createInvestmentFromDeposit = async (req: Request, res: Response): Promise<void> => {
  try {
    const { depositTransactionId } = req.body;

    if (!depositTransactionId) {
      res.status(400).json({
        status: 'error',
        message: 'Deposit transaction ID is required'
      });
      return;
    }

    // Get deposit transaction
    const depositTx = await DepositTransaction.findById(depositTransactionId);
    if (!depositTx) {
      res.status(404).json({
        status: 'error',
        message: 'Deposit transaction not found'
      });
      return;
    }

    // Check if deposit can create investment
    if (!depositTx.canCreateInvestment()) {
      res.status(400).json({
        status: 'error',
        message: 'Deposit transaction cannot create investment package',
        details: {
          status: depositTx.status,
          autoInvestmentEnabled: depositTx.autoInvestmentEnabled,
          hasExistingPackage: !!depositTx.investmentPackageId,
          usdtValue: depositTx.usdtValue
        }
      });
      return;
    }

    // Calculate USDT amount for investment
    const usdtAmount = await depositTx.calculateUSDTValue();

    if (usdtAmount < 1) {
      res.status(400).json({
        status: 'error',
        message: 'Deposit amount below minimum investment threshold (1 USDT)'
      });
      return;
    }

    // Create investment package
    const investmentPackage = new InvestmentPackage({
      userId: depositTx.userId,
      amount: usdtAmount, // Always store as USDT equivalent
      currency: 'USDT',
      compoundEnabled: false, // Default for auto-created packages
      status: 'pending',
      autoCreated: true,
      depositTransactionId: depositTx._id,
      depositCurrency: depositTx.currency,
      depositAmount: depositTx.amount,
      conversionRate: depositTx.conversionRate,
      originalUSDTValue: usdtAmount
    });

    // Activate the package (sets activation time to next 03:00)
    await investmentPackage.activate();

    // Link deposit transaction to investment package
    depositTx.investmentPackageId = investmentPackage._id;
    await depositTx.save();

    // Create audit trail
    try {
      await AuditTrail.createAuditLog({
        userId: depositTx.userId,
        action: 'AUTO_INVESTMENT_CREATED',
        packageId: investmentPackage._id as any,
        amount: usdtAmount,
        currency: 'USDT',
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        details: {
          depositTransactionId: depositTx._id,
          originalCurrency: depositTx.currency,
          originalAmount: depositTx.amount,
          conversionRate: depositTx.conversionRate,
          activationTime: investmentPackage.activatedAt,
          autoCreated: true
        }
      });
    } catch (auditError) {
      console.log('Audit trail error, continuing without audit log');
    }

    console.log(`Auto-investment package created: ${investmentPackage._id} from deposit: ${depositTx._id}`);

    res.status(201).json({
      status: 'success',
      message: 'Investment package created automatically from deposit',
      data: {
        package: investmentPackage,
        depositTransaction: depositTx,
        activationTime: investmentPackage.activatedAt,
        nextCalculationTime: timeService.getNextUnlockTime(),
        autoCreated: true
      }
    });

  } catch (error: any) {
    console.error('Auto-investment creation error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to create investment package from deposit',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get user's investment packages with comprehensive data
 * @route   GET /api/investment-packages/comprehensive
 * @access  Private
 */
export const getComprehensiveInvestmentData = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user._id;

    // Get comprehensive investment data using the service
    const investmentData = await InvestmentPackageService.getUserPackages(userId);

    // Get withdrawal eligibility
    const withdrawalEligibility = await InvestmentPackageService.getWithdrawalEligibility(userId);

    res.json({
      status: 'success',
      data: {
        ...investmentData,
        withdrawalEligibility
      }
    });

  } catch (error: any) {
    logger.error('Error fetching comprehensive investment data:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch investment data',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get interest distribution history
 * @route   GET /api/investment-packages/distributions
 * @access  Private
 */
export const getInterestDistributions = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user._id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const packageId = req.query.packageId as string;

    let query: any = { userId };
    if (packageId) {
      query.packageId = packageId;
    }

    const [distributions, total] = await Promise.all([
      InterestDistribution.find(query)
        .sort({ distributionDate: -1 })
        .skip((page - 1) * limit)
        .limit(limit)
        .populate('packageId', 'packageId currency amount')
        .lean(),
      InterestDistribution.countDocuments(query)
    ]);

    res.json({
      status: 'success',
      data: {
        distributions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error: any) {
    logger.error('Error fetching interest distributions:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch interest distributions',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Create investment package from transaction (automatic)
 * @route   POST /api/investment-packages/create-from-transaction
 * @access  Private
 */
export const createInvestmentFromTransaction = async (req: Request, res: Response): Promise<void> => {
  try {
    const { transactionId } = req.body;

    if (!transactionId) {
      res.status(400).json({
        status: 'error',
        message: 'Transaction ID is required'
      });
      return;
    }

    // Use the service to create investment package from transaction
    const investmentPackage = await InvestmentPackageService.createFromTransaction(transactionId);

    if (!investmentPackage) {
      res.status(400).json({
        status: 'error',
        message: 'Unable to create investment package from this transaction'
      });
      return;
    }

    res.status(201).json({
      status: 'success',
      message: 'Investment package created from transaction successfully',
      data: {
        package: investmentPackage
      }
    });

  } catch (error: any) {
    logger.error('Error creating investment from transaction:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to create investment package from transaction',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Get investment balances for withdrawal
// @route   GET /api/investments/balances
// @access  Private
export const getInvestmentBalances = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user._id;

    // Get all active investment packages for the user
    const packages = await InvestmentPackage.find({
      userId,
      status: 'active'
    });

    // Group by currency and calculate totals
    const balanceMap = new Map();

    packages.forEach(pkg => {
      const currency = pkg.currency.toUpperCase();
      const existing = balanceMap.get(currency) || {
        currency,
        totalEarnings: 0,
        availableForWithdrawal: 0,
        totalWithdrawn: 0,
        activePackages: 0,
        lastEarningDate: null
      };

      existing.totalEarnings += pkg.totalEarned || 0;
      existing.availableForWithdrawal += pkg.totalEarned || 0;
      existing.totalWithdrawn += pkg.totalWithdrawn || 0;
      existing.activePackages += 1;

      if (pkg.lastEarningDate && (!existing.lastEarningDate || pkg.lastEarningDate > existing.lastEarningDate)) {
        existing.lastEarningDate = pkg.lastEarningDate;
      }

      balanceMap.set(currency, existing);
    });

    const balances = Array.from(balanceMap.values());

    res.json({
      status: 'success',
      data: balances
    });
  } catch (error: any) {
    console.error('Get investment balances error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch investment balances',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Get investment balance for specific currency
// @route   GET /api/investments/balances/:currency
// @access  Private
export const getInvestmentBalanceByCurrency = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user._id;
    const { currency } = req.params;

    // Get active investment packages for the specific currency
    const packages = await InvestmentPackage.find({
      userId,
      currency: currency.toUpperCase(),
      status: 'active'
    });

    const balance = {
      currency: currency.toUpperCase(),
      totalEarnings: 0,
      availableForWithdrawal: 0,
      totalWithdrawn: 0,
      activePackages: packages.length,
      lastEarningDate: null as Date | null
    };

    packages.forEach(pkg => {
      balance.totalEarnings += pkg.totalEarned || 0;
      balance.availableForWithdrawal += pkg.totalEarned || 0;
      balance.totalWithdrawn += pkg.totalWithdrawn || 0;

      if (pkg.lastEarningDate && (!balance.lastEarningDate || pkg.lastEarningDate > balance.lastEarningDate)) {
        balance.lastEarningDate = pkg.lastEarningDate;
      }
    });

    res.json({
      status: 'success',
      data: balance
    });
  } catch (error: any) {
    console.error('Get investment balance by currency error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch investment balance',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Check withdrawal eligibility
// @route   POST /api/investments/withdrawal-eligibility
// @access  Private
export const checkWithdrawalEligibility = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user._id;
    const { currency, amount } = req.body;

    if (!currency || !amount || amount <= 0) {
      res.status(400).json({
        status: 'error',
        message: 'Currency and amount are required'
      });
      return;
    }

    // Get time lock status
    const timeLockStatus = await timeService.getTimeLockStatus();

    // Get available balance for the currency
    const packages = await InvestmentPackage.find({
      userId,
      currency: currency.toUpperCase(),
      status: 'active',
      totalEarned: { $gt: 0 }
    });

    const availableBalance = packages.reduce((sum, pkg) => sum + (pkg.totalEarned || 0), 0);
    const minimumRequired = 50; // 50 USDT equivalent

    const eligibility = {
      isEligible: availableBalance >= amount && amount >= minimumRequired && !timeLockStatus.isLocked,
      availableBalance,
      minimumRequired,
      timeLockStatus,
      currency: currency.toUpperCase()
    };

    res.json({
      status: 'success',
      data: eligibility
    });
  } catch (error: any) {
    console.error('Check withdrawal eligibility error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to check withdrawal eligibility',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

export default {
  createInvestmentPackage,
  getInvestmentPackages,
  getEarningsSummary,
  requestWithdrawal,
  createInvestmentFromDeposit,
  getComprehensiveInvestmentData,
  getInterestDistributions,
  createInvestmentFromTransaction,
  getInvestmentBalances,
  getInvestmentBalanceByCurrency,
  checkWithdrawalEligibility
};
