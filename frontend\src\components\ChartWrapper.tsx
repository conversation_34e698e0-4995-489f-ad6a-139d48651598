import React, { useState, useEffect } from 'react';
import { Box, Text, Center, Spinner } from '@chakra-ui/react';

interface ChartWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  height?: string | number;
  width?: string | number;
}

/**
 * A wrapper component for charts that handles errors and provides fallback UI
 */
const ChartWrapper: React.FC<ChartWrapperProps> = ({
  children,
  fallback,
  height = '300px',
  width = '100%'
}) => {
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading time to ensure all dependencies are loaded
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Add global error handler for chart-related errors
    const handleError = (event: ErrorEvent) => {
      // Check if the error is related to charts
      if (
        event.message.includes('Cannot access') ||
        event.message.includes('d3') ||
        event.message.includes('recharts') ||
        event.filename?.includes('charts')
      ) {
        console.error('Chart error caught:', event.message);
        setHasError(true);
        // Prevent the error from propagating
        event.preventDefault();
      }
    };

    window.addEventListener('error', handleError);

    return () => {
      window.removeEventListener('error', handleError);
    };
  }, []);

  if (isLoading) {
    return (
      <Box height={height} width={width} position="relative">
        <Center height="100%">
          <Spinner color="#F0B90B" size="xl" />
        </Center>
      </Box>
    );
  }

  if (hasError) {
    return (
      <Box 
        height={height} 
        width={width} 
        border="1px dashed" 
        borderColor="#2B3139"
        borderRadius="md"
        p={4}
      >
        {fallback || (
          <Center height="100%" flexDirection="column">
            <Text color="#EAECEF" mb={2}>Unable to load chart</Text>
            <Text color="#848E9C" fontSize="sm">
              There was an error loading the chart. Please try refreshing the page.
            </Text>
          </Center>
        )}
      </Box>
    );
  }

  return (
    <Box height={height} width={width}>
      {children}
    </Box>
  );
};

export default ChartWrapper;
