import React, { useState, useEffect } from 'react';
import {
  Box,
  Heading,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  <PERSON><PERSON>,
  Button,
  Flex,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  HStack,
  VStack,
  useToast,
  Text,
  Spinner,
  Center
} from '@chakra-ui/react';
import { SearchIcon } from '@chakra-ui/icons';
import { useNavigate } from 'react-router-dom';
import { Transaction } from '../../components/TransactionHistory';

// Default user data for display
const defaultUser = '<PERSON>';

const AdminTransactions = () => {
  const navigate = useNavigate();
  const toast = useToast();
  const [transactions, setTransactions] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [isLoading, setIsLoading] = useState(true);

  // Colors
  const bgColor = "#1E2329";
  const cardBgColor = "#0B0E11";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";

  // Load transactions from localStorage
  useEffect(() => {
    const loadTransactions = () => {
      try {
        const storedTransactions = localStorage.getItem('transactions');

        if (storedTransactions) {
          const parsedTransactions = JSON.parse(storedTransactions) as Transaction[];

          // Convert to admin format
          const adminTransactions = parsedTransactions.map(tx => ({
            id: tx.id,
            user: defaultUser,
            wallet: tx.walletAddress || '******************************************',
            amount: tx.amount,
            type: tx.type,
            date: tx.date,
            status: tx.status,
            currency: tx.currency
          }));

          setTransactions(adminTransactions);
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Error loading transactions:', error);
        setIsLoading(false);
      }
    };

    // Handle storage changes
    const handleStorageChange = () => {
      loadTransactions();
    };

    // Add event listeners
    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('transactionUpdated', handleStorageChange);

    // Initial load
    loadTransactions();

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('transactionUpdated', handleStorageChange);
    };
  }, []);

  // Update transaction in localStorage
  const updateTransactionInLocalStorage = (transactionId: string, newStatus: string) => {
    try {
      // Get transactions from localStorage
      const storedTransactions = localStorage.getItem('transactions');

      if (storedTransactions) {
        const transactions = JSON.parse(storedTransactions);
        let updatedTransaction = null;

        // Find and update the transaction
        const updatedTransactions = transactions.map((tx: any) => {
          if (tx.id === transactionId) {
            updatedTransaction = { ...tx, status: newStatus };
            return updatedTransaction;
          }
          return tx;
        });

        // Save updated transactions back to localStorage
        localStorage.setItem('transactions', JSON.stringify(updatedTransactions));

        // Update timestamp to trigger storage events
        localStorage.setItem('lastTransactionUpdate', Date.now().toString());

        // Dispatch custom event to notify components about the update
        const event = new CustomEvent('transactionUpdated', {
          detail: updatedTransaction
        });
        window.dispatchEvent(event);
        console.log(`AdminTransactions: Transaction ${transactionId} status updated to ${newStatus}`, updatedTransaction);

        // Dispatch a second event after a small delay to ensure it's caught
        setTimeout(() => {
          const secondEvent = new CustomEvent('transactionUpdated', {
            detail: updatedTransaction
          });
          window.dispatchEvent(secondEvent);
          console.log('AdminTransactions: Second transaction updated event dispatched');

          // Force a storage event by updating the timestamp again
          localStorage.setItem('lastTransactionUpdate', Date.now().toString());
        }, 100);
      }
    } catch (error) {
      console.error('Error updating transaction in localStorage:', error);
    }
  };

  // Filter transactions based on search query, status, and type
  const filteredTransactions = transactions.filter(tx => {
    const matchesSearch =
      (tx.user && tx.user.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (tx.wallet && tx.wallet.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesStatus = filterStatus === 'all' || tx.status === filterStatus;
    const matchesType = filterType === 'all' || tx.type === filterType;

    return matchesSearch && matchesStatus && matchesType;
  });

  const handleApprove = (txId: string) => {
    // Update local state
    setTransactions(transactions.map(tx =>
      tx.id === txId ? { ...tx, status: 'approved' } : tx
    ));

    // Update in localStorage
    updateTransactionInLocalStorage(txId, 'approved');

    toast({
      title: "Transaction Approved",
      description: `Transaction #${txId} has been approved.`,
      status: "success",
      duration: 3000,
      isClosable: true,
    });
  };

  const handleReject = (txId: string) => {
    // Update local state
    setTransactions(transactions.map(tx =>
      tx.id === txId ? { ...tx, status: 'rejected' } : tx
    ));

    // Update in localStorage
    updateTransactionInLocalStorage(txId, 'rejected');

    toast({
      title: "Transaction Rejected",
      description: `Transaction #${txId} has been rejected.`,
      status: "error",
      duration: 3000,
      isClosable: true,
    });
  };

  // If still loading
  if (isLoading) {
    return (
      <Box>
        <Heading size="lg" color="#F0B90B" mb={6}>Transaction Management</Heading>
        <Box bg={bgColor} p={8} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
          <Center>
            <VStack spacing={4}>
              <Spinner size="xl" color="#F0B90B" thickness="4px" />
              <Text color={textColor}>Loading transactions...</Text>
            </VStack>
          </Center>
        </Box>
      </Box>
    );
  }

  return (
    <Box>
      <Heading size="lg" color="#F0B90B" mb={6}>Transaction Management</Heading>

      <Box bg={bgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
        <Flex justify="space-between" align="center" mb={4} flexDir={{ base: "column", md: "row" }} gap={4}>
          <InputGroup maxW={{ base: "100%", md: "300px" }}>
            <InputLeftElement pointerEvents="none">
              <SearchIcon color="#848E9C" />
            </InputLeftElement>
            <Input
              placeholder="Search by user or wallet"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              bg={cardBgColor}
              borderColor={borderColor}
              color={textColor}
            />
          </InputGroup>

          <HStack spacing={4}>
            <Select
              maxW={{ base: "100%", md: "150px" }}
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              bg={cardBgColor}
              borderColor={borderColor}
              color={textColor}
            >
              <option value="all">All Status</option>
              <option value="approved">Approved</option>
              <option value="pending">Pending</option>
              <option value="rejected">Rejected</option>
            </Select>

            <Select
              maxW={{ base: "100%", md: "150px" }}
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              bg={cardBgColor}
              borderColor={borderColor}
              color={textColor}
            >
              <option value="all">All Types</option>
              <option value="deposit">Deposit</option>
              <option value="withdrawal">Withdrawal</option>
            </Select>
          </HStack>
        </Flex>

        <Box overflowX="auto">
          <Table variant="simple" size="md">
            <Thead>
              <Tr>
                <Th color={secondaryTextColor} borderColor={borderColor}>ID</Th>
                <Th color={secondaryTextColor} borderColor={borderColor}>User</Th>
                <Th color={secondaryTextColor} borderColor={borderColor}>Amount</Th>
                <Th color={secondaryTextColor} borderColor={borderColor}>Type</Th>
                <Th color={secondaryTextColor} borderColor={borderColor}>Date</Th>
                <Th color={secondaryTextColor} borderColor={borderColor}>Status</Th>
                <Th color={secondaryTextColor} borderColor={borderColor}>Actions</Th>
              </Tr>
            </Thead>
            <Tbody>
              {filteredTransactions.length > 0 ? (
                filteredTransactions.map((tx) => (
                  <Tr key={tx.id}>
                    <Td color={textColor} borderColor={borderColor}>{tx.id}</Td>
                    <Td color={textColor} borderColor={borderColor}>{tx.user}</Td>
                    <Td color={textColor} borderColor={borderColor}>{tx.amount} {tx.currency}</Td>
                    <Td borderColor={borderColor}>
                      <Badge
                        colorScheme={tx.type === 'deposit' ? 'green' : 'red'}
                        borderRadius="full"
                        px={2}
                      >
                        {tx.type}
                      </Badge>
                    </Td>
                    <Td color={textColor} borderColor={borderColor}>{new Date(tx.date).toLocaleDateString()}</Td>
                    <Td borderColor={borderColor}>
                      <Badge
                        colorScheme={
                          tx.status === 'approved' ? 'green' :
                          tx.status === 'pending' ? 'yellow' : 'red'
                        }
                        borderRadius="full"
                        px={2}
                      >
                        {tx.status}
                      </Badge>
                    </Td>
                    <Td borderColor={borderColor}>
                      <HStack spacing={2}>
                        <Button
                          size="sm"
                          colorScheme="blue"
                          onClick={() => navigate(`/admin/transaction/${tx.id}`)}
                        >
                          View
                        </Button>

                        {tx.status === 'pending' && (
                          <>
                            <Button
                              size="sm"
                              colorScheme="green"
                              onClick={() => handleApprove(tx.id)}
                            >
                              Approve
                            </Button>
                            <Button
                              size="sm"
                              colorScheme="red"
                              onClick={() => handleReject(tx.id)}
                            >
                              Reject
                            </Button>
                          </>
                        )}
                      </HStack>
                    </Td>
                  </Tr>
                ))
              ) : (
                <Tr>
                  <Td colSpan={7} textAlign="center" color={secondaryTextColor} borderColor={borderColor}>
                    {transactions.length === 0
                      ? "No transactions found. Create a deposit or withdrawal to get started."
                      : "No transactions found matching your search criteria."}
                  </Td>
                </Tr>
              )}
            </Tbody>
          </Table>
        </Box>
      </Box>
    </Box>
  );
};

export default AdminTransactions;
