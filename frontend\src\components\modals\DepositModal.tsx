import React, { useState, useRef, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON>verlay,
  ModalContent,
  <PERSON>dal<PERSON>eader,
  Modal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  HStack,
  Text,
  Box,
  Flex,
  Divider,
  useToast,
  Select,
  Textarea,
  Image,
  Badge,
  useColorModeValue,
  Icon,
  Tooltip,
  InputGroup,
  InputRightElement,
  Alert,
  AlertIcon,
  Progress,
  useClipboard,
  FormHelperText,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Spinner
} from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { FaClipboard, FaCheck, FaInfoCircle, FaUpload, FaTrash, FaExclamationTriangle } from 'react-icons/fa';
import useAuth from '../../hooks/useAuth';
import NetworkSelector from '../common/NetworkSelector';
import { CRYPTO_NETWORKS, getDefaultNetwork, NetworkOption } from '../../utils/cryptoNetworks';
import { investmentService } from '../../services/investmentService';
import { walletService } from '../../services/walletService';

// Crypto addresses - In a real app, these would come from your backend
const cryptoAddresses = {
  BTC: '**********************************',
  ETH: '******************************************',
  USDT: 'TKQvCcXdgmAQwu4U5RgQJdFfvGbmzXxC9X',
  BNB: 'bnb1jxfh2g85q3v0tdq56fnevx6xcxtcnhtsmcu64m',
  XRP: 'rHb9CJAWyB4rj91VRWn96DkukG4bwdtyTh'
};

interface DepositModalProps {
  isOpen: boolean;
  onClose: () => void;
  defaultAsset?: string;
  onSuccess?: () => void;
}

const DepositModal: React.FC<DepositModalProps> = ({ isOpen, onClose, defaultAsset, onSuccess }) => {
  const { t } = useTranslation();
  const toast = useToast();
  const { user } = useAuth();

  // State variables
  const [selectedCrypto, setSelectedCrypto] = useState(defaultAsset || 'BTC');
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [receipt, setReceipt] = useState<File | null>(null);
  const [receiptPreview, setReceiptPreview] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [activeStep, setActiveStep] = useState(0);
  const [selectedNetwork, setSelectedNetwork] = useState<string>('');
  const [networkOptions, setNetworkOptions] = useState<NetworkOption[]>([]);
  const [currentNetworkDetails, setCurrentNetworkDetails] = useState<NetworkOption | undefined>();
  const [depositAddress, setDepositAddress] = useState<string>('');
  const [isLoadingAddress, setIsLoadingAddress] = useState<boolean>(false);
  const [investmentId, setInvestmentId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Clipboard functionality
  const { hasCopied, onCopy } = useClipboard(depositAddress || cryptoAddresses[selectedCrypto as keyof typeof cryptoAddresses] || '');

  // Colors
  const bgColor = useColorModeValue('#FFFFFF', '#1E2329');
  const borderColor = useColorModeValue('#E2E8F0', '#2B3139');
  const primaryColor = '#F0B90B';
  const textColor = useColorModeValue('#1A202C', '#EAECEF');
  const secondaryTextColor = useColorModeValue('#4A5568', '#848E9C');

  // Calculate commission (1%) based on selected cryptocurrency
  const commission = amount ? parseFloat(amount) * 0.01 : 0;

  // Initialize network options when cryptocurrency changes
  useEffect(() => {
    const networks = CRYPTO_NETWORKS[selectedCrypto] || [];
    setNetworkOptions(networks);

    const defaultNetwork = getDefaultNetwork(selectedCrypto);
    if (defaultNetwork) {
      setSelectedNetwork(defaultNetwork.id);
      setCurrentNetworkDetails(defaultNetwork);
    }
  }, [selectedCrypto]);

  // Update current network details when selected network changes
  useEffect(() => {
    if (selectedNetwork && networkOptions.length > 0) {
      const networkDetails = networkOptions.find(network => network.id === selectedNetwork);
      setCurrentNetworkDetails(networkDetails);

      // Fetch deposit address when network is selected
      if (selectedNetwork && selectedCrypto) {
        fetchDepositAddress(selectedCrypto, selectedNetwork);
      }
    }
  }, [selectedNetwork, networkOptions, selectedCrypto]);

  // Fetch deposit address from API
  const fetchDepositAddress = async (currency: string, network: string) => {
    try {
      setIsLoadingAddress(true);
      const response = await investmentService.getDepositAddress(currency, network);

      if (response && response.data && response.data.address) {
        setDepositAddress(response.data.address);
      } else {
        // Fallback to hardcoded addresses
        setDepositAddress(cryptoAddresses[currency as keyof typeof cryptoAddresses] || '');
      }
    } catch (error) {
      console.error('Error fetching deposit address:', error);
      // Fallback to hardcoded addresses
      setDepositAddress(cryptoAddresses[currency as keyof typeof cryptoAddresses] || '');
    } finally {
      setIsLoadingAddress(false);
    }
  };

  // Handle network selection change
  const handleNetworkChange = (networkId: string) => {
    setSelectedNetwork(networkId);
  };

  // Handle file upload
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Check file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        toast({
          title: t('depositModal.fileTooLarge', 'File is too large'),
          description: t('depositModal.fileSizeLimit', 'Maximum file size should be 10MB.'),
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      // Check file type
      const validTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'];
      if (!validTypes.includes(file.type)) {
        toast({
          title: t('depositModal.invalidFileType', 'Invalid file type'),
          description: t('depositModal.supportedFormats', 'Supported formats: JPG, PNG, PDF'),
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      setReceipt(file);

      // Create preview for images
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          setReceiptPreview(e.target?.result as string);
        };
        reader.readAsDataURL(file);
      } else {
        // For PDFs, just show an icon or text
        setReceiptPreview(null);
      }
    }
  };

  // Remove uploaded file
  const handleRemoveFile = () => {
    setReceipt(null);
    setReceiptPreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };



  // Handle form submission
  const handleSubmit = async () => {
    // Validate form
    if (!amount || parseFloat(amount) <= 0) {
      toast({
        title: t('depositModal.invalidAmount', 'Invalid amount'),
        description: t('depositModal.enterValidAmount', 'Please enter a valid amount.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    if (!receipt) {
      toast({
        title: t('depositModal.receiptRequired', 'Receipt required'),
        description: t('depositModal.uploadReceipt', 'Please upload your transaction receipt.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    // Clear any previous errors
    setError(null);

    // Check if we have a valid investment ID
    if (!investmentId) {
      toast({
        title: 'Error',
        description: 'Investment not created. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    // Start submission process
    setIsSubmitting(true);
    setUploadProgress(0);

    try {
      // Create FormData for file upload
      const formData = new FormData();
      formData.append('receipt', receipt);

      // Add transaction details to the form data
      const txHash = 'tx_' + Date.now(); // In a real app, user would provide this
      console.log('Using transaction hash:', txHash);
      formData.append('txHash', txHash);
      formData.append('amount', amount);
      formData.append('currency', selectedCrypto);
      formData.append('network', selectedNetwork);

      if (description) {
        formData.append('description', description);
      }

      // Simulate upload progress
      let progress = 0;
      const interval = setInterval(() => {
        progress += 10;
        if (progress <= 90) {
          setUploadProgress(progress);
        }
      }, 200);

      try {
        // Upload receipt to the backend
        console.log('Uploading receipt to backend with investment ID:', investmentId);
        console.log('FormData contents:', Array.from(formData.entries()));

        // Add more detailed logging
        console.log('Receipt file details:', {
          name: receipt.name,
          type: receipt.type,
          size: receipt.size,
          lastModified: new Date(receipt.lastModified).toISOString()
        });

        const response = await investmentService.uploadReceipt(investmentId, formData);

        // Log the response for debugging
        console.log('Receipt upload response:', response);
        console.log('Receipt uploaded successfully to backend!');

        // If we have a transaction hash, update it in the backend
        if (txHash) {
          console.log('Updating transaction hash:', txHash);
          const txResponse = await investmentService.updateTransactionHash(
            investmentId,
            txHash
          );
          console.log('Transaction hash update response:', txResponse);
        }

        clearInterval(interval);
        setUploadProgress(100);

        // Show success message
        toast({
          title: t('depositModal.successTitle', 'Transaction Submitted'),
          description: t('depositModal.successDescription', 'Your investment request has been submitted and is pending admin approval. Once approved, you will start earning 1% daily interest.'),
          status: 'success',
          duration: 5000,
          isClosable: true,
        });

        // Call onSuccess callback if provided
        if (onSuccess) {
          onSuccess();
        }

        // Reset form and close modal
        setAmount('');
        setDescription('');
        setReceipt(null);
        setReceiptPreview(null);
        setActiveStep(0);
        setInvestmentId(null);
        onClose();
      } catch (uploadError: unknown) {
        clearInterval(interval);
        console.error('Error uploading receipt:', uploadError);

        // Try to continue anyway - the investment was created, so the admin might be able to handle it
        console.log('Got error uploading receipt but investment was created. Trying to continue...');

        // Show warning but continue
        toast({
          title: 'Warning',
          description: 'There was an issue uploading your receipt, but your investment was created. Please contact support.',
          status: 'warning',
          duration: 5000,
          isClosable: true,
        });

        // Reset form and close modal
        setAmount('');
        setDescription('');
        setReceipt(null);
        setReceiptPreview(null);
        setActiveStep(0);
        setInvestmentId(null);
        onClose();

        // Call onSuccess callback if provided
        if (onSuccess) {
          onSuccess();
        }

        return;
      }
    } catch (err: unknown) {
      console.error('Error uploading receipt:', err);

      // Type guard for error with response property
      interface ErrorWithResponse {
        response?: {
          data?: {
            message?: string;
          };
          status?: number;
        };
        request?: unknown;
        message?: string;
      }

      const errorWithResponse = err as ErrorWithResponse;
      const errorMessage = errorWithResponse.response?.data?.message || 'Failed to upload receipt';

      setError(errorMessage);

      toast({
        title: 'Error',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
      setUploadProgress(0);
    }
  };

  // Next step in the wizard
  const nextStep = async () => {
    // Clear any previous errors
    setError(null);

    if (activeStep === 0) {
      // Validate amount
      if (!amount || parseFloat(amount) <= 0) {
        toast({
          title: t('depositModal.invalidAmount', 'Invalid amount'),
          description: t('depositModal.enterValidAmount', 'Please enter a valid amount.'),
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      // Validate network selection
      if (!selectedNetwork) {
        toast({
          title: t('depositModal.networkRequired', 'Network Required'),
          description: t('depositModal.selectNetwork', 'Please select a network for your transaction.'),
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      try {
        setIsSubmitting(true);

        // Create investment in the backend
        console.log('Creating investment with data:', {
          currency: selectedCrypto,
          amount: parseFloat(amount),
          description: description || undefined,
          network: selectedNetwork
        });

        const response = await investmentService.createInvestment({
          currency: selectedCrypto,
          amount: parseFloat(amount),
          description: description || undefined,
          network: selectedNetwork
        });

        // Fetch deposit address for the selected network
        const addressResponse = await investmentService.getDepositAddress(selectedCrypto, selectedNetwork);

        // Log the full response for debugging
        console.log('Investment API response:', response);

        // Check different possible response structures
        let investmentId = null;

        // Safely extract the investment ID from the response
        // Define a type for the response object
        type InvestmentResponse = {
          data?: {
            investment?: {
              _id?: string;
              id?: string;
            };
            _id?: string;
            id?: string;
          };
          investment?: {
            _id?: string;
            id?: string;
          };
          _id?: string;
          id?: string;
        };

        // Cast response to our defined type
        const responseObj = response as InvestmentResponse;

        if (responseObj?.data?.investment?._id) {
          investmentId = responseObj.data.investment._id;
        }
        else if (responseObj?.data?.investment?.id) {
          investmentId = responseObj.data.investment.id;
        }
        else if (responseObj?.data?._id) {
          investmentId = responseObj.data._id;
        }
        else if (responseObj?.data?.id) {
          investmentId = responseObj.data.id;
        }
        else if (responseObj?.investment?._id) {
          investmentId = responseObj.investment._id;
        }
        else if (responseObj?.investment?.id) {
          investmentId = responseObj.investment.id;
        }
        else if (responseObj?._id) {
          investmentId = responseObj._id;
        }
        else if (responseObj?.id) {
          investmentId = responseObj.id;
        }

        if (investmentId) {
          console.log('Investment created successfully with ID:', investmentId);
          setInvestmentId(investmentId);

          // Log the address response for debugging
          console.log('Address API response:', addressResponse);

          // Try different possible structures for address response
          let depositAddr = null;

          // Define a type for the address response
          type AddressResponse = {
            data?: {
              address?: string;
              data?: {
                address?: string;
              }
            };
            address?: string;
          };

          // Cast addressResponse to our defined type
          const addressObj = addressResponse as AddressResponse;

          // Case 1: addressResponse.data.address
          if (addressObj?.data?.address) {
            depositAddr = addressObj.data.address;
          }
          // Case 2: addressResponse.data.data.address
          else if (addressObj?.data?.data?.address) {
            depositAddr = addressObj.data.data.address;
          }
          // Case 3: addressResponse.address
          else if (addressObj?.address) {
            depositAddr = addressObj.address;
          }

          if (depositAddr) {
            console.log('Got crypto address from API:', depositAddr);
            setDepositAddress(depositAddr);
          } else {
            // Fallback to hardcoded addresses
            console.log('Using fallback address');
            const fallbackAddress = cryptoAddresses[selectedCrypto as keyof typeof cryptoAddresses] || '';
            setDepositAddress(fallbackAddress);

            // Log the fallback address for debugging
            console.log('Using fallback address:', fallbackAddress);

            // Show a warning toast that we're using a fallback address
            toast({
              title: 'Using Default Address',
              description: 'Could not get a specific address from the server. Using a default address instead.',
              status: 'warning',
              duration: 5000,
              isClosable: true,
            });
          }

          // Move to the next step
          setActiveStep(activeStep + 1);
        } else {
          // Handle case where we couldn't find an investment ID
          console.error('Could not find investment ID in response:', response);
          throw new Error('Invalid response from server - could not find investment ID');
        }
      } catch (err: unknown) {
        console.error('Error creating investment:', err);

        // Improved error handling with more detailed messages
        let errorMessage = 'Failed to create investment';

        // Type guard for error with response property
        interface ErrorWithResponse {
          response?: {
            data?: {
              message?: string;
            };
            status?: number;
          };
          request?: unknown;
          message?: string;
        }

        const errorWithResponse = err as ErrorWithResponse;

        if (errorWithResponse.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          errorMessage = errorWithResponse.response.data?.message || `Server error: ${errorWithResponse.response.status}`;
          console.log('Response error data:', errorWithResponse.response.data);

          // Special case: if we got a response, the investment might have been created
          // Let's try to continue anyway
          console.log('Got error response but investment might have been created. Trying to continue...');

          // Set a default investment ID if we don't have one
          if (!investmentId) {
            const tempId = 'temp_' + Date.now();
            console.log('Setting temporary investment ID:', tempId);
            setInvestmentId(tempId);
          }

          // Set a default deposit address
          const fallbackAddress = cryptoAddresses[selectedCrypto as keyof typeof cryptoAddresses] || '';
          console.log('Setting fallback address:', fallbackAddress);
          setDepositAddress(fallbackAddress);

          // Show warning but continue
          toast({
            title: 'Warning',
            description: 'There was an issue with the server, but we\'ll try to continue. Your deposit might still work.',
            status: 'warning',
            duration: 5000,
            isClosable: true,
          });

          // Move to the next step anyway
          setActiveStep(activeStep + 1);
          return;
        } else if (errorWithResponse.request) {
          // The request was made but no response was received
          errorMessage = 'No response from server. Please check your internet connection.';
        } else {
          // Something happened in setting up the request that triggered an Error
          errorMessage = errorWithResponse.message || 'An unexpected error occurred';
        }

        setError(errorMessage);

        toast({
          title: 'Error',
          description: errorMessage,
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      } finally {
        setIsSubmitting(false);
      }
    }
    // If moving from step 2 to step 3
    else if (activeStep === 1) {
      // Ensure we have a valid investment ID
      if (!investmentId) {
        setError('Investment not created properly. Please try again.');
        toast({
          title: 'Error',
          description: 'Investment not created properly. Please try again.',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
      } else {
        // We have a valid investment ID, proceed to next step
        setActiveStep(activeStep + 1);
      }
    }
    // For other steps, just move forward
    else {
      setActiveStep(activeStep + 1);
    }
  };

  // Previous step in the wizard
  const prevStep = () => {
    setActiveStep(activeStep - 1);
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl" isCentered>
      <ModalOverlay backdropFilter="blur(5px)" />
      <ModalContent
        bg={bgColor}
        borderColor={borderColor}
        borderWidth="1px"
        mx={{ base: 2, md: "auto" }}
        maxH={{ base: "calc(100vh - 40px)", md: "calc(100vh - 80px)" }}
        overflowY="auto"
        height="auto"
      >
        <ModalHeader color={primaryColor} borderBottomWidth="1px" borderColor={borderColor}>
          {t('depositModal.title', 'Investment Transaction')}
        </ModalHeader>
        <ModalCloseButton color={textColor} />

        <ModalBody py={6}>
          <Tabs index={activeStep} onChange={setActiveStep} variant="enclosed" colorScheme="yellow">
            <TabList mb={4}>
              <Tab _selected={{ color: primaryColor, borderColor: primaryColor }}>
                {t('depositModal.step1', '1. Investment Details')}
              </Tab>
              <Tab _selected={{ color: primaryColor, borderColor: primaryColor }} isDisabled={activeStep < 1}>
                {t('depositModal.step2', '2. Crypto Address')}
              </Tab>
              <Tab _selected={{ color: primaryColor, borderColor: primaryColor }} isDisabled={activeStep < 2}>
                {t('depositModal.step3', '3. Receipt Upload')}
              </Tab>
            </TabList>

            <TabPanels>
              {/* Step 1: Investment Details */}
              <TabPanel>
                <VStack spacing={6} align="stretch">
                  {error && (
                    <Alert status="error" borderRadius="md">
                      <AlertIcon />
                      <Box>
                        <Text fontWeight="bold">Error</Text>
                        <Text fontSize="sm">{error}</Text>
                      </Box>
                    </Alert>
                  )}
                  <FormControl isRequired>
                    <FormLabel>{t('depositModal.cryptoCurrency', 'Cryptocurrency')}</FormLabel>
                    <Select
                      value={selectedCrypto}
                      onChange={(e) => setSelectedCrypto(e.target.value)}
                      bg={useColorModeValue('white', '#0B0E11')}
                      borderColor={borderColor}
                      color={textColor}
                    >
                      <option value="BTC">Bitcoin (BTC)</option>
                      <option value="ETH">Ethereum (ETH)</option>
                      <option value="USDT">Tether (USDT)</option>
                      <option value="BNB">Binance Coin (BNB)</option>
                      <option value="XRP">XRP</option>
                    </Select>
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel>{t('depositModal.amount', 'Investment Amount')}</FormLabel>
                    <InputGroup>
                      <Input
                        type="number"
                        value={amount}
                        onChange={(e) => setAmount(e.target.value)}
                        placeholder="0.00"
                        bg={useColorModeValue('white', '#0B0E11')}
                        borderColor={borderColor}
                        color={textColor}
                      />
                      <InputRightElement width="4.5rem">
                        <Text color={secondaryTextColor} fontSize="sm">{selectedCrypto}</Text>
                      </InputRightElement>
                    </InputGroup>
                  </FormControl>

                  {/* Network Selection */}
                  <NetworkSelector
                    networks={networkOptions}
                    selectedNetwork={selectedNetwork}
                    onChange={handleNetworkChange}
                    isRequired={true}
                    label={t('depositModal.network', 'Select Network')}
                    helperText={t('depositModal.networkHelperText', 'Choose the network for your transaction')}
                    currency={selectedCrypto}
                  />

                  <FormControl>
                    <FormLabel>{t('depositModal.description', 'Description (Optional)')}</FormLabel>
                    <Textarea
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      placeholder={t('depositModal.descriptionPlaceholder', 'Notes about your investment...')}
                      bg={useColorModeValue('white', '#0B0E11')}
                      borderColor={borderColor}
                      color={textColor}
                      rows={3}
                    />
                  </FormControl>

                  {/* Commission calculation */}
                  {amount && parseFloat(amount) > 0 && (
                    <Box
                      p={4}
                      bg={useColorModeValue('gray.50', 'rgba(240, 185, 11, 0.05)')}
                      borderRadius="md"
                      borderWidth="1px"
                      borderColor={useColorModeValue('gray.200', 'rgba(240, 185, 11, 0.2)')}
                    >
                      <Text fontWeight="bold" mb={2} color={textColor}>
                        {t('depositModal.commissionCalculation.title', 'Commission Calculation')}
                      </Text>
                      <HStack justify="space-between" mb={1}>
                        <Text color={secondaryTextColor}>{t('depositModal.commissionCalculation.investmentAmount', 'Investment Amount')}</Text>
                        <Text color={textColor}>{parseFloat(amount).toFixed(selectedCrypto === 'USDT' ? 2 : 8)} {selectedCrypto}</Text>
                      </HStack>
                      <HStack justify="space-between">
                        <Text color={secondaryTextColor}>{t('depositModal.commissionCalculation.commissionYouWillEarn', 'Commission (1%)')}</Text>
                        <Text color={primaryColor} fontWeight="bold">{commission.toFixed(selectedCrypto === 'USDT' ? 2 : 8)} {selectedCrypto}</Text>
                      </HStack>
                      <Divider my={2} />
                      <Text fontSize="xs" color={secondaryTextColor}>
                        {t('depositModal.commissionCalculation.commissionRate', '* Commission rate: 1% - Automatically added to your account after confirmation')}
                      </Text>
                    </Box>
                  )}
                </VStack>

                <Flex justify="flex-end" mt={6}>
                  <Button
                    colorScheme="yellow"
                    bg={primaryColor}
                    color="#0B0E11"
                    _hover={{ bg: "#F8D12F" }}
                    onClick={nextStep}
                    isLoading={isSubmitting}
                    loadingText={t('common.processing', 'Processing...')}
                    isDisabled={!amount || parseFloat(amount) <= 0 || !selectedNetwork || isSubmitting}
                  >
                    {t('common.next', 'Next')}
                  </Button>
                </Flex>
              </TabPanel>

              {/* Step 2: Crypto Address */}
              <TabPanel>
                <VStack spacing={6} align="stretch">
                  {error && (
                    <Alert status="error" borderRadius="md">
                      <AlertIcon />
                      <Box>
                        <Text fontWeight="bold">Error</Text>
                        <Text fontSize="sm">{error}</Text>
                      </Box>
                    </Alert>
                  )}
                  <Alert status="info" borderRadius="md">
                    <AlertIcon />
                    <Box>
                      <Text fontWeight="bold">{t('depositModal.transferInstructions', 'Transfer Instructions')}</Text>
                      <Text fontSize="sm">
                        {t('depositModal.transferInstructionsDetail', 'Please send exactly the amount you specified to the address below and then upload your receipt.')}
                      </Text>
                    </Box>
                  </Alert>

                  <Box
                    p={6}
                    bg={useColorModeValue('gray.50', '#0B0E11')}
                    borderRadius="md"
                    borderWidth="1px"
                    borderColor={borderColor}
                    textAlign="center"
                  >
                    <Text fontWeight="bold" mb={4} color={textColor}>
                      {t('depositModal.bitcoinAddress', `Make Payment to ${selectedCrypto} Address`)}
                    </Text>

                    <Box
                      p={4}
                      bg={useColorModeValue('white', '#1E2329')}
                      borderRadius="md"
                      borderWidth="1px"
                      borderColor={borderColor}
                      mb={4}
                      position="relative"
                      wordBreak="break-all"
                    >
                      {isLoadingAddress ? (
                        <Spinner size="sm" color={primaryColor} />
                      ) : (
                        <Text fontFamily="monospace" color={textColor}>
                          {depositAddress || cryptoAddresses[selectedCrypto as keyof typeof cryptoAddresses]}
                        </Text>
                      )}
                      <Button
                        position="absolute"
                        top="50%"
                        right="8px"
                        transform="translateY(-50%)"
                        size="sm"
                        onClick={onCopy}
                        leftIcon={hasCopied ? <FaCheck /> : <FaClipboard />}
                        colorScheme={hasCopied ? "green" : "gray"}
                        variant="ghost"
                      >
                        {hasCopied ? t('common.copied', 'Copied!') : t('depositModal.copyButton', 'Copy')}
                      </Button>
                    </Box>

                    <HStack justify="center" spacing={4}>
                      <Badge colorScheme="yellow" p={2} borderRadius="md">
                        {t('depositModal.amount', 'Amount')}: {parseFloat(amount).toFixed(selectedCrypto === 'USDT' ? 2 : 8)} {selectedCrypto}
                      </Badge>
                      <Badge colorScheme="green" p={2} borderRadius="md">
                        {t('depositModal.commission', 'Commission')}: {commission.toFixed(selectedCrypto === 'USDT' ? 2 : 8)} {selectedCrypto}
                      </Badge>
                    </HStack>
                  </Box>

                  <Box
                    p={4}
                    bg={useColorModeValue('yellow.50', 'rgba(240, 185, 11, 0.05)')}
                    borderRadius="md"
                    borderWidth="1px"
                    borderColor={useColorModeValue('yellow.200', 'rgba(240, 185, 11, 0.2)')}
                  >
                    <HStack align="flex-start">
                      <Icon as={FaExclamationTriangle} color={primaryColor} boxSize={5} mt={1} />
                      <Box>
                        <Text fontWeight="bold" color={textColor}>
                          {t('depositModal.important', 'Important')}
                        </Text>
                        <Text fontSize="sm" color={secondaryTextColor}>
                          {t('depositModal.warning', '* Please send exactly the amount you specified to this address and then upload your receipt.')}
                        </Text>
                        <Text fontSize="sm" color={secondaryTextColor} mt={1}>
                          {t('depositModal.warningDetail', '* Transfers made to the wrong address or over the wrong network cannot be recovered.')}
                        </Text>
                        {currentNetworkDetails && (
                          <Text fontSize="sm" color={secondaryTextColor} mt={2}>
                            <strong>{t('depositModal.selectedNetwork', 'Selected Network')}:</strong> {currentNetworkDetails.name}
                            ({t('depositModal.fee', 'Fee')}: {currentNetworkDetails.fee} {selectedCrypto})
                          </Text>
                        )}
                        {depositAddress && (
                          <Text fontSize="sm" color={secondaryTextColor} mt={2}>
                            <strong>{t('depositModal.walletAddress', 'Wallet Address')}:</strong> {depositAddress.substring(0, 10)}...{depositAddress.substring(depositAddress.length - 10)}
                          </Text>
                        )}
                      </Box>
                    </HStack>
                  </Box>
                </VStack>

                <Flex justify="space-between" mt={6}>
                  <Button variant="ghost" onClick={prevStep}>
                    {t('common.back', 'Back')}
                  </Button>
                  <Button
                    colorScheme="yellow"
                    bg={primaryColor}
                    color="#0B0E11"
                    _hover={{ bg: "#F8D12F" }}
                    onClick={nextStep}
                  >
                    {t('common.next', 'Next')}
                  </Button>
                </Flex>
              </TabPanel>

              {/* Step 3: Receipt Upload */}
              <TabPanel>
                <VStack spacing={6} align="stretch">
                  {error && (
                    <Alert status="error" borderRadius="md">
                      <AlertIcon />
                      <Box>
                        <Text fontWeight="bold">Error</Text>
                        <Text fontSize="sm">{error}</Text>
                      </Box>
                    </Alert>
                  )}
                  <FormControl isRequired>
                    <FormLabel>
                      {t('depositModal.uploadReceipt.title', 'Upload Receipt')}
                      <Tooltip
                        label={t('depositModal.uploadReceipt.tooltip', 'Upload your transaction receipt or screenshot')}
                        placement="top"
                      >
                        <Icon as={FaInfoCircle} ml={2} boxSize={4} color={secondaryTextColor} />
                      </Tooltip>
                    </FormLabel>

                    {!receipt ? (
                      <Box
                        borderWidth="2px"
                        borderRadius="md"
                        borderColor={borderColor}
                        borderStyle="dashed"
                        p={6}
                        textAlign="center"
                        bg={useColorModeValue('gray.50', '#0B0E11')}
                        cursor="pointer"
                        onClick={() => fileInputRef.current?.click()}
                        _hover={{ borderColor: primaryColor }}
                        transition="all 0.2s"
                      >
                        <Input
                          type="file"
                          accept="image/jpeg,image/png,image/jpg,application/pdf"
                          onChange={handleFileChange}
                          ref={fileInputRef}
                          display="none"
                        />
                        <Icon as={FaUpload} boxSize={8} color={secondaryTextColor} mb={4} />
                        <Text fontWeight="bold" color={textColor}>
                          {t('depositModal.dragAndDrop', 'Drag and drop file or click here')}
                        </Text>
                        <Text fontSize="sm" color={secondaryTextColor} mt={2}>
                          {t('depositModal.uploadReceipt.supportedFormats', 'Supported formats: JPG, PNG, PDF (Max. 10MB)')}
                        </Text>
                      </Box>
                    ) : (
                      <Box
                        borderWidth="1px"
                        borderRadius="md"
                        borderColor={borderColor}
                        p={4}
                        bg={useColorModeValue('white', '#0B0E11')}
                      >
                        <Flex justify="space-between" align="center">
                          <HStack>
                            {receiptPreview ? (
                              <Image
                                src={receiptPreview}
                                alt="Receipt preview"
                                boxSize="60px"
                                objectFit="cover"
                                borderRadius="md"
                              />
                            ) : (
                              <Box
                                bg={useColorModeValue('gray.100', '#1E2329')}
                                p={3}
                                borderRadius="md"
                              >
                                <Icon as={FaUpload} boxSize={5} color={secondaryTextColor} />
                              </Box>
                            )}
                            <Box>
                              <Text fontWeight="bold" color={textColor}>{receipt.name}</Text>
                              <Text fontSize="sm" color={secondaryTextColor}>
                                {(receipt.size / 1024 / 1024).toFixed(2)} MB
                              </Text>
                            </Box>
                          </HStack>
                          <Button
                            size="sm"
                            colorScheme="red"
                            variant="ghost"
                            onClick={handleRemoveFile}
                          >
                            <Icon as={FaTrash} />
                          </Button>
                        </Flex>
                      </Box>
                    )}
                    <FormHelperText>
                      {t('depositModal.uploadReceipt.helperText', 'The receipt you upload will be used to verify your transaction.')}
                    </FormHelperText>
                  </FormControl>

                  <Accordion allowToggle>
                    <AccordionItem borderColor={borderColor}>
                      <AccordionButton py={3}>
                        <Box flex="1" textAlign="left" fontWeight="bold" color={textColor}>
                          {t('depositModal.tips.title', 'Receipt Upload Tips')}
                        </Box>
                        <AccordionIcon />
                      </AccordionButton>
                      <AccordionPanel pb={4}>
                        <VStack align="start" spacing={2} color={secondaryTextColor}>
                          <Text>{t('depositModal.tips.tip1', '• Make sure the receipt image is clear and legible')}</Text>
                          <Text>{t('depositModal.tips.tip2', '• Transaction amount and crypto address should be visible')}</Text>
                          <Text>{t('depositModal.tips.tip3', '• Transaction hash should be visible')}</Text>
                          <Text>{t('depositModal.tips.tip4', '• Verification process is usually completed within 24 hours')}</Text>
                        </VStack>
                      </AccordionPanel>
                    </AccordionItem>
                  </Accordion>

                  <Box
                    p={4}
                    bg={useColorModeValue('blue.50', 'rgba(66, 153, 225, 0.1)')}
                    borderRadius="md"
                    borderWidth="1px"
                    borderColor={useColorModeValue('blue.200', 'rgba(66, 153, 225, 0.3)')}
                  >
                    <Text fontWeight="bold" mb={2} color={textColor}>
                      {t('depositModal.whyChooseUs.title', 'Why Choose Shipping Finance?')}
                    </Text>
                    <VStack align="start" spacing={1} color={secondaryTextColor}>
                      <Text>{t('depositModal.whyChooseUs.reason1', '• Highest commission rate in the market: 1%')}</Text>
                      <Text>{t('depositModal.whyChooseUs.reason2', '• 24/7 customer support and fast verification process')}</Text>
                      <Text>{t('depositModal.whyChooseUs.reason3', '• 100% secure and transparent transactions')}</Text>
                      <Text>{t('depositModal.whyChooseUs.reason4', '• 10,000+ satisfied users and growing community')}</Text>
                    </VStack>
                  </Box>
                </VStack>

                <Flex justify="space-between" mt={6}>
                  <Button variant="ghost" onClick={prevStep}>
                    {t('common.back', 'Back')}
                  </Button>
                  <Button
                    colorScheme="yellow"
                    bg={primaryColor}
                    color="#0B0E11"
                    _hover={{ bg: "#F8D12F" }}
                    onClick={handleSubmit}
                    isLoading={isSubmitting}
                    loadingText={t('common.processing', 'Processing...')}
                    isDisabled={!receipt}
                  >
                    {t('depositModal.submitButton', 'Submit Receipt and Earn')}
                  </Button>
                </Flex>

                {isSubmitting && (
                  <Box mt={4}>
                    <Text fontSize="sm" color={secondaryTextColor} mb={2}>
                      {t('depositModal.uploading', 'Uploading receipt...')}
                    </Text>
                    <Progress value={uploadProgress} size="sm" colorScheme="yellow" borderRadius="full" />
                  </Box>
                )}
              </TabPanel>
            </TabPanels>
          </Tabs>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default DepositModal;
