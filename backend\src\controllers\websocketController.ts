import { Request, Response } from 'express';
import { logger } from '../utils/logger';
import { catchAsync } from '../utils/errorHandler';

/**
 * Set up WebSocket/Socket.IO authentication using cookies
 * This endpoint confirms the user is authenticated and sets up necessary cookies
 * @param req Express request
 * @param res Express response
 */
export const getWebSocketToken = catchAsync(async (req: Request, res: Response) => {
  // Log request details for debugging
  logger.debug('Socket.IO authentication request received', {
    url: req.originalUrl,
    method: req.method,
    userId: req.user?._id,
    userEmail: req.user?.email,
    hasUser: !!req.user,
    cookies: Object.keys(req.cookies || {}),
    headers: {
      origin: req.headers.origin,
      referer: req.headers.referer,
      'user-agent': req.headers['user-agent']
    }
  });

  // User is already authenticated via the protect middleware
  if (!req.user || !req.user._id) {
    logger.warn('Socket.IO authentication rejected: No authenticated user');
    return res.status(401).json({
      status: 'fail',
      message: 'Not authenticated'
    });
  }

  // Generate a unique token for this session to prevent duplicate connections
  const timestamp = Date.now();
  const sessionId = `${req.user._id.toString()}_${timestamp}`;

  // Set a special cookie for Socket.IO authentication
  // This is just a flag to indicate the user has been authenticated for Socket.IO
  // The actual authentication will use the existing auth cookie (token)
  res.cookie('ws_auth', sessionId, {
    httpOnly: false, // Allow JavaScript access
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax', // Use 'none' in production for cross-site requests
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    path: '/'
  });

  // Log cookie settings for debugging
  logger.debug('Setting ws_auth cookie with options', {
    httpOnly: false,
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
    maxAge: 24 * 60 * 60 * 1000,
    path: '/',
    sessionId: sessionId
  });

  logger.info(`Socket.IO authentication successful for user ${req.user._id}`, {
    userId: req.user._id.toString(),
    email: req.user.email,
    sessionId: sessionId
  });

  // Return success response
  res.status(200).json({
    status: 'success',
    message: 'Socket.IO authentication successful',
    userId: req.user._id.toString(),
    sessionId: sessionId,
    timestamp: timestamp
  });
});
