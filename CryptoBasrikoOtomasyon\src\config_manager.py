import json
import os
import logging

class ConfigManager:
    """Konfigürasyon yönetimi sınıfı"""
    
    def __init__(self, config_file):
        self.config_file = config_file
        self.logger = logging.getLogger('ConfigManager')
        self.logger.info("ConfigManager başlatıldı")
    
    def load_settings(self):
        """Ayarları yükle"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    return json.load(f)
            else:
                # Varsayılan ayarlar
                default_settings = {
                    "api_key": "",
                    "api_secret": "",
                    "commission_rate": 0.01,  # %1
                    "interest_rates": {
                        "BTC": 0.03,  # %3
                        "ETH": 0.04,  # %4
                        "BNB": 0.05,  # %5
                        "SOL": 0.06,  # %6
                        "ADA": 0.07,  # %7
                        "USDT": 0.08,  # %8
                        "default": 0.05  # %5
                    },
                    "theme": "dark",
                    "language": "tr",
                    "auto_refresh": True,
                    "refresh_interval": 60,  # saniye
                    "default_currency": "USD"
                }
                
                # Klasör yoksa oluştur
                os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
                
                # Ayarları kaydet
                with open(self.config_file, 'w') as f:
                    json.dump(default_settings, f, indent=4)
                
                self.logger.info("Varsayılan ayarlar oluşturuldu")
                return default_settings
        except Exception as e:
            self.logger.error(f"Ayarlar yüklenirken hata: {str(e)}")
            # Hata durumunda minimum ayarlar
            return {
                "api_key": "",
                "api_secret": "",
                "commission_rate": 0.01,
                "interest_rates": {"default": 0.05},
                "theme": "dark",
                "language": "tr"
            }
    
    def save_settings(self, settings):
        """Ayarları kaydet"""
        try:
            # Klasör yoksa oluştur
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            with open(self.config_file, 'w') as f:
                json.dump(settings, f, indent=4)
            
            self.logger.info("Ayarlar kaydedildi")
            return True
        except Exception as e:
            self.logger.error(f"Ayarlar kaydedilirken hata: {str(e)}")
            return False
    
    def update_setting(self, key, value):
        """Belirli bir ayarı güncelle"""
        try:
            settings = self.load_settings()
            settings[key] = value
            return self.save_settings(settings)
        except Exception as e:
            self.logger.error(f"Ayar güncellenirken hata: {str(e)}")
            return False
