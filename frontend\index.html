<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <meta name="description" content="Shipping Finance - An innovative financial platform that offers investors stable and sustainable returns by leveraging opportunities in international trade" />
    <meta name="keywords" content="crypto, investment, bitcoin, ethereum, dogecoin, usdt, shipping, Finance, daily returns" />
    <meta name="theme-color" content="#0B0E11" />
    <title>Shipping Finance | Crypto Investment Platform</title>
    <link rel="manifest" href="/manifest.json" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />

    <!-- Preload critical assets -->
    <link rel="preload" href="/images/bitcoin-bg.jpg" as="image" />
    <link rel="preload" href="/assets/fonts/inter-var.woff2" as="font" type="font/woff2" crossorigin />

    <!-- Preconnect to API domain -->
    <link rel="preconnect" href="https://api.shippingFinance.com" />

    <!-- Google Fonts - Optimize with display=swap -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Source+Sans+Pro:wght@400;600&display=swap" rel="stylesheet">

    <!-- Critical CSS inline -->
    <style>
      html {
        font-size: 15px;
      }
      @media (max-width: 1600px) {
        html {
          font-size: 14px;
        }
      }
      @media (max-width: 768px) {
        html {
          font-size: 13px;
        }
      }
      body {
        font-family: 'Source Sans Pro', 'Segoe UI', sans-serif;
        line-height: 1.6;
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        letter-spacing: 0;
        margin: 0;
        padding: 0;
        background-color: #0B0E11;
        color: #EAECEF;
      }
      h1, h2, h3, h4, h5, h6 {
        font-family: 'Inter', 'Segoe UI', sans-serif;
        font-weight: 600;
        line-height: 1.3;
        letter-spacing: -0.01em;
      }
      .container {
        max-width: 1400px !important;
        width: 95% !important;
        margin: 0 auto;
        padding: 0 1.5rem;
      }
      /* Daha iyi okunabilirlik için ek stiller */
      p, li, span, label, input, textarea, button {
        font-size: 1rem;
      }
      button {
        font-weight: 600;
      }
      /* Mobil cihazlarda daha iyi okunabilirlik */
      @media (max-width: 480px) {
        p, li, span, label, input, textarea {
          font-size: 0.95rem;
          line-height: 1.5;
        }
      }

      /* Mobile touch optimization */
      @media (max-width: 767px) {
        /* Prevent zoom on input focus */
        input, textarea, select {
          font-size: 16px !important;
          transform: translateZ(0);
          -webkit-appearance: none;
        }

        /* Optimize touch targets */
        button, .chakra-button, [role="button"] {
          min-height: 44px !important;
          min-width: 44px !important;
          touch-action: manipulation;
          -webkit-tap-highlight-color: transparent;
        }

        /* Prevent text selection on UI elements */
        .crypto-card, .crypto-button {
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
          -webkit-touch-callout: none;
        }

        /* Smooth scrolling */
        * {
          -webkit-overflow-scrolling: touch;
        }

        /* Prevent horizontal scroll */
        body {
          overflow-x: hidden;
          position: relative;
        }

        /* iOS Safari specific optimizations */
        @supports (-webkit-touch-callout: none) {
          /* iOS Safari specific styles */
          input, textarea, select {
            font-size: 16px !important;
            border-radius: 0;
            -webkit-appearance: none;
            -webkit-border-radius: 0;
          }

          /* Prevent zoom on input focus in iOS */
          input:focus, textarea:focus, select:focus {
            font-size: 16px !important;
            transform: translateZ(0);
          }

          /* Fix iOS Safari viewport issues */
          body {
            -webkit-text-size-adjust: 100%;
            -webkit-overflow-scrolling: touch;
          }

          /* Optimize button interactions for iOS */
          button, .chakra-button {
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            cursor: pointer;
          }
        }

        /* Android Chrome specific optimizations */
        @media screen and (-webkit-min-device-pixel-ratio: 1) {
          /* Android specific styles */
          input, textarea, select {
            font-size: 16px !important;
            -webkit-appearance: none;
            appearance: none;
          }

          /* Optimize touch response for Android */
          button, .chakra-button {
            touch-action: manipulation;
            -webkit-tap-highlight-color: rgba(240, 185, 11, 0.2);
            tap-highlight-color: rgba(240, 185, 11, 0.2);
          }

          /* Fix Android viewport scaling */
          body {
            -webkit-text-size-adjust: 100%;
            text-size-adjust: 100%;
          }
        }
      }
      /* Initial loading spinner */
      #initial-loader {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        width: 100%;
        background-color: #0B0E11;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 9999;
      }
      .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(240, 185, 11, 0.2);
        border-radius: 50%;
        border-top-color: #F0B90B;
        animation: spin 1s ease-in-out infinite;
      }
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div id="initial-loader">
      <div class="spinner"></div>
    </div>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <script>
      // Remove initial loader when app is ready
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loader = document.getElementById('initial-loader');
          if (loader) {
            loader.style.opacity = '0';
            loader.style.transition = 'opacity 0.5s ease';
            setTimeout(function() {
              loader.style.display = 'none';
            }, 500);
          }
        }, 300);
      });

      // Prefetch important pages
      const prefetchLinks = [
        '/profile',
        '/login',
        '/register',
        '/about',
        '/contact'
      ];

      // Add prefetch links after page load
      window.addEventListener('load', function() {
        setTimeout(function() {
          prefetchLinks.forEach(function(url) {
            const link = document.createElement('link');
            link.rel = 'prefetch';
            link.href = url;
            document.head.appendChild(link);
          });
        }, 1000); // Wait 1 second after page load
      });

      // Check authentication status when page loads
      window.addEventListener('DOMContentLoaded', function() {
        // Function to check user authentication status - removed as endpoint no longer exists
        function checkAuthStatus() {
          console.log('Authentication check will be handled by the application');
          // The authentication check is now handled by the React application
          // This function is kept for backward compatibility but doesn't do anything
        }

        // Function to check admin authentication status
        function checkAdminStatus() {
          fetch('/api/admin/check-auth', {
            method: 'GET',
            credentials: 'include', // Include cookies
            headers: {
              'Content-Type': 'application/json',
              'Cache-Control': 'no-cache'
            }
          })
          .then(response => {
            console.log('Admin check response status:', response.status);
            return response.json();
          })
          .then(data => {
            if (data.isAdmin) {
              console.log('User is admin:', data.user);
              // Store admin token and update user data
              localStorage.setItem('adminToken', 'true');

              // Update user object with admin flag
              const user = JSON.parse(localStorage.getItem('user') || '{}');
              if (user && !user.isAdmin) {
                user.isAdmin = true;
                localStorage.setItem('user', JSON.stringify(user));
                console.log('Updated user object with admin flag');
              }
            } else {
              console.log('User is not admin');
              localStorage.removeItem('adminToken');

              // Update user object to remove admin flag if present
              const user = JSON.parse(localStorage.getItem('user') || '{}');
              if (user && user.isAdmin) {
                user.isAdmin = false;
                localStorage.setItem('user', JSON.stringify(user));
                console.log('Removed admin flag from user object');
              }
            }
          })
          .catch(error => {
            console.error('Error checking admin status:', error);
            localStorage.removeItem('adminToken');
          });
        }

        // Authentication check is now handled by the React application
        // Check admin status if user is logged in
        if (localStorage.getItem('user')) {
          checkAdminStatus();
        }
      });
    </script>
  </body>
</html>
