import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { routes, RouteDefinition, RouteType } from '../routes/RouteController';
import useAuth from '../hooks/useAuth';

interface RouteContextType {
  currentRoute: RouteDefinition | null;
  isLoading: boolean;
  previousRoute: string | null;
  routeHistory: string[];
  navigateTo: (path: string, options?: { replace?: boolean, state?: any }) => void;
  goBack: () => void;
  canAccessRoute: (path: string) => boolean;
  isPublicRoute: (path: string) => boolean;
  isProtectedRoute: (path: string) => boolean;
  isAdminRoute: (path: string) => boolean;
  getRouteByPath: (path: string) => RouteDefinition | undefined;
}

const RouteContext = createContext<RouteContextType | undefined>(undefined);

interface RouteProviderProps {
  children: ReactNode;
}

export const RouteProvider: React.FC<RouteProviderProps> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentRoute, setCurrentRoute] = useState<RouteDefinition | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [routeHistory, setRouteHistory] = useState<string[]>([]);
  const [previousRoute, setPreviousRoute] = useState<string | null>(null);

  // Update current route when location changes
  useEffect(() => {
    const findRoute = () => {
      const found = routes.find(route => {
        if (route.exact) {
          return route.path === location.pathname;
        }
        return location.pathname.startsWith(route.path);
      });
      
      setCurrentRoute(found || null);
    };

    findRoute();
  }, [location.pathname]);

  // Track route history
  useEffect(() => {
    setRouteHistory(prev => {
      // Don't add duplicate consecutive entries
      if (prev[prev.length - 1] === location.pathname) {
        return prev;
      }
      
      // Set previous route
      if (prev.length > 0) {
        setPreviousRoute(prev[prev.length - 1]);
      }
      
      // Keep only the last 10 routes in history
      const newHistory = [...prev, location.pathname];
      if (newHistory.length > 10) {
        return newHistory.slice(newHistory.length - 10);
      }
      
      return newHistory;
    });
  }, [location.pathname]);

  // Navigate to a route with permission check
  const navigateTo = (path: string, options?: { replace?: boolean, state?: any }) => {
    setIsLoading(true);
    
    // Find the route definition
    const route = routes.find(r => r.path === path);
    
    if (!route) {
      // If route not found, navigate to 404
      navigate('/not-found', { replace: true });
      setIsLoading(false);
      return;
    }
    
    // Check permissions
    if (route.type === RouteType.PROTECTED && !user) {
      // Save the intended destination for post-login redirect
      navigate('/login', { 
        replace: true, 
        state: { redirectAfterLogin: path } 
      });
      setIsLoading(false);
      return;
    }
    
    if (route.type === RouteType.ADMIN && (!user || !user.isAdmin)) {
      // Redirect non-admin users
      navigate(user ? '/dashboard' : '/login', { 
        replace: true,
        state: user ? undefined : { redirectAfterLogin: path }
      });
      setIsLoading(false);
      return;
    }
    
    // Navigate to the route
    navigate(path, options);
    setIsLoading(false);
  };

  // Go back to previous route
  const goBack = () => {
    if (previousRoute) {
      navigate(previousRoute);
    } else {
      navigate(-1);
    }
  };

  // Check if a route is accessible to the current user
  const canAccessRoute = (path: string): boolean => {
    const route = routes.find(r => r.path === path);
    
    if (!route) return false;
    
    if (route.type === RouteType.PUBLIC) return true;
    if (route.type === RouteType.PROTECTED) return !!user;
    if (route.type === RouteType.ADMIN) return !!user && !!user.isAdmin;
    
    return false;
  };

  // Check if a route is public
  const isPublicRoute = (path: string): boolean => {
    const route = routes.find(r => r.path === path);
    return route?.type === RouteType.PUBLIC;
  };

  // Check if a route is protected
  const isProtectedRoute = (path: string): boolean => {
    const route = routes.find(r => r.path === path);
    return route?.type === RouteType.PROTECTED;
  };

  // Check if a route is admin-only
  const isAdminRoute = (path: string): boolean => {
    const route = routes.find(r => r.path === path);
    return route?.type === RouteType.ADMIN;
  };

  // Get route by path
  const getRouteByPath = (path: string): RouteDefinition | undefined => {
    return routes.find(r => r.path === path);
  };

  const value = {
    currentRoute,
    isLoading,
    previousRoute,
    routeHistory,
    navigateTo,
    goBack,
    canAccessRoute,
    isPublicRoute,
    isProtectedRoute,
    isAdminRoute,
    getRouteByPath
  };

  return (
    <RouteContext.Provider value={value}>
      {children}
    </RouteContext.Provider>
  );
};

// Custom hook to use the route context
export const useRouteContext = (): RouteContextType => {
  const context = useContext(RouteContext);
  
  if (context === undefined) {
    throw new Error('useRouteContext must be used within a RouteProvider');
  }
  
  return context;
};

export default RouteContext;
