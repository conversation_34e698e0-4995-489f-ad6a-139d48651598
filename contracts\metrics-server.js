const express = require('express');
const { ethers } = require('ethers');
const promClient = require('prom-client');
const fs = require('fs');

const app = express();
const port = 9323;
const register = new promClient.Registry();

// Contract ABI
const contractABI = JSON.parse(fs.readFileSync('./CryptoYieldHub.json')).abi;

// Initialize metrics
const totalTransactionsCounter = new promClient.Counter({
  name: 'crypto_yield_total_transactions',
  help: 'Total number of transactions processed by the contract'
});

const totalValueLockedGauge = new promClient.Gauge({
  name: 'crypto_yield_total_value_locked',
  help: 'Total value locked in the contract (in wei)'
});

const assetBalanceGauge = new promClient.Gauge({
  name: 'crypto_yield_asset_balance',
  help: 'Balance of each asset in the contract',
  labelNames: ['asset']
});

const userCountGauge = new promClient.Gauge({
  name: 'crypto_yield_user_count',
  help: 'Number of unique users interacting with the contract'
});

const gasUsageHistogram = new promClient.Histogram({
  name: 'crypto_yield_gas_usage',
  help: 'Gas usage distribution for contract transactions',
  buckets: [50000, 100000, 200000, 500000, 1000000]
});

// Register metrics
register.registerMetric(totalTransactionsCounter);
register.registerMetric(totalValueLockedGauge);
register.registerMetric(assetBalanceGauge);
register.registerMetric(userCountGauge);
register.registerMetric(gasUsageHistogram);

// Initialize provider and contract
const provider = new ethers.providers.JsonRpcProvider(process.env.PROVIDER_URL);
const contract = new ethers.Contract(process.env.CONTRACT_ADDRESS, contractABI, provider);

// Update metrics
async function updateMetrics() {
  try {
    // Get total transactions (from events)
    const depositFilter = contract.filters.Deposit();
    const withdrawFilter = contract.filters.Withdrawal();
    const deposits = await contract.queryFilter(depositFilter);
    const withdrawals = await contract.queryFilter(withdrawFilter);
    totalTransactionsCounter.set(deposits.length + withdrawals.length);

    // Get TVL
    const tvl = await provider.getBalance(contract.address);
    totalValueLockedGauge.set(parseFloat(ethers.utils.formatEther(tvl)));

    // Get supported tokens and their balances
    const supportedTokens = await contract.getSupportedTokens();
    for (const token of supportedTokens) {
      const balance = await contract.getTokenBalance(token);
      assetBalanceGauge.set({ asset: token }, parseFloat(ethers.utils.formatEther(balance)));
    }

    // Get unique users (from events)
    const uniqueUsers = new Set();
    deposits.forEach(event => uniqueUsers.add(event.args.user));
    withdrawals.forEach(event => uniqueUsers.add(event.args.user));
    userCountGauge.set(uniqueUsers.size);

    console.log('Metrics updated successfully');
  } catch (error) {
    console.error('Error updating metrics:', error);
  }
}

// Event listeners for real-time metrics
contract.on('Deposit', async (user, token, amount, event) => {
  totalTransactionsCounter.inc();
  gasUsageHistogram.observe(event.gasUsed);
  await updateMetrics();
});

contract.on('Withdrawal', async (user, token, amount, event) => {
  totalTransactionsCounter.inc();
  gasUsageHistogram.observe(event.gasUsed);
  await updateMetrics();
});

// Initial metrics update
updateMetrics();

// Update metrics every 5 minutes
setInterval(updateMetrics, 5 * 60 * 1000);

// Metrics endpoint
app.get('/metrics', async (req, res) => {
  try {
    res.set('Content-Type', register.contentType);
    res.end(await register.metrics());
  } catch (error) {
    res.status(500).end(error.message);
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'healthy' });
});

// Start server
app.listen(port, '0.0.0.0', () => {
  console.log(`Contract metrics server listening at http://0.0.0.0:${port}`);
});