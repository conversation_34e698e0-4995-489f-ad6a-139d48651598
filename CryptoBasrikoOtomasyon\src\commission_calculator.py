import logging

class CommissionCalculator:
    """Komisyon hesaplayan sınıf"""
    
    def __init__(self, commission_rate=0.01):
        self.commission_rate = commission_rate
        self.logger = logging.getLogger('CommissionCalculator')
        self.logger.info(f"CommissionCalculator başlatıldı, oran: {commission_rate}")
    
    def calculate(self, amount):
        """Belirli bir miktar için komisyon hesapla"""
        try:
            commission = amount * self.commission_rate
            self.logger.info(f"Komisyon hesaplandı: {amount} * {self.commission_rate} = {commission}")
            return commission
        except Exception as e:
            self.logger.error(f"Komisyon hesaplanırken hata: {str(e)}")
            return 0
    
    def set_commission_rate(self, new_rate):
        """Komisyon oranını güncelle"""
        try:
            self.commission_rate = new_rate
            self.logger.info(f"Komisyon oranı güncellendi: {new_rate}")
            return True
        except Exception as e:
            self.logger.error(f"Komisyon oranı güncellenirken hata: {str(e)}")
            return False
