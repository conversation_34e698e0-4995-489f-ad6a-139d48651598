// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";

contract CryptoYieldHub is Initializable, PausableUpgradeable, OwnableUpgradeable, ReentrancyGuardUpgradeable {
    using SafeERC20 for IERC20;

    // Constants
    uint256 private constant BASIS_POINTS = 10000;
    uint256 private constant COMMISSION_RATE = 100; // 1%
    uint256 private constant INTEREST_PLATFORM_FEE = 2000; // 20%
    uint256 private constant MIN_DEPOSIT = 100; // Minimum deposit in token's smallest unit
    uint256 private constant MAX_DEPOSIT = 1000000; // Maximum deposit in token's smallest unit

    // Yield modes
    enum YieldMode { COMMISSION, INTEREST }

    // User asset structure
    struct UserAsset {
        uint256 balance;
        uint256 commissionBalance;
        uint256 interestBalance;
        uint256 lastInterestCalculationTime;
        YieldMode mode;
    }

    // Events
    event Deposit(address indexed user, address indexed token, uint256 amount);
    event Withdrawal(address indexed user, address indexed token, uint256 amount);
    event CommissionEarned(address indexed user, address indexed token, uint256 amount);
    event InterestEarned(address indexed user, address indexed token, uint256 amount);
    event EmergencyWithdraw(address indexed token, uint256 amount);
    event TokenAdded(address indexed token);
    event TokenRemoved(address indexed token);
    event AnnualInterestRateUpdated(uint256 newRate);

    // State variables
    mapping(address => mapping(address => UserAsset)) private userAssets;
    mapping(address => bool) public supportedTokens;
    uint256 public annualInterestRate;
    bool private _initialized;
    
    // Circuit breaker
    bool public emergencyStop;

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    function initialize() public initializer {
        __Pausable_init();
        __Ownable_init();
        __ReentrancyGuard_init();
        annualInterestRate = 500; // 5% annual interest rate
    }

    // Modifiers
    modifier tokenSupported(address token) {
        require(supportedTokens[token], "Token not supported");
        _;
    }

    modifier validAmount(uint256 amount) {
        require(amount >= MIN_DEPOSIT && amount <= MAX_DEPOSIT, "Invalid amount");
        _;
    }

    modifier notEmergency() {
        require(!emergencyStop, "Contract is in emergency stop");
        _;
    }

    // Admin functions
    function addSupportedToken(address token) external onlyOwner {
        require(token != address(0), "Invalid token address");
        require(!supportedTokens[token], "Token already supported");
        supportedTokens[token] = true;
        emit TokenAdded(token);
    }

    function removeSupportedToken(address token) external onlyOwner {
        require(supportedTokens[token], "Token not supported");
        supportedTokens[token] = false;
        emit TokenRemoved(token);
    }

    function setAnnualInterestRate(uint256 newRate) external onlyOwner {
        require(newRate <= 3000, "Interest rate too high"); // Max 30%
        annualInterestRate = newRate;
        emit AnnualInterestRateUpdated(newRate);
    }

    function toggleEmergencyStop() external onlyOwner {
        emergencyStop = !emergencyStop;
    }

    function pause() external onlyOwner {
        _pause();
    }

    function unpause() external onlyOwner {
        _unpause();
    }

    // Main functions
    function deposit(address token, uint256 amount) 
        external 
        nonReentrant 
        whenNotPaused 
        notEmergency 
        tokenSupported(token) 
        validAmount(amount) 
    {
        require(amount > 0, "Amount must be greater than 0");
        
        UserAsset storage asset = userAssets[msg.sender][token];
        
        // Transfer tokens to contract
        IERC20(token).safeTransferFrom(msg.sender, address(this), amount);
        
        // Update balance
        asset.balance += amount;
        
        // Calculate and add commission if in COMMISSION mode
        if (asset.mode == YieldMode.COMMISSION) {
            uint256 commissionAmount = (amount * COMMISSION_RATE) / BASIS_POINTS;
            asset.commissionBalance += commissionAmount;
            emit CommissionEarned(msg.sender, token, commissionAmount);
        }
        
        // Update last interest calculation time
        asset.lastInterestCalculationTime = block.timestamp;
        
        emit Deposit(msg.sender, token, amount);
    }

    function withdraw(address token, uint256 amount) 
        external 
        nonReentrant 
        whenNotPaused 
        tokenSupported(token) 
    {
        UserAsset storage asset = userAssets[msg.sender][token];
        require(amount > 0 && amount <= asset.balance, "Invalid withdrawal amount");
        
        // If in INTEREST mode, calculate pending interest first
        if (asset.mode == YieldMode.INTEREST) {
            _calculateAndAddInterest(msg.sender, token);
        }
        
        // Update balance
        asset.balance -= amount;
        
        // Transfer tokens to user
        IERC20(token).safeTransfer(msg.sender, amount);
        
        emit Withdrawal(msg.sender, token, amount);
    }

    function withdrawCommission(address token) 
        external 
        nonReentrant 
        whenNotPaused 
        tokenSupported(token) 
    {
        UserAsset storage asset = userAssets[msg.sender][token];
        uint256 commissionAmount = asset.commissionBalance;
        
        require(commissionAmount > 0, "No commission to withdraw");
        
        // Reset commission balance
        asset.commissionBalance = 0;
        
        // Transfer commission to user
        IERC20(token).safeTransfer(msg.sender, commissionAmount);
        
        emit Withdrawal(msg.sender, token, commissionAmount);
    }

    function withdrawInterest(address token) 
        external 
        nonReentrant 
        whenNotPaused 
        tokenSupported(token) 
    {
        UserAsset storage asset = userAssets[msg.sender][token];
        
        // Calculate latest interest
        if (asset.mode == YieldMode.INTEREST) {
            _calculateAndAddInterest(msg.sender, token);
        }
        
        uint256 interestAmount = asset.interestBalance;
        require(interestAmount > 0, "No interest to withdraw");
        
        // Reset interest balance
        asset.interestBalance = 0;
        
        // Transfer interest to user
        IERC20(token).safeTransfer(msg.sender, interestAmount);
        
        emit Withdrawal(msg.sender, token, interestAmount);
    }

    function toggleMode(address token) 
        external 
        nonReentrant 
        whenNotPaused 
        tokenSupported(token) 
    {
        UserAsset storage asset = userAssets[msg.sender][token];
        
        // If switching from INTEREST to COMMISSION, calculate pending interest
        if (asset.mode == YieldMode.INTEREST && asset.balance > 0) {
            _calculateAndAddInterest(msg.sender, token);
        }
        
        // Toggle mode
        asset.mode = asset.mode == YieldMode.COMMISSION ? YieldMode.INTEREST : YieldMode.COMMISSION;
        
        // Update last interest calculation time if switching to INTEREST
        if (asset.mode == YieldMode.INTEREST) {
            asset.lastInterestCalculationTime = block.timestamp;
        }
    }

    // View functions
    function getUserAsset(address user, address token) 
        external 
        view 
        returns (
            uint256 balance,
            uint256 commissionBalance,
            uint256 interestBalance,
            YieldMode mode,
            uint256 pendingInterest
        ) 
    {
        UserAsset storage asset = userAssets[user][token];
        
        balance = asset.balance;
        commissionBalance = asset.commissionBalance;
        interestBalance = asset.interestBalance;
        mode = asset.mode;
        
        // Calculate pending interest if in INTEREST mode
        if (mode == YieldMode.INTEREST && balance > 0) {
            pendingInterest = _calculatePendingInterest(user, token);
        }
    }

    // Internal functions
    function _calculateAndAddInterest(address user, address token) internal {
        UserAsset storage asset = userAssets[user][token];
        
        if (asset.balance == 0 || asset.mode != YieldMode.INTEREST) {
            return;
        }
        
        uint256 pendingInterest = _calculatePendingInterest(user, token);
        if (pendingInterest > 0) {
            asset.interestBalance += pendingInterest;
            asset.lastInterestCalculationTime = block.timestamp;
            emit InterestEarned(user, token, pendingInterest);
        }
    }

    function _calculatePendingInterest(address user, address token) internal view returns (uint256) {
        UserAsset storage asset = userAssets[user][token];
        
        uint256 timeElapsed = block.timestamp - asset.lastInterestCalculationTime;
        if (timeElapsed == 0) return 0;
        
        uint256 interest = (asset.balance * annualInterestRate * timeElapsed) / (BASIS_POINTS * 365 days);
        
        // Calculate user's share after platform fee
        return (interest * (BASIS_POINTS - INTEREST_PLATFORM_FEE)) / BASIS_POINTS;
    }

    // Emergency functions
    function emergencyWithdraw(address token) external onlyOwner {
        require(emergencyStop, "Emergency stop not activated");
        uint256 balance = IERC20(token).balanceOf(address(this));
        if (balance > 0) {
            IERC20(token).safeTransfer(owner(), balance);
            emit EmergencyWithdraw(token, balance);
        }
    }
}
