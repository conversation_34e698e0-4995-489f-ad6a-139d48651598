{"login": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Hesabınıza giriş yapın", "email": "E-posta", "password": "Şifre", "rememberMe": "<PERSON><PERSON>", "forgotPassword": "<PERSON><PERSON><PERSON><PERSON> unuttum", "loginButton": "<PERSON><PERSON><PERSON>", "noAccount": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z yok mu?", "registerLink": "<PERSON><PERSON><PERSON> o<PERSON>n", "welcomeBack": "<PERSON><PERSON>r hoş geldiniz!", "loginSuccess": "Başarıyla giriş yaptı<PERSON>ız", "loginError": "<PERSON><PERSON><PERSON> başar<PERSON>s<PERSON><PERSON>", "invalidCredentials": "E-posta veya şifre hatalı", "accountLocked": "Hesabınız kilitlenmiş", "accountNotVerified": "Hesabınız doğrulanmamış", "tooManyAttempts": "Çok fazla başarısız deneme", "sessionExpired": "Oturumunuzun süresi doldu", "loginRequired": "Bu sayfaya erişmek için giriş yapmalısınız"}, "register": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON>", "firstName": "Ad", "lastName": "Soyad", "email": "E-posta", "password": "Şifre", "confirmPassword": "Şifre Tekrarı", "phone": "Telefon", "country": "<PERSON><PERSON><PERSON>", "city": "Şehir", "referralCode": "<PERSON><PERSON><PERSON> (Opsiyonel)", "agreeTerms": "Kullanım şartlarını kabul ediyorum", "agreePrivacy": "Gizlilik politikasını kabul ediyorum", "marketingConsent": "Pazarlama e-postalarını almayı kabul ediyorum", "registerButton": "<PERSON><PERSON><PERSON>", "haveAccount": "Zaten hesabınız var mı?", "loginLink": "<PERSON><PERSON><PERSON>", "registrationSuccess": "Kayıt başarılı! E-postanızı kontrol edin", "registrationError": "<PERSON><PERSON><PERSON> başarısız", "emailExists": "Bu e-posta adresi zaten kullanımda", "weakPassword": "Şifre çok zayıf", "invalidEmail": "Geçersiz e-posta adresi", "termsRequired": "Kullanım şartlarını kabul etmelisiniz", "privacyRequired": "Gizlilik politikasını kabul etmelisiniz"}, "forgotPassword": {"title": "Şif<PERSON><PERSON>", "subtitle": "Şifre sıfırlama bağlantısı göndereceğiz", "email": "E-posta", "sendButton": "<PERSON><PERSON><PERSON>", "backToLogin": "<PERSON><PERSON><PERSON>", "emailSent": "Şifre sıfırlama bağlantısı e-postanıza gönderildi", "emailNotFound": "Bu e-posta adresi bulunamadı", "rateLimited": "Çok fazla istek. Lütfen bekleyin"}, "resetPassword": {"title": "Şifre <PERSON>ırla", "subtitle": "<PERSON><PERSON>", "newPassword": "<PERSON><PERSON>", "confirmPassword": "Şifre Tekrarı", "resetButton": "Şifreyi <PERSON>", "resetSuccess": "Şifreniz başarıyla sıfırlandı", "resetError": "Şifre sıfırlama başarısız", "invalidToken": "Geçersiz veya süresi dolmuş bağlantı", "passwordMismatch": "<PERSON><PERSON><PERSON><PERSON> eşleşmiyor"}, "verification": {"title": "E-posta Doğrulama", "subtitle": "E-postanıza gönderilen kodu girin", "code": "Doğrulama <PERSON>du", "verifyButton": "<PERSON><PERSON><PERSON><PERSON>", "resendCode": "<PERSON><PERSON> tekrar g<PERSON>", "verificationSuccess": "E-posta başarıyla doğrulandı", "verificationError": "Doğrulama başarısız", "invalidCode": "Geçersiz doğrulama kodu", "expiredCode": "Doğrulama kodunun süresi dolmuş", "codeSent": "Doğrulama kodu e-postanıza gönderildi"}, "logout": {"title": "Çıkış Yap", "message": "<PERSON><PERSON><PERSON><PERSON><PERSON> yapmak istediğinizden emin misiniz?", "confirmButton": "Çıkış Yap", "cancelButton": "İptal", "logoutSuccess": "Başarıyla çıkış yaptınız", "logoutError": "Çıkış yapılırken hata oluştu"}, "twoFactor": {"title": "İki Faktörlü Doğrulama", "subtitle": "Güvenlik kodunuzu girin", "code": "Güvenlik Kodu", "verifyButton": "<PERSON><PERSON><PERSON><PERSON>", "backupCode": "<PERSON><PERSON> kod kullan", "trustDevice": "<PERSON><PERSON> cihaza g<PERSON>", "verificationSuccess": "İki faktörlü doğrulama başarılı", "verificationError": "Doğrulama başarısız", "invalidCode": "Geçersiz güvenlik kodu", "setup": {"title": "İki Faktörlü Doğrulama Kurulumu", "step1": "Authenticator uygulamasını indirin", "step2": "QR kodunu tarayın", "step3": "<PERSON><PERSON><PERSON><PERSON>a kodunu girin", "qrCode": "QR Kod", "manualEntry": "<PERSON>", "secretKey": "<PERSON><PERSON><PERSON>", "verificationCode": "Doğrulama <PERSON>du", "enableButton": "Etkinleştir", "setupSuccess": "İki faktörlü doğrulama etkinleştirildi", "setupError": "<PERSON><PERSON><PERSON> başarısız"}}}