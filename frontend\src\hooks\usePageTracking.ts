import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { routes, RouteType } from '../routes/RouteController';

interface PageViewData {
  path: string;
  title: string;
  timestamp: number;
  referrer: string;
  routeType: RouteType | null;
  isAuthenticated: boolean;
  isAdmin: boolean;
}

/**
 * Custom hook for tracking page views and user navigation
 * 
 * Features:
 * - Tracks page views with detailed metadata
 * - Supports integration with analytics services
 * - Records navigation patterns for optimization
 * - Respects user privacy settings
 * 
 * @param options - Tracking options
 */
const usePageTracking = (options: { 
  enabled?: boolean;
  trackTiming?: boolean;
  trackReferrer?: boolean;
} = {}): void => {
  const location = useLocation();
  const { 
    enabled = true, 
    trackTiming = true,
    trackReferrer = true
  } = options;
  
  useEffect(() => {
    // Skip tracking if disabled
    if (!enabled) return;
    
    // Find the current route definition
    const currentRoute = routes.find(route => {
      if (route.exact) {
        return route.path === location.pathname;
      }
      return location.pathname.startsWith(route.path);
    });
    
    // Get user authentication status from localStorage
    const userString = localStorage.getItem('user');
    const user = userString ? JSON.parse(userString) : null;
    const isAuthenticated = !!user;
    const isAdmin = user?.isAdmin || false;
    
    // Create page view data
    const pageViewData: PageViewData = {
      path: location.pathname + location.search + location.hash,
      title: document.title,
      timestamp: Date.now(),
      referrer: trackReferrer ? document.referrer : '',
      routeType: currentRoute?.type || null,
      isAuthenticated,
      isAdmin
    };
    
    // Log page view to console (in development)
    if (process.env.NODE_ENV === 'development') {
      console.log('Page View:', pageViewData);
    }
    
    // Track page view timing if enabled
    if (trackTiming) {
      // Record page load time
      const pageLoadTime = performance.now();
      
      // Log timing data
      console.log(`Page loaded in ${pageLoadTime.toFixed(2)}ms`);
      
      // You would typically send this data to your analytics service
      // analyticsService.trackTiming('page_load', pageLoadTime);
    }
    
    // Here you would typically send the page view data to your analytics service
    // For example:
    // 
    // if (typeof window.gtag === 'function') {
    //   window.gtag('config', 'G-XXXXXXXXXX', {
    //     page_path: pageViewData.path,
    //     page_title: pageViewData.title
    //   });
    // }
    // 
    // Or for a custom analytics service:
    // analyticsService.trackPageView(pageViewData);
    
  }, [location, enabled, trackTiming, trackReferrer]);
};

export default usePageTracking;
