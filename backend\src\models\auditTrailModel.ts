import mongoose, { Document, Schema } from 'mongoose';

export interface IAuditTrail extends Document {
  timestamp: Date;
  userId: mongoose.Types.ObjectId;
  action: string;
  packageId?: mongoose.Types.ObjectId;
  amount?: number;
  currency?: string;
  hash: string;
  ipAddress?: string;
  userAgent?: string;
  details: any;
  status: 'success' | 'failed' | 'pending';
}

const auditTrailSchema = new Schema<IAuditTrail>(
  {
    timestamp: {
      type: Date,
      default: Date.now,
      index: true
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
      index: true
    },
    action: {
      type: String,
      required: [true, 'Action is required'],
      enum: [
        'INVESTMENT_CREATED',
        'INVESTMENT_ACTIVATED',
        'INTEREST_CALCULATED',
        'WITHDRAWAL_REQUESTED',
        'WITHDRAWAL_COMPLETED',
        'EMERGENCY_WITHDRAWAL',
        'PACKAGE_COMPLETED',
        'COMPOUND_ENABLED',
        'COMPOUND_DISABLED',
        'RATE_UPDATED'
      ],
      index: true
    },
    packageId: {
      type: Schema.Types.ObjectId,
      ref: 'InvestmentPackage',
      default: null,
      index: true
    },
    amount: {
      type: Number,
      default: null,
      min: 0
    },
    currency: {
      type: String,
      uppercase: true,
      default: null
    },
    hash: {
      type: String,
      required: [true, 'Hash is required'],
      unique: true,
      index: true
    },
    ipAddress: {
      type: String,
      default: null,
      validate: {
        validator: function(value: string) {
          if (!value) return true;
          // Basic IP validation
          const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
          return ipRegex.test(value);
        },
        message: 'Invalid IP address format'
      }
    },
    userAgent: {
      type: String,
      default: null,
      maxlength: 500
    },
    details: {
      type: Schema.Types.Mixed,
      default: {}
    },
    status: {
      type: String,
      enum: ['success', 'failed', 'pending'],
      default: 'success',
      index: true
    }
  },
  {
    timestamps: false, // We use custom timestamp
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Indexes for performance
auditTrailSchema.index({ timestamp: -1 });
auditTrailSchema.index({ userId: 1, timestamp: -1 });
auditTrailSchema.index({ action: 1, timestamp: -1 });
auditTrailSchema.index({ packageId: 1, timestamp: -1 });
auditTrailSchema.index({ hash: 1 }, { unique: true });

// Pre-save middleware to generate hash
auditTrailSchema.pre('save', function(next) {
  if (!this.hash) {
    const crypto = require('crypto');
    const hashData = `${this.timestamp}-${this.userId}-${this.action}-${this.packageId || ''}-${this.amount || 0}`;
    this.hash = crypto.createHash('sha256').update(hashData).digest('hex');
  }
  next();
});

// Static methods interface
interface IAuditTrailModel extends mongoose.Model<IAuditTrail> {
  createAuditLog(data: Partial<IAuditTrail>): Promise<IAuditTrail>;
  getUserAuditTrail(userId: string, limit?: number): Promise<IAuditTrail[]>;
  getPackageAuditTrail(packageId: string): Promise<IAuditTrail[]>;
  getSystemAuditTrail(startDate?: Date, endDate?: Date, action?: string, limit?: number): Promise<IAuditTrail[]>;
}

// Static methods
auditTrailSchema.statics.createAuditLog = function(data: Partial<IAuditTrail>) {
  const auditLog = new this({
    timestamp: new Date(),
    ...data
  });
  return auditLog.save();
};

auditTrailSchema.statics.getUserAuditTrail = function(userId: string, limit: number = 50) {
  return this.find({ userId })
    .sort({ timestamp: -1 })
    .limit(limit)
    .populate('packageId', 'amount currency status')
    .populate('userId', 'email firstName lastName');
};

auditTrailSchema.statics.getPackageAuditTrail = function(packageId: string) {
  return this.find({ packageId })
    .sort({ timestamp: -1 })
    .populate('userId', 'email firstName lastName');
};

auditTrailSchema.statics.getSystemAuditTrail = function(
  startDate?: Date,
  endDate?: Date,
  action?: string,
  limit: number = 100
) {
  const query: any = {};

  if (startDate || endDate) {
    query.timestamp = {};
    if (startDate) query.timestamp.$gte = startDate;
    if (endDate) query.timestamp.$lte = endDate;
  }

  if (action) {
    query.action = action;
  }

  return this.find(query)
    .sort({ timestamp: -1 })
    .limit(limit)
    .populate('userId', 'email firstName lastName')
    .populate('packageId', 'amount currency status');
};

// Virtual fields
auditTrailSchema.virtual('turkeyTime').get(function() {
  return new Date(this.timestamp.getTime() + (3 * 60 * 60 * 1000));
});

auditTrailSchema.virtual('formattedAction').get(function() {
  const actionMap: { [key: string]: string } = {
    'INVESTMENT_CREATED': 'Yatırım Oluşturuldu',
    'INVESTMENT_ACTIVATED': 'Yatırım Aktifleştirildi',
    'INTEREST_CALCULATED': 'Faiz Hesaplandı',
    'WITHDRAWAL_REQUESTED': 'Çekim Talep Edildi',
    'WITHDRAWAL_COMPLETED': 'Çekim Tamamlandı',
    'EMERGENCY_WITHDRAWAL': 'Acil Çekim',
    'PACKAGE_COMPLETED': 'Paket Tamamlandı',
    'COMPOUND_ENABLED': 'Bileşik Faiz Aktif',
    'COMPOUND_DISABLED': 'Bileşik Faiz Pasif',
    'RATE_UPDATED': 'Oran Güncellendi'
  };

  return actionMap[this.action] || this.action;
});

// Instance methods
auditTrailSchema.methods.verify = function() {
  const crypto = require('crypto');
  const hashData = `${this.timestamp}-${this.userId}-${this.action}-${this.packageId || ''}-${this.amount || 0}`;
  const calculatedHash = crypto.createHash('sha256').update(hashData).digest('hex');

  return this.hash === calculatedHash;
};

const AuditTrail = mongoose.model<IAuditTrail, IAuditTrailModel>('AuditTrail', auditTrailSchema);

export default AuditTrail;
