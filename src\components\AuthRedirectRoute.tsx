import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import useAuth from '../hooks/useAuth';
import { useToast } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';

interface AuthRedirectRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
}

/**
 * AuthRedirectRoute component
 * 
 * This component redirects authenticated users away from routes like login and register
 * If the user is already logged in, they will be redirected to the specified path (default: '/')
 */
const AuthRedirectRoute: React.FC<AuthRedirectRouteProps> = ({ 
  children, 
  redirectTo = '/' 
}) => {
  const { user, loading } = useAuth();
  const location = useLocation();
  const toast = useToast();
  const { t } = useTranslation();

  useEffect(() => {
    if (user && !loading) {
      toast({
        title: t('login.alreadyLoggedIn', 'Already Logged In'),
        description: t('login.redirectingToHome', 'You are already logged in. Redirecting...'),
        status: 'info',
        duration: 3000,
        isClosable: true,
      });
    }
  }, [user, loading, toast, t]);

  // If the user is authenticated, redirect to the specified path
  if (user && !loading) {
    return <Navigate to={redirectTo} replace />;
  }

  // Otherwise, render the children
  return <>{children}</>;
};

export default AuthRedirectRoute;
