import React, { useState, useEffect } from 'react';
import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Text,
  Badge,
  Flex,
  Spinner,
  useToast,
  useColorModeValue,
  Button,
  HStack,
  VStack,
  Heading,
  Card,
  CardBody,
  Select,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Icon,
  Divider,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription
} from '@chakra-ui/react';
import Pagination from '../common/Pagination';
import { FaCoins, FaExchangeAlt, FaFilter } from 'react-icons/fa';
import { userService } from '../../services/api';
import { formatDate, formatCurrency } from '../../utils/formatters';
import { useTranslation } from 'react-i18next';

interface Commission {
  _id: string;
  amount: number;
  currency: string;
  status: string;
  createdAt: string;
  referredUser: {
    firstName: string;
    lastName: string;
    email: string;
  };
  investment?: {
    amount: number;
    currency: string;
    status: string;
  };
}

interface CommissionResponse {
  commissions: Commission[];
  totals: { _id: string; total: number }[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

const ReferralCommissionHistory: React.FC = () => {
  const [commissions, setCommissions] = useState<Commission[]>([]);
  const [totals, setTotals] = useState<{ currency: string; total: number }[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [currencyFilter, setCurrencyFilter] = useState<string>('');
  const toast = useToast();
  const { t } = useTranslation();

  // Colors
  const bgColor = useColorModeValue('white', '#1E2329');
  const cardBgColor = useColorModeValue('gray.50', '#0B0E11');
  const borderColor = useColorModeValue('gray.200', '#2B3139');
  const textColor = useColorModeValue('gray.800', '#EAECEF');
  const secondaryTextColor = useColorModeValue('gray.600', '#848E9C');
  const accentColor = '#F0B90B';

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    fetchCommissions(1);
  }, [statusFilter, currencyFilter]);

  const fetchCommissions = async (page: number) => {
    setLoading(true);
    setError(null);

    try {
      const params: any = { page, limit: 10 };
      if (statusFilter) params.status = statusFilter;
      if (currencyFilter) params.currency = currencyFilter;

      const response = await userService.getReferralCommissions(params);
      const data: CommissionResponse = response.data;

      setCommissions(data.commissions);
      setTotals(data.totals.map(item => ({
        currency: item._id,
        total: item.total
      })));

      // Update pagination
      setTotalPages(data.pagination.pages);
      setCurrentPage(data.pagination.page);
    } catch (err) {
      console.error('Error fetching commission history:', err);
      setError('Failed to load commission history. Please try again.');
      toast({
        title: 'Error',
        description: 'Failed to load commission history',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (nextPage: number) => {
    fetchCommissions(nextPage);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
        return 'green';
      case 'pending':
        return 'yellow';
      case 'rejected':
        return 'red';
      default:
        return 'gray';
    }
  };

  return (
    <Box>
      <Heading size="md" mb={4} color={textColor}>
        {t('referrals.commissionHistory', 'Commission History')}
      </Heading>

      {/* Totals by Currency */}
      {totals.length > 0 && (
        <Flex direction={{ base: 'column', md: 'row' }} gap={4} mb={6}>
          {totals.map((total, index) => (
            <Card key={index} flex="1" bg={cardBgColor} boxShadow="sm" borderRadius="md">
              <CardBody>
                <HStack spacing={4}>
                  <Icon as={FaCoins} boxSize={8} color={accentColor} />
                  <Stat>
                    <StatLabel>{t('referrals.totalEarningsIn', 'Total Earnings in')} {total.currency}</StatLabel>
                    <StatNumber fontSize="xl">{formatCurrency(total.total, total.currency)}</StatNumber>
                    <StatHelpText>
                      {t('referrals.approvedCommissions', 'Approved commissions')}
                    </StatHelpText>
                  </Stat>
                </HStack>
              </CardBody>
            </Card>
          ))}
        </Flex>
      )}

      {/* Filters */}
      <Flex mb={4} gap={4} direction={{ base: 'column', md: 'row' }}>
        <Box flex="1">
          <Select
            placeholder={t('common.filterByStatus', 'Filter by status')}
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            bg={bgColor}
            borderColor={borderColor}
            color={textColor}
          >
            <option value="">All Statuses</option>
            <option value="approved">Approved</option>
            <option value="pending">Pending</option>
            <option value="rejected">Rejected</option>
          </Select>
        </Box>
        <Box flex="1">
          <Select
            placeholder={t('common.filterByCurrency', 'Filter by currency')}
            value={currencyFilter}
            onChange={(e) => setCurrencyFilter(e.target.value)}
            bg={bgColor}
            borderColor={borderColor}
            color={textColor}
          >
            <option value="">All Currencies</option>
            <option value="BTC">Bitcoin (BTC)</option>
            <option value="ETH">Ethereum (ETH)</option>
            <option value="USDT">Tether (USDT)</option>
            <option value="USDC">USD Coin (USDC)</option>
          </Select>
        </Box>
      </Flex>

      {/* Commission Table */}
      {loading ? (
        <Flex justify="center" align="center" minH="200px">
          <Spinner size="xl" thickness="4px" speed="0.65s" color={accentColor} />
        </Flex>
      ) : error ? (
        <Alert status="error" variant="subtle" borderRadius="md">
          <AlertIcon />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      ) : commissions.length > 0 ? (
        <Box>
          <Box overflowX="auto">
            <Table variant="simple" size="md">
              <Thead>
                <Tr>
                  <Th color={secondaryTextColor} borderColor={borderColor}>{t('common.date', 'Date')}</Th>
                  <Th color={secondaryTextColor} borderColor={borderColor}>{t('common.referredUser', 'Referred User')}</Th>
                  <Th color={secondaryTextColor} borderColor={borderColor}>{t('common.amount', 'Amount')}</Th>
                  <Th color={secondaryTextColor} borderColor={borderColor}>{t('common.status', 'Status')}</Th>
                </Tr>
              </Thead>
              <Tbody>
                {commissions.map((commission) => (
                  <Tr key={commission._id}>
                    <Td color={textColor} borderColor={borderColor}>{formatDate(commission.createdAt)}</Td>
                    <Td color={textColor} borderColor={borderColor}>
                      {commission.referredUser ?
                        `${commission.referredUser.firstName} ${commission.referredUser.lastName}` :
                        t('common.unknownUser', 'Unknown User')}
                    </Td>
                    <Td color={textColor} borderColor={borderColor}>
                      {formatCurrency(commission.amount, commission.currency)}
                    </Td>
                    <Td borderColor={borderColor}>
                      <Badge colorScheme={getStatusColor(commission.status)}>
                        {commission.status}
                      </Badge>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          </Box>

          {/* Pagination */}
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </Box>
      ) : (
        <Card bg={cardBgColor} p={6} borderRadius="md" textAlign="center">
          <VStack spacing={4}>
            <Icon as={FaExchangeAlt} boxSize={12} color={secondaryTextColor} />
            <Text color={textColor} fontWeight="medium">
              {t('referrals.noCommissionsYet', 'You have no commission history yet')}
            </Text>
            <Text color={secondaryTextColor}>
              {t('referrals.startEarning', 'Start earning by sharing your referral link with friends')}
            </Text>
          </VStack>
        </Card>
      )}
    </Box>
  );
};

export default ReferralCommissionHistory;
