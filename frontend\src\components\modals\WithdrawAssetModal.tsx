import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>dalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  HStack,
  Text,
  Box,
  Flex,
  Divider,
  useToast,
  Select,
  Textarea,
  Badge,
  Icon,
  Tooltip,
  InputGroup,
  InputRightElement,
  Alert,
  AlertIcon,
  Progress,
  FormHelperText,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Radio,
  RadioGroup,
  Stack
} from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { FaWallet, FaInfoCircle, FaExclamationTriangle, FaMoneyBillWave } from 'react-icons/fa';
import useAuth from '../../hooks/useAuth';
import NetworkSelector from '../common/NetworkSelector';
import { CRYPTO_NETWORKS, getDefaultNetwork, NetworkOption } from '../../utils/cryptoNetworks';
import walletService from '../../services/walletService';

interface WithdrawAssetModalProps {
  isOpen: boolean;
  onClose: () => void;
  asset: {
    symbol: string;
    balance: number;
    commissionBalance: number;
    interestBalance: number;
    mode: 'commission' | 'interest';
  };
  withdrawalType: 'main' | 'commission' | 'interest';
  availableBalance: number;
}

const WithdrawAssetModal: React.FC<WithdrawAssetModalProps> = ({
  isOpen,
  onClose,
  asset,
  withdrawalType,
  availableBalance
}) => {
  const { t } = useTranslation();
  const toast = useToast();
  const { user } = useAuth();
  
  // State variables
  const [activeStep, setActiveStep] = useState(0);
  const [amount, setAmount] = useState('');
  const [walletAddress, setWalletAddress] = useState('');
  const [memo, setMemo] = useState('');
  const [selectedNetwork, setSelectedNetwork] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [withdrawType, setWithdrawType] = useState<'main' | 'commission' | 'interest'>(withdrawalType);

  // Colors
  const primaryColor = "#F0B90B";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const borderColor = "#2B3139";

  // Set default network when modal opens
  useEffect(() => {
    if (asset) {
      const defaultNetwork = getDefaultNetwork(asset.symbol);
      setSelectedNetwork(defaultNetwork);
    }
  }, [asset]);

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      setActiveStep(0);
      setAmount('');
      setWalletAddress('');
      setMemo('');
      setIsSubmitting(false);
      setUploadProgress(0);
      setWithdrawType(withdrawalType);
    } else {
      setWithdrawType(withdrawalType);
    }
  }, [isOpen, withdrawalType]);

  // Format number with commas and fixed decimal places
  const formatNumber = (num: number, decimals: number = 2) => {
    return num.toLocaleString('en-US', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    });
  };

  // Get available balance based on withdrawal type
  const getBalance = (type: 'main' | 'commission' | 'interest') => {
    switch (type) {
      case 'main':
        return asset.balance;
      case 'commission':
        return asset.commissionBalance;
      case 'interest':
        return asset.interestBalance;
      default:
        return 0;
    }
  };

  // Calculate withdrawal fee (example: 0.1% of amount)
  const calculateFee = () => {
    const amountNum = parseFloat(amount) || 0;
    return amountNum * 0.001; // 0.1% fee
  };

  // Calculate net amount after fee
  const calculateNetAmount = () => {
    const amountNum = parseFloat(amount) || 0;
    const fee = calculateFee();
    return amountNum - fee;
  };

  // Next step handler
  const nextStep = () => {
    setActiveStep((prev) => prev + 1);
  };

  // Previous step handler
  const prevStep = () => {
    setActiveStep((prev) => prev - 1);
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      
      // Simulate progress for better UX
      const interval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 90) {
            clearInterval(interval);
            return 90;
          }
          return prev + 10;
        });
      }, 300);

      // Send withdrawal request to backend
      await walletService.withdrawAsset({
        asset: asset.symbol,
        amount: parseFloat(amount),
        address: walletAddress,
        memo: memo || undefined,
        withdrawalType: withdrawType === 'main' ? 'interest' : withdrawType, // API expects 'interest' or 'commission'
        network: selectedNetwork,
        blockchainNetwork: selectedNetwork
      });

      // Complete progress
      setUploadProgress(100);
      
      // Show success message
      toast({
        title: t('withdrawModal.success', 'Withdrawal Submitted'),
        description: t('withdrawModal.successMessage', 'Your withdrawal request has been submitted successfully.'),
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
      
      // Close modal
      onClose();
    } catch (error: any) {
      console.error('Withdrawal error:', error);
      
      // Show error message
      toast({
        title: t('withdrawModal.error', 'Withdrawal Failed'),
        description: error.message || t('withdrawModal.errorMessage', 'Failed to process your withdrawal. Please try again.'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      
      setUploadProgress(0);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="md" isCentered>
      <ModalOverlay backdropFilter="blur(5px)" />
      <ModalContent
        bg="#0B0E11"
        borderColor="#2B3139"
        borderWidth="1px"
        mx={{ base: 2, md: "auto" }}
        maxH={{ base: "calc(100vh - 40px)", md: "calc(100vh - 80px)" }}
        overflowY="auto"
        height="auto"
      >
        <ModalHeader color={primaryColor} borderBottomWidth="1px" borderColor="#2B3139">
          {t('withdrawModal.title', 'Withdraw')} {asset.symbol}
        </ModalHeader>
        <ModalCloseButton color="#EAECEF" />

        <ModalBody py={6}>
          <Tabs index={activeStep} onChange={setActiveStep} variant="unstyled" colorScheme="yellow">
            <TabList mb={4}>
              <Tab
                _selected={{ color: primaryColor, fontWeight: "bold" }}
                color={activeStep === 0 ? primaryColor : "#848E9C"}
                fontWeight={activeStep === 0 ? "bold" : "normal"}
              >
                {t('withdrawModal.step1', '1. Amount')}
              </Tab>
              <Tab
                _selected={{ color: primaryColor, fontWeight: "bold" }}
                color={activeStep === 1 ? primaryColor : "#848E9C"}
                fontWeight={activeStep === 1 ? "bold" : "normal"}
                isDisabled={activeStep < 1}
              >
                {t('withdrawModal.step2', '2. Address')}
              </Tab>
              <Tab
                _selected={{ color: primaryColor, fontWeight: "bold" }}
                color={activeStep === 2 ? primaryColor : "#848E9C"}
                fontWeight={activeStep === 2 ? "bold" : "normal"}
                isDisabled={activeStep < 2}
              >
                {t('withdrawModal.step3', '3. Confirm')}
              </Tab>
            </TabList>

            <TabPanels>
              {/* Step 1: Amount Details */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <Box p={4} bg="#1E2329" borderRadius="md">
                    <Text color="#848E9C" fontSize="sm" mb={2}>{t('withdrawModal.selectWithdrawalType', 'Select Withdrawal Type')}</Text>
                    
                    <RadioGroup onChange={(val) => setWithdrawType(val as any)} value={withdrawType} mb={4}>
                      <Stack direction="column" spacing={3}>
                        <Radio
                          value="main"
                          colorScheme="yellow"
                          borderColor="#2B3139"
                        >
                          <HStack>
                            <Text color="#EAECEF">{t('withdrawModal.mainBalance', 'Main Balance')}</Text>
                            <Badge colorScheme="yellow" ml={2}>Available</Badge>
                            <Text color="#F0B90B" fontWeight="bold" ml="auto">
                              {formatNumber(asset.balance, asset.symbol === 'BTC' ? 8 : 4)} {asset.symbol}
                            </Text>
                          </HStack>
                        </Radio>
                        
                        <Radio
                          value="commission"
                          colorScheme="green"
                          borderColor="#2B3139"
                        >
                          <HStack>
                            <Text color="#EAECEF">{t('withdrawModal.commissionBalance', 'Commission Balance')}</Text>
                            <Badge colorScheme="green" ml={2}>Available</Badge>
                            <Text color="#0ECB81" fontWeight="bold" ml="auto">
                              {formatNumber(asset.commissionBalance, asset.symbol === 'BTC' ? 8 : 4)} {asset.symbol}
                            </Text>
                          </HStack>
                        </Radio>
                        
                        <Radio
                          value="interest"
                          colorScheme="blue"
                          borderColor="#2B3139"
                        >
                          <HStack>
                            <Text color="#EAECEF">{t('withdrawModal.interestBalance', 'Interest Balance')}</Text>
                            <Badge colorScheme="blue" ml={2}>Available</Badge>
                            <Text color="#1E88E5" fontWeight="bold" ml="auto">
                              {formatNumber(asset.interestBalance, asset.symbol === 'BTC' ? 8 : 4)} {asset.symbol}
                            </Text>
                          </HStack>
                        </Radio>
                      </Stack>
                    </RadioGroup>
                  </Box>

                  <FormControl isRequired>
                    <FormLabel color="#848E9C" fontSize="sm">{t('withdrawModal.amount', 'Withdrawal Amount')}</FormLabel>
                    <InputGroup>
                      <NumberInput
                        value={amount}
                        onChange={setAmount}
                        max={getBalance(withdrawType)}
                        min={0.001}
                        precision={asset.symbol === 'BTC' ? 8 : 4}
                        w="100%"
                      >
                        <NumberInputField
                          placeholder="0.00"
                          bg="#0B0E11"
                          borderColor="#2B3139"
                          color="#EAECEF"
                          _hover={{ borderColor: "#F0B90B" }}
                        />
                        <NumberInputStepper>
                          <NumberIncrementStepper color="#EAECEF" />
                          <NumberDecrementStepper color="#EAECEF" />
                        </NumberInputStepper>
                      </NumberInput>
                    </InputGroup>
                    <FormHelperText color="#848E9C" fontSize="xs">
                      {t('withdrawModal.availableBalance', 'Available Balance')}: {formatNumber(getBalance(withdrawType), asset.symbol === 'BTC' ? 8 : 4)} {asset.symbol}
                    </FormHelperText>
                  </FormControl>

                  <FormControl>
                    <FormLabel color="#848E9C" fontSize="sm">{t('withdrawModal.network', 'Network')}</FormLabel>
                    <NetworkSelector
                      selectedNetwork={selectedNetwork}
                      onNetworkChange={setSelectedNetwork}
                      cryptoSymbol={asset.symbol}
                    />
                    <FormHelperText color="#848E9C" fontSize="xs">
                      {t('withdrawModal.selectNetwork', 'Select the blockchain network for your withdrawal')}
                    </FormHelperText>
                  </FormControl>

                  <Flex justify="flex-end" mt={6}>
                    <Button
                      bg="#F0B90B"
                      color="#0B0E11"
                      _hover={{ bg: "#F8D12F" }}
                      onClick={nextStep}
                      isDisabled={!amount || parseFloat(amount) <= 0 || parseFloat(amount) > getBalance(withdrawType) || !selectedNetwork}
                    >
                      {t('common.next', 'Next')}
                    </Button>
                  </Flex>
                </VStack>
              </TabPanel>

              {/* Step 2: Wallet Address */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <FormControl isRequired>
                    <FormLabel color="#848E9C" fontSize="sm">{t('withdrawModal.walletAddress', 'Wallet Address')}</FormLabel>
                    <Input
                      placeholder={`Enter your ${asset.symbol} wallet address`}
                      value={walletAddress}
                      onChange={(e) => setWalletAddress(e.target.value)}
                      bg="#0B0E11"
                      borderColor="#2B3139"
                      color="#EAECEF"
                      _hover={{ borderColor: "#F0B90B" }}
                    />
                    <FormHelperText color="#848E9C" fontSize="xs">
                      {t('withdrawModal.addressWarning', 'Make sure to enter the correct address for the selected network')}
                    </FormHelperText>
                  </FormControl>

                  <FormControl>
                    <FormLabel color="#848E9C" fontSize="sm">{t('withdrawModal.memo', 'Memo (Optional)')}</FormLabel>
                    <Textarea
                      placeholder={t('withdrawModal.memoPlaceholder', 'Add a note to your transaction')}
                      value={memo}
                      onChange={(e) => setMemo(e.target.value)}
                      bg="#0B0E11"
                      borderColor="#2B3139"
                      color="#EAECEF"
                      _hover={{ borderColor: "#F0B90B" }}
                      size="sm"
                      resize="vertical"
                      maxLength={100}
                    />
                    <FormHelperText color="#848E9C" fontSize="xs">
                      {t('withdrawModal.memoHelp', 'Some exchanges require a memo/tag for deposits')}
                    </FormHelperText>
                  </FormControl>

                  <Alert status="warning" bg="#332B00" borderRadius="md" mt={4}>
                    <AlertIcon color="#F0B90B" />
                    <Text color="#EAECEF" fontSize="sm">
                      {t('withdrawModal.addressWarningFull', 'Please double-check the address. Transactions cannot be reversed once submitted.')}
                    </Text>
                  </Alert>

                  <Flex justify="space-between" mt={6}>
                    <Button
                      variant="outline"
                      onClick={prevStep}
                      borderColor="#2B3139"
                      color="#EAECEF"
                      _hover={{ borderColor: "#F0B90B" }}
                      flex="1"
                      mr={2}
                    >
                      {t('common.back', 'Back')}
                    </Button>
                    <Button
                      bg="#F0B90B"
                      color="#0B0E11"
                      _hover={{ bg: "#F8D12F" }}
                      onClick={nextStep}
                      isDisabled={!walletAddress}
                      flex="1"
                      ml={2}
                    >
                      {t('common.next', 'Next')}
                    </Button>
                  </Flex>
                </VStack>
              </TabPanel>

              {/* Step 3: Confirmation */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <Box p={4} bg="#1E2329" borderRadius="md">
                    <Text color="#848E9C" fontSize="sm" mb={2}>{t('withdrawModal.confirmationTitle', 'Transaction Confirmation')}</Text>
                    
                    <Divider borderColor="#2B3139" my={3} />
                    
                    <HStack justify="space-between" mb={2}>
                      <Text color="#848E9C" fontSize="sm">{t('withdrawModal.asset', 'Asset')}</Text>
                      <Text color="#EAECEF" fontSize="sm" fontWeight="medium">{asset.symbol}</Text>
                    </HStack>
                    
                    <HStack justify="space-between" mb={2}>
                      <Text color="#848E9C" fontSize="sm">{t('withdrawModal.withdrawalType', 'Withdrawal Type')}</Text>
                      <Badge colorScheme={withdrawType === 'main' ? 'yellow' : withdrawType === 'commission' ? 'green' : 'blue'}>
                        {withdrawType === 'main' 
                          ? t('withdrawModal.mainBalance', 'Main Balance') 
                          : withdrawType === 'commission' 
                            ? t('withdrawModal.commissionBalance', 'Commission') 
                            : t('withdrawModal.interestBalance', 'Interest')}
                      </Badge>
                    </HStack>
                    
                    <HStack justify="space-between" mb={2}>
                      <Text color="#848E9C" fontSize="sm">{t('withdrawModal.amount', 'Amount')}</Text>
                      <Text color="#EAECEF" fontSize="sm" fontWeight="medium">{amount} {asset.symbol}</Text>
                    </HStack>
                    
                    <HStack justify="space-between" mb={2}>
                      <Text color="#848E9C" fontSize="sm">{t('withdrawModal.fee', 'Fee')}</Text>
                      <Text color="#EAECEF" fontSize="sm" fontWeight="medium">{formatNumber(calculateFee(), asset.symbol === 'BTC' ? 8 : 4)} {asset.symbol}</Text>
                    </HStack>
                    
                    <HStack justify="space-between" mb={2}>
                      <Text color="#848E9C" fontSize="sm">{t('withdrawModal.netAmount', 'Net Amount')}</Text>
                      <Text color="#EAECEF" fontSize="sm" fontWeight="bold">{formatNumber(calculateNetAmount(), asset.symbol === 'BTC' ? 8 : 4)} {asset.symbol}</Text>
                    </HStack>
                    
                    <Divider borderColor="#2B3139" my={3} />
                    
                    <HStack justify="space-between" mb={2}>
                      <Text color="#848E9C" fontSize="sm">{t('withdrawModal.network', 'Network')}</Text>
                      <Text color="#EAECEF" fontSize="sm" fontWeight="medium">{selectedNetwork}</Text>
                    </HStack>
                    
                    <HStack justify="space-between" mb={2} align="flex-start">
                      <Text color="#848E9C" fontSize="sm">{t('withdrawModal.walletAddress', 'Wallet Address')}</Text>
                      <Text color="#EAECEF" fontSize="sm" fontWeight="medium" maxW="200px" isTruncated>{walletAddress}</Text>
                    </HStack>
                    
                    {memo && (
                      <HStack justify="space-between" mb={2} align="flex-start">
                        <Text color="#848E9C" fontSize="sm">{t('withdrawModal.memo', 'Memo')}</Text>
                        <Text color="#EAECEF" fontSize="sm" fontWeight="medium" maxW="200px" isTruncated>{memo}</Text>
                      </HStack>
                    )}
                  </Box>

                  <Alert status="info" bg="#00253E" borderRadius="md">
                    <AlertIcon color="#1E88E5" />
                    <Text color="#EAECEF" fontSize="sm">
                      {t('withdrawModal.processingTime', 'Withdrawal processing time: 1-24 hours')}
                    </Text>
                  </Alert>

                  <Flex justify="space-between" mt={6}>
                    <Button
                      variant="outline"
                      onClick={prevStep}
                      borderColor="#2B3139"
                      color="#EAECEF"
                      _hover={{ borderColor: "#F0B90B" }}
                      flex="1"
                      mr={2}
                    >
                      {t('common.back', 'Back')}
                    </Button>
                    <Button
                      bg="#F0B90B"
                      color="#0B0E11"
                      _hover={{ bg: "#F8D12F" }}
                      onClick={handleSubmit}
                      isLoading={isSubmitting}
                      loadingText={t('common.processing', 'Processing...')}
                      flex="1"
                      ml={2}
                    >
                      {t('withdrawModal.confirmButton', 'Confirm')}
                    </Button>
                  </Flex>

                  {isSubmitting && (
                    <Box mt={4}>
                      <Text fontSize="xs" color="#848E9C" mb={2}>
                        {t('withdrawModal.processing', 'Processing your transaction...')}
                      </Text>
                      <Progress value={uploadProgress} size="xs" colorScheme="yellow" borderRadius="full" />
                    </Box>
                  )}
                </VStack>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default WithdrawAssetModal;
