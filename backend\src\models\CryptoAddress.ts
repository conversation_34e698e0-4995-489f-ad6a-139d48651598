import mongoose, { Document, Schema } from 'mongoose';

export interface ICryptoAddress extends Document {
  address: string;
  currency: string;
  userId: string;
  derivationPath: string;
  publicKey: string;
  isActive: boolean;
  lastUsed?: Date;
  transactionCount: number;
  createdAt: Date;
  updatedAt: Date;
}

const CryptoAddressSchema = new Schema<ICryptoAddress>({
  address: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  currency: {
    type: String,
    required: true,
    enum: ['BTC', 'ETH', 'USDT', 'BNB', 'SOL'],
    index: true
  },
  userId: {
    type: String,
    required: true,
    ref: 'User',
    index: true
  },
  derivationPath: {
    type: String,
    required: true
  },
  publicKey: {
    type: String,
    required: true
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  lastUsed: {
    type: Date
  },
  transactionCount: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true,
  collection: 'crypto_addresses'
});

// Compound indexes for efficient queries
CryptoAddressSchema.index({ userId: 1, currency: 1, createdAt: -1 });
CryptoAddressSchema.index({ address: 1, currency: 1 });
CryptoAddressSchema.index({ isActive: 1, currency: 1 });

// Instance methods
CryptoAddressSchema.methods.markAsUsed = function() {
  this.lastUsed = new Date();
  this.transactionCount += 1;
  return this.save();
};

CryptoAddressSchema.methods.deactivate = function() {
  this.isActive = false;
  return this.save();
};

// Static methods
CryptoAddressSchema.statics.findActiveByUser = function(userId: string, currency?: string) {
  const query: any = { userId, isActive: true };
  if (currency) {
    query.currency = currency.toUpperCase();
  }
  return this.find(query).sort({ createdAt: -1 });
};

CryptoAddressSchema.statics.findByAddress = function(address: string) {
  return this.findOne({ address, isActive: true });
};

CryptoAddressSchema.statics.getAddressStats = function(userId: string) {
  return this.aggregate([
    { $match: { userId } },
    {
      $group: {
        _id: '$currency',
        totalAddresses: { $sum: 1 },
        activeAddresses: {
          $sum: { $cond: [{ $eq: ['$isActive', true] }, 1, 0] }
        },
        totalTransactions: { $sum: '$transactionCount' },
        lastUsed: { $max: '$lastUsed' }
      }
    }
  ]);
};

export const CryptoAddress = mongoose.model<ICryptoAddress>('CryptoAddress', CryptoAddressSchema);
