import React, { useState } from 'react';
import { Box, Button, FormControl, FormLabel, Input, Select, FormErrorMessage, VStack, Heading, Text, Flex, Divider, useColorModeValue } from '@chakra-ui/react';
import { useFormWithValidation, validationSchemas } from '../../hooks/useFormWithValidation';
import { useContract } from '../../hooks/useContract';
import { useWeb3 } from '../../hooks/useWeb3';
import { walletService } from '../../services/api';

/**
 * Deposit form component
 */
const DepositForm: React.FC = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { contract, loading: contractLoading } = useContract();
  const { account, isConnected, connect } = useWeb3();
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  
  // Initialize form with validation
  const { 
    register, 
    handleSubmit, 
    formState: { errors }, 
    reset,
    handleSubmitWithToast
  } = useFormWithValidation(validationSchemas.deposit);
  
  // Handle form submission
  const onSubmit = async (data: any) => {
    if (!isConnected) {
      await connect();
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Call contract method
      if (contract) {
        const tx = await contract.deposit(
          data.asset,
          data.amount,
          data.mode.toUpperCase()
        );
        
        // Wait for transaction to be mined
        await tx.wait();
        
        // Record transaction in backend
        await walletService.deposit({
          token: data.asset,
          amount: parseFloat(data.amount),
          mode: data.mode
        });
        
        // Reset form
        reset();
      }
    } catch (error) {
      console.error('Deposit error:', error);
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <Box 
      p={6} 
      borderRadius="lg" 
      boxShadow="md" 
      bg={bgColor}
      borderWidth="1px"
      borderColor={borderColor}
    >
      <VStack spacing={6} align="stretch">
        <Heading size="md">Deposit Funds</Heading>
        
        <Divider />
        
        <form onSubmit={handleSubmitWithToast(onSubmit, 'Deposit successful!')}>
          <VStack spacing={4} align="stretch">
            {!isConnected && (
              <Box p={4} bg="blue.50" color="blue.800" borderRadius="md">
                <Text>Please connect your wallet to deposit funds.</Text>
                <Button 
                  mt={2} 
                  colorScheme="blue" 
                  size="sm" 
                  onClick={connect}
                  isLoading={contractLoading}
                >
                  Connect Wallet
                </Button>
              </Box>
            )}
            
            <FormControl isInvalid={!!errors.asset}>
              <FormLabel>Asset</FormLabel>
              <Select 
                placeholder="Select asset" 
                {...register('asset')}
              >
                <option value="ETH">Ethereum (ETH)</option>
                <option value="USDT">Tether (USDT)</option>
                <option value="USDC">USD Coin (USDC)</option>
                <option value="BTC">Bitcoin (BTC)</option>
              </Select>
              <FormErrorMessage>
                {errors.asset?.message?.toString()}
              </FormErrorMessage>
            </FormControl>
            
            <FormControl isInvalid={!!errors.amount}>
              <FormLabel>Amount</FormLabel>
              <Input 
                type="number" 
                step="0.000001"
                placeholder="0.0" 
                {...register('amount')}
              />
              <FormErrorMessage>
                {errors.amount?.message?.toString()}
              </FormErrorMessage>
            </FormControl>
            
            <FormControl isInvalid={!!errors.mode}>
              <FormLabel>Yield Mode</FormLabel>
              <Select 
                placeholder="Select mode" 
                {...register('mode')}
              >
                <option value="commission">Commission (1% instant)</option>
                <option value="interest">Interest (variable rate)</option>
              </Select>
              <FormErrorMessage>
                {errors.mode?.message?.toString()}
              </FormErrorMessage>
            </FormControl>
            
            <Flex justify="space-between" mt={4}>
              <Button 
                variant="outline" 
                onClick={() => reset()}
              >
                Reset
              </Button>
              <Button 
                type="submit" 
                colorScheme="blue" 
                isLoading={isSubmitting || contractLoading}
                isDisabled={!isConnected}
              >
                Deposit
              </Button>
            </Flex>
          </VStack>
        </form>
      </VStack>
    </Box>
  );
};

export default DepositForm;
