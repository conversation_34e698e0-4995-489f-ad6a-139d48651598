import React from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  Button,
  Input,
  FormControl,
  FormLabel,
  FormHelperText,
  Checkbox,
  Radio,
  RadioGroup,
  Stack,
  Select,
  Textarea,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Alert,
  AlertIcon,
  useColorModeValue,
  Link,
  HStack,
  Divider
} from '@chakra-ui/react';
import { Helmet } from 'react-helmet';
import { useTranslation } from 'react-i18next';

const AccessibilityDemo: React.FC = () => {
  const { t } = useTranslation();
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  return (
    <Box>
      <Helmet>
        <title>Accessibility Demo | Shipping Finance</title>
        <meta name="description" content="Accessibility features demonstration page" />
      </Helmet>

      <Container maxW="container.xl" py={8}>
        <VStack spacing={8} align="stretch">
          <Heading as="h1" size="xl" textAlign="center">
            Accessibility Features Demo
          </Heading>
          
          <Text fontSize="lg" textAlign="center">
            This page demonstrates various accessibility features implemented in our application.
          </Text>

          <Divider />

          <Box p={6} borderWidth="1px" borderRadius="lg" bg={bgColor} borderColor={borderColor}>
            <VStack spacing={6} align="stretch">
              <Heading as="h2" size="lg">Form Controls</Heading>
              
              <FormControl id="name" isRequired>
                <FormLabel>Name</FormLabel>
                <Input placeholder="Enter your name" aria-describedby="name-helper-text" />
                <FormHelperText id="name-helper-text">
                  Your full name as it appears on your ID.
                </FormHelperText>
              </FormControl>

              <FormControl id="email" isRequired>
                <FormLabel>Email address</FormLabel>
                <Input type="email" placeholder="Enter your email" />
              </FormControl>

              <FormControl id="preferences">
                <FormLabel>Notification Preferences</FormLabel>
                <VStack align="start" spacing={2}>
                  <Checkbox>Email notifications</Checkbox>
                  <Checkbox>SMS notifications</Checkbox>
                  <Checkbox>Push notifications</Checkbox>
                </VStack>
              </FormControl>

              <FormControl id="contact-method">
                <FormLabel>Preferred Contact Method</FormLabel>
                <RadioGroup defaultValue="email">
                  <Stack direction="column" spacing={2}>
                    <Radio value="email">Email</Radio>
                    <Radio value="phone">Phone</Radio>
                    <Radio value="mail">Mail</Radio>
                  </Stack>
                </RadioGroup>
              </FormControl>

              <FormControl id="country" isRequired>
                <FormLabel>Country</FormLabel>
                <Select placeholder="Select country">
                  <option value="us">United States</option>
                  <option value="ca">Canada</option>
                  <option value="uk">United Kingdom</option>
                  <option value="au">Australia</option>
                </Select>
              </FormControl>

              <FormControl id="message">
                <FormLabel>Message</FormLabel>
                <Textarea placeholder="Enter your message" />
              </FormControl>

              <Button colorScheme="blue" aria-label="Submit form">
                Submit
              </Button>
            </VStack>
          </Box>

          <Divider />

          <Box p={6} borderWidth="1px" borderRadius="lg" bg={bgColor} borderColor={borderColor}>
            <VStack spacing={6} align="stretch">
              <Heading as="h2" size="lg">Keyboard Navigation</Heading>
              
              <Text>
                Try navigating through the elements below using only your keyboard (Tab, Shift+Tab, Enter, Space, and arrow keys).
              </Text>
              
              <HStack spacing={4}>
                <Button>Button 1</Button>
                <Button>Button 2</Button>
                <Button>Button 3</Button>
              </HStack>
              
              <Tabs isFitted variant="enclosed">
                <TabList>
                  <Tab>Tab 1</Tab>
                  <Tab>Tab 2</Tab>
                  <Tab>Tab 3</Tab>
                </TabList>
                <TabPanels>
                  <TabPanel>
                    <Text>Content for Tab 1</Text>
                  </TabPanel>
                  <TabPanel>
                    <Text>Content for Tab 2</Text>
                  </TabPanel>
                  <TabPanel>
                    <Text>Content for Tab 3</Text>
                  </TabPanel>
                </TabPanels>
              </Tabs>
            </VStack>
          </Box>

          <Divider />

          <Box p={6} borderWidth="1px" borderRadius="lg" bg={bgColor} borderColor={borderColor}>
            <VStack spacing={6} align="stretch">
              <Heading as="h2" size="lg">Screen Reader Support</Heading>
              
              <Text>
                The following elements have been optimized for screen readers with appropriate ARIA attributes.
              </Text>
              
              <Alert status="info" aria-live="polite">
                <AlertIcon />
                This is an informational alert that can be read by screen readers.
              </Alert>
              
              <Link href="#" aria-label="Learn more about accessibility features">
                Learn more about our accessibility features
              </Link>
            </VStack>
          </Box>
        </VStack>
      </Container>
    </Box>
  );
};

export default AccessibilityDemo;
