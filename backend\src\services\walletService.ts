import UserWallet, { IUserWallet } from '../models/userWalletModel';
import DepositTransaction from '../models/depositTransactionModel';
import cryptoApiService from './cryptoApiService';
import crypto from 'crypto';

interface WalletBalance {
  currency: string;
  balance: number;
  usdtValue: number;
  lastUpdated: Date;
}

interface CreateWalletParams {
  userId: string;
  currency: string;
  network?: string;
}

class WalletService {
  private readonly SUPPORTED_CURRENCIES = ['BTC', 'ETH', 'USDT', 'BNB', 'ADA'];

  // Mock addresses for development - in production, these would be generated or retrieved from a wallet service
  private readonly MOCK_ADDRESSES = {
    BTC: [
      '******************************************',
      '******************************************',
      '**************************************************************'
    ],
    ETH: [
      '******************************************',
      '0x8ba1f109551bD432803012645Hac136c22C4',
      '******************************************'
    ],
    USDT: [
      '******************************************',
      '******************************************',
      '******************************************'
    ],
    BNB: [
      'bnb1grpf0955h0ykzq3ar5nmum7y6gdfl6lxfn46h2',
      'bnb1jxfh2g85q3v0tdq56fnevx6xcxtcnhtsmcu64m',
      'bnb136ns6lfw4zs5hg4n85vdthaad7hq5m4gtkgf23'
    ],
    ADA: [
      'addr1qxy2kgdygjrsqtzq2n0yrf2493p83kkfjhx0wlh',
      'addr1qw508d6qejxtdg4y5r3zarvary0c5xw7kv8f3t4',
      'addr1qrp33g0q5c5txsp9arysrx4k6zdkfs4nce4xj0gdcccefvpysxf3qccfmv3'
    ]
  };

  /**
   * Get all wallets for a user
   */
  async getUserWallets(userId: string): Promise<IUserWallet[]> {
    try {
      const wallets = await UserWallet.getUserWallets(userId);

      // If user has no wallets, create them
      if (wallets.length === 0) {
        return await this.createUserWallets(userId);
      }

      return wallets;
    } catch (error) {
      console.error('Error getting user wallets:', error);
      throw new Error('Failed to get user wallets');
    }
  }

  /**
   * Create wallets for all supported currencies for a user
   */
  async createUserWallets(userId: string): Promise<IUserWallet[]> {
    try {
      const wallets: IUserWallet[] = [];

      for (const currency of this.SUPPORTED_CURRENCIES) {
        try {
          const wallet = await this.createWallet({ userId, currency });
          wallets.push(wallet);
        } catch (error) {
          console.error(`Error creating ${currency} wallet for user ${userId}:`, error);
          // Continue with other currencies
        }
      }

      console.log(`✅ Created ${wallets.length} wallets for user ${userId}`);
      return wallets;
    } catch (error) {
      console.error('Error creating user wallets:', error);
      throw new Error('Failed to create user wallets');
    }
  }

  /**
   * Create a single wallet for a user
   */
  async createWallet(params: CreateWalletParams): Promise<IUserWallet> {
    try {
      const { userId, currency, network = 'mainnet' } = params;

      // Check if wallet already exists
      const existingWallets = await UserWallet.find({ userId, currency });
      if (existingWallets.length > 0) {
        return existingWallets[0];
      }

      // Generate or get address for the currency
      const address = this.generateAddress(currency);

      const wallet = await UserWallet.createUserWallet(userId, currency, address, network);

      console.log(`✅ Created ${currency} wallet for user ${userId}: ${address}`);
      return wallet;
    } catch (error) {
      console.error('Error creating wallet:', error);
      throw new Error(`Failed to create ${params.currency} wallet`);
    }
  }

  /**
   * Get wallet balances with USDT values
   */
  async getWalletBalances(userId: string): Promise<WalletBalance[]> {
    try {
      const wallets = await this.getUserWallets(userId);
      const balances: WalletBalance[] = [];

      for (const wallet of wallets) {
        try {
          let usdtValue = wallet.balance;

          // Convert to USDT if not already USDT
          if (wallet.currency !== 'USDT' && wallet.balance > 0) {
            try {
              const conversion = await cryptoApiService.convertCurrency(
                wallet.balance,
                wallet.currency,
                'USDT'
              );
              usdtValue = conversion.amount;
            } catch (conversionError) {
              console.warn(`Failed to convert ${wallet.currency} to USDT, using mock rate`);
              // Use mock conversion rates
              const mockRates: { [key: string]: number } = {
                'BTC': 45000,
                'ETH': 3000,
                'BNB': 300,
                'ADA': 0.5,
                'DOT': 8
              };
              usdtValue = wallet.balance * (mockRates[wallet.currency] || 1);
            }
          }

          balances.push({
            currency: wallet.currency,
            balance: wallet.balance,
            usdtValue,
            lastUpdated: wallet.lastUpdated
          });
        } catch (error) {
          console.error(`Error processing balance for ${wallet.currency}:`, error);
          // Add with zero values if error
          balances.push({
            currency: wallet.currency,
            balance: 0,
            usdtValue: 0,
            lastUpdated: new Date()
          });
        }
      }

      return balances;
    } catch (error) {
      console.error('Error getting wallet balances:', error);
      throw new Error('Failed to get wallet balances');
    }
  }

  /**
   * Get deposit history for a user
   */
  async getDepositHistory(userId: string, limit: number = 10): Promise<any[]> {
    try {
      const deposits = await DepositTransaction.getUserDeposits(userId);

      return deposits.slice(0, limit).map(deposit => ({
        id: deposit._id,
        currency: deposit.currency,
        amount: deposit.amount,
        usdtValue: deposit.usdtValue,
        status: deposit.status,
        confirmations: deposit.confirmations,
        requiredConfirmations: deposit.requiredConfirmations,
        transactionHash: deposit.transactionHash,
        createdAt: deposit.createdAt,
        confirmedAt: deposit.confirmedAt,
        autoInvestmentEnabled: deposit.autoInvestmentEnabled,
        investmentPackageId: deposit.investmentPackageId
      }));
    } catch (error) {
      console.error('Error getting deposit history:', error);
      throw new Error('Failed to get deposit history');
    }
  }

  /**
   * Get wallet by address
   */
  async getWalletByAddress(address: string): Promise<IUserWallet | null> {
    try {
      return await UserWallet.getWalletByAddress(address);
    } catch (error) {
      console.error('Error getting wallet by address:', error);
      return null;
    }
  }

  /**
   * Update wallet balance
   */
  async updateWalletBalance(address: string, newBalance: number): Promise<IUserWallet | null> {
    try {
      const wallet = await UserWallet.getWalletByAddress(address);
      if (!wallet) {
        throw new Error(`Wallet not found for address: ${address}`);
      }

      return await wallet.updateBalance(newBalance);
    } catch (error) {
      console.error('Error updating wallet balance:', error);
      throw new Error('Failed to update wallet balance');
    }
  }

  /**
   * Generate QR code for wallet address
   */
  generateQRCode(address: string, currency: string): string {
    try {
      // Create QR code URL with proper format for each currency
      let qrData = address;

      switch (currency.toUpperCase()) {
        case 'BTC':
          qrData = `bitcoin:${address}`;
          break;
        case 'ETH':
        case 'USDT':
          qrData = `ethereum:${address}`;
          break;
        case 'BNB':
          qrData = `binance:${address}`;
          break;
        case 'ADA':
          qrData = `cardano:${address}`;
          break;
        default:
          qrData = address;
      }

      return `https://api.qrserver.com/v1/create-qr-code/?size=256x256&data=${encodeURIComponent(qrData)}`;
    } catch (error) {
      console.error('Error generating QR code:', error);
      return `https://api.qrserver.com/v1/create-qr-code/?size=256x256&data=${encodeURIComponent(address)}`;
    }
  }

  /**
   * Generate address for a currency (mock implementation)
   */
  private generateAddress(currency: string): string {
    try {
      const addresses = this.MOCK_ADDRESSES[currency as keyof typeof this.MOCK_ADDRESSES];
      if (!addresses || addresses.length === 0) {
        throw new Error(`No mock addresses available for ${currency}`);
      }

      // Return a random address from the mock list
      const randomIndex = Math.floor(Math.random() * addresses.length);
      return addresses[randomIndex];
    } catch (error) {
      console.error(`Error generating address for ${currency}:`, error);
      // Fallback to a generic address
      return `mock_${currency.toLowerCase()}_${crypto.randomBytes(16).toString('hex')}`;
    }
  }

  /**
   * Get total deposits summary for a user
   */
  async getDepositsSummary(userId: string): Promise<any> {
    try {
      const totalDeposits = await DepositTransaction.getTotalDepositsByUser(userId);

      const summary = {
        totalDeposits: 0,
        totalUSDTValue: 0,
        currencyBreakdown: totalDeposits,
        pendingDeposits: 0,
        confirmedDeposits: 0
      };

      // Calculate totals
      for (const deposit of totalDeposits) {
        summary.totalUSDTValue += deposit.totalUSDTValue || 0;
        summary.confirmedDeposits += deposit.count;
      }

      // Get pending deposits count
      const pendingDeposits = await DepositTransaction.find({
        userId,
        status: 'pending'
      }).countDocuments();

      summary.pendingDeposits = pendingDeposits;

      return summary;
    } catch (error) {
      console.error('Error getting deposits summary:', error);
      throw new Error('Failed to get deposits summary');
    }
  }
}

// Export singleton instance
const walletService = new WalletService();
export default walletService;
