version: '3.8'

services:
  # MongoDB service
  mongodb:
    image: mongo:latest
    container_name: cryptoyield-mongodb
    restart: always
    environment:
      MONGO_INITDB_ROOT_USERNAME: cryptoyield_admin
      MONGO_INITDB_ROOT_PASSWORD: secure_password123
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init-replica.js:/docker-entrypoint-initdb.d/mongo-init-replica.js
      - ./mongodb-keyfile/mongodb-keyfile:/tmp/keyfile-source:ro
    ports:
      - "27017:27017"
    networks:
      - cryptoyield-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5
    entrypoint: >
      bash -c "
        cp /tmp/keyfile-source /data/keyfile &&
        chmod 600 /data/keyfile &&
        chown mongodb:mongodb /data/keyfile &&
        exec docker-entrypoint.sh mongod --replSet rs0 --keyFile /data/keyfile --bind_ip_all
      "

  # Mongo Express service (MongoDB admin interface)
  mongo-express:
    image: mongo-express:latest
    container_name: cryptoyield-mongo-express
    restart: always
    depends_on:
      - mongodb
    environment:
      - ME_CONFIG_MONGODB_ADMINUSERNAME=cryptoyield_admin
      - ME_CONFIG_MONGODB_ADMINPASSWORD=secure_password123
      - ME_CONFIG_MONGODB_URL=*****************************************************************************************
      - ME_CONFIG_BASICAUTH_USERNAME=admin
      - ME_CONFIG_BASICAUTH_PASSWORD=admin123
      - ME_CONFIG_MONGODB_ENABLE_ADMIN=true
      - ME_CONFIG_OPTIONS_EDITORTHEME=ambiance
    ports:
      - "8081:8081"
    networks:
      - cryptoyield-network

networks:
  cryptoyield-network:
    driver: bridge

volumes:
  mongodb_data:
    driver: local
