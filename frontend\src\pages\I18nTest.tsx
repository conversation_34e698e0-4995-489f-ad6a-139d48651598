import React from 'react';
import {
  Box,
  Container,
  VStack,
  Heading,
  Text,
  useColorModeValue,
} from '@chakra-ui/react';
import I18nTestComponent from '../components/I18nTestComponent';
import { useI18n } from '../hooks/useI18n';

/**
 * I18nTest Page
 * 
 * A dedicated page for testing and demonstrating i18n functionality.
 * This page is useful for development and QA testing.
 */
const I18nTest: React.FC = () => {
  const { t, currentLanguage } = useI18n();
  
  // Theme colors
  const bgColor = useColorModeValue('gray.50', '#0B0E11');
  const textColor = useColorModeValue('gray.800', '#EAECEF');

  return (
    <Box bg={bgColor} minH="100vh" py={8}>
      <Container maxW="container.xl">
        <VStack spacing={8} align="stretch">
          {/* Page Header */}
          <Box textAlign="center">
            <Heading size="xl" color={textColor} mb={4}>
              🌐 Internationalization (i18n) Test Page
            </Heading>
            <Text color={textColor} fontSize="lg">
              Current Language: <strong>{currentLanguage.toUpperCase()}</strong>
            </Text>
            <Text color={textColor} mt={2}>
              This page demonstrates all i18n features including translations, 
              formatting, and language switching functionality.
            </Text>
          </Box>

          {/* Test Component */}
          <I18nTestComponent />

          {/* Footer */}
          <Box textAlign="center" pt={8}>
            <Text color={textColor} fontSize="sm">
              🚀 CryptoYield Platform - Comprehensive i18n Implementation
            </Text>
          </Box>
        </VStack>
      </Container>
    </Box>
  );
};

export default I18nTest;
