import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  Modal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  HStack,
  Text,
  Box,
  Flex,
  Divider,
  useToast,
  Select,
  Textarea,
  Image,
  Badge,
  useColorModeValue,
  Icon,
  Tooltip,
  InputGroup,
  InputRightElement,
  Alert,
  AlertIcon,
  Progress,
  FormHelperText,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Spinner,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper
} from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { FaWallet, FaInfoCircle, FaUpload, FaTrash, FaExclamation<PERSON>riangle, FaMoneyBillWave } from 'react-icons/fa';
import useAuth from '../../hooks/useAuth';
import NetworkSelector from '../common/NetworkSelector';
import { CRYPTO_NETWORKS, getDefaultNetwork, NetworkOption } from '../../utils/cryptoNetworks';

interface WithdrawModalProps {
  isOpen: boolean;
  onClose: () => void;
  availableBalance?: number;
}

const WithdrawModal: React.FC<WithdrawModalProps> = ({ isOpen, onClose, availableBalance = 0 }) => {
  const { t } = useTranslation();
  const toast = useToast();
  const { user } = useAuth();

  // State variables
  const [selectedCrypto, setSelectedCrypto] = useState('BTC');
  const [amount, setAmount] = useState('');
  const [walletAddress, setWalletAddress] = useState('');
  const [description, setDescription] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [activeStep, setActiveStep] = useState(0);
  const [withdrawalFee, setWithdrawalFee] = useState(0.001); // Default fee
  const [selectedNetwork, setSelectedNetwork] = useState<string>('');
  const [networkOptions, setNetworkOptions] = useState<NetworkOption[]>([]);
  const [currentNetworkDetails, setCurrentNetworkDetails] = useState<NetworkOption | undefined>(undefined);

  // Colors
  const bgColor = useColorModeValue('#FFFFFF', '#1E2329');
  const borderColor = useColorModeValue('#E2E8F0', '#2B3139');
  const primaryColor = '#F0B90B';
  const textColor = useColorModeValue('#1A202C', '#EAECEF');
  const secondaryTextColor = useColorModeValue('#4A5568', '#848E9C');

  // Initialize network options when cryptocurrency changes
  useEffect(() => {
    const networks = CRYPTO_NETWORKS[selectedCrypto] || [];
    setNetworkOptions(networks);

    const defaultNetwork = getDefaultNetwork(selectedCrypto);
    if (defaultNetwork) {
      setSelectedNetwork(defaultNetwork.id);
      setCurrentNetworkDetails(defaultNetwork);
      setWithdrawalFee(defaultNetwork.fee);
    }
  }, [selectedCrypto]);

  // Update current network details when selected network changes
  useEffect(() => {
    if (selectedNetwork && networkOptions.length > 0) {
      const networkDetails = networkOptions.find(network => network.id === selectedNetwork);
      setCurrentNetworkDetails(networkDetails);
      if (networkDetails) {
        setWithdrawalFee(networkDetails.fee);
      }
    }
  }, [selectedNetwork, networkOptions]);

  // Handle network selection change
  const handleNetworkChange = (networkId: string) => {
    setSelectedNetwork(networkId);
  };

  // Calculate net amount after fee
  const netAmount = amount ? parseFloat(amount) - withdrawalFee : 0;

  // Process withdrawal with API call
  const processWithdrawal = useCallback(async () => {
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      if (progress <= 90) {
        setUploadProgress(progress);
      }
    }, 200);

    try {
      // Import wallet service dynamically to avoid circular dependencies
      const { default: walletService } = await import('../../services/walletService');

      // Send withdrawal request to backend
      await walletService.withdrawAsset({
        asset: selectedCrypto,
        amount: parseFloat(amount),
        address: walletAddress,
        memo: description || undefined,
        withdrawalType: 'interest', // Default to interest, can be updated based on user selection
        network: selectedNetwork,
        blockchainNetwork: selectedNetwork // Add blockchainNetwork parameter for backend compatibility
      });

      // Complete progress
      clearInterval(interval);
      setUploadProgress(100);

      // Show success message
      toast({
        title: t('withdrawModal.successTitle', 'Transaction Successful'),
        description: t('withdrawModal.successDescription', 'Your withdrawal request has been received. Your transaction will be processed as soon as possible.'),
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      // Reset form and close modal
      setAmount('');
      setWalletAddress('');
      setDescription('');
      setActiveStep(0);
      onClose();
    } catch (error: any) {
      clearInterval(interval);
      setUploadProgress(0);

      // Show error message
      toast({
        title: t('withdrawModal.errorTitle', 'Transaction Failed'),
        description: error.response?.data?.message || t('withdrawModal.errorDescription', 'An error occurred while processing your withdrawal.'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }

    return () => clearInterval(interval);
  }, [t, toast, onClose, amount, selectedCrypto, walletAddress, description, selectedNetwork]);

  // Handle form submission
  const handleSubmit = () => {
    // Validate form
    if (!amount || parseFloat(amount) <= 0) {
      toast({
        title: t('withdrawModal.invalidAmount', 'Invalid Amount'),
        description: t('withdrawModal.enterValidAmount', 'Please enter a valid amount.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    if (parseFloat(amount) > availableBalance) {
      toast({
        title: t('withdrawModal.insufficientBalance', 'Insufficient Balance'),
        description: t('withdrawModal.insufficientBalanceDesc', 'The amount you want to withdraw exceeds your balance.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    if (!walletAddress) {
      toast({
        title: t('withdrawModal.addressRequired', 'Wallet Address Required'),
        description: t('withdrawModal.enterAddress', 'Please enter a valid wallet address.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    // Start submission process
    setIsSubmitting(true);
    processWithdrawal();
  };

  // Next step in the wizard
  const nextStep = () => {
    if (activeStep === 0) {
      // Validate first step
      if (!amount || parseFloat(amount) <= 0) {
        toast({
          title: t('withdrawModal.invalidAmount', 'Invalid Amount'),
          description: t('withdrawModal.enterValidAmount', 'Please enter a valid amount.'),
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      if (parseFloat(amount) > availableBalance) {
        toast({
          title: t('withdrawModal.insufficientBalance', 'Insufficient Balance'),
          description: t('withdrawModal.insufficientBalanceDesc', 'The amount you want to withdraw exceeds your balance.'),
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      // Validate network selection
      if (!selectedNetwork) {
        toast({
          title: t('withdrawModal.networkRequired', 'Network Required'),
          description: t('withdrawModal.selectNetwork', 'Please select a network for your withdrawal.'),
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }
    } else if (activeStep === 1) {
      // Validate second step
      if (!walletAddress) {
        toast({
          title: t('withdrawModal.addressRequired', 'Wallet Address Required'),
          description: t('withdrawModal.enterAddress', 'Please enter a valid wallet address.'),
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }
    }

    setActiveStep(activeStep + 1);
  };

  // Previous step in the wizard
  const prevStep = () => {
    setActiveStep(activeStep - 1);
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="md" isCentered>
      <ModalOverlay backdropFilter="blur(5px)" />
      <ModalContent
        bg="#0B0E11"
        borderColor="#2B3139"
        borderWidth="1px"
        mx={{ base: 2, md: "auto" }}
        maxH={{ base: "calc(100vh - 40px)", md: "calc(100vh - 80px)" }}
        overflowY="auto"
        height="auto"
      >
        <ModalHeader color={primaryColor} borderBottomWidth="1px" borderColor="#2B3139">
          {t('withdrawModal.title', 'Withdrawal Transaction')}
        </ModalHeader>
        <ModalCloseButton color="#EAECEF" />

        <ModalBody py={6}>
          <Tabs index={activeStep} onChange={setActiveStep} variant="unstyled" colorScheme="yellow">
            <TabList mb={4}>
              <Tab
                _selected={{ color: primaryColor, fontWeight: "bold" }}
                color={activeStep === 0 ? primaryColor : "#848E9C"}
                fontWeight={activeStep === 0 ? "bold" : "normal"}
              >
                {t('withdrawModal.step1', '1. Amount Details')}
              </Tab>
              <Tab
                _selected={{ color: primaryColor, fontWeight: "bold" }}
                color={activeStep === 1 ? primaryColor : "#848E9C"}
                fontWeight={activeStep === 1 ? "bold" : "normal"}
                isDisabled={activeStep < 1}
              >
                {t('withdrawModal.step2', '2. Wallet Address')}
              </Tab>
              <Tab
                _selected={{ color: primaryColor, fontWeight: "bold" }}
                color={activeStep === 2 ? primaryColor : "#848E9C"}
                fontWeight={activeStep === 2 ? "bold" : "normal"}
                isDisabled={activeStep < 2}
              >
                {t('withdrawModal.step3', '3. Confirmation')}
              </Tab>
            </TabList>

            <TabPanels>
              {/* Step 1: Amount Details */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <Box p={4} bg="#1E2329" borderRadius="md">
                    <Text color="#848E9C" fontSize="sm">{t('withdrawModal.availableBalance', 'Available Balance')}</Text>
                    <Text fontSize="xl" fontWeight="bold" color="#EAECEF">
                      ${availableBalance.toFixed(2)} USD
                    </Text>
                  </Box>

                  <FormControl isRequired>
                    <FormLabel color="#848E9C" fontSize="sm">{t('withdrawModal.cryptoCurrency', 'Cryptocurrency')}</FormLabel>
                    <Select
                      value={selectedCrypto}
                      onChange={(e) => setSelectedCrypto(e.target.value)}
                      bg="#0B0E11"
                      borderColor="#2B3139"
                      color="#EAECEF"
                      _hover={{ borderColor: "#F0B90B" }}
                    >
                      <option value="BTC">Bitcoin (BTC)</option>
                      <option value="ETH">Ethereum (ETH)</option>
                      <option value="USDT">Tether (USDT)</option>
                      <option value="DOGE">Dogecoin (DOGE)</option>
                      <option value="XRP">XRP</option>
                    </Select>
                  </FormControl>

                  {/* Network Selection */}
                  <NetworkSelector
                    networks={networkOptions}
                    selectedNetwork={selectedNetwork}
                    onChange={handleNetworkChange}
                    isRequired={true}
                    label={t('withdrawModal.network', 'Select Network')}
                    helperText={t('withdrawModal.networkHelperText', `Choose the network for your ${selectedCrypto} withdrawal`)}
                    currency={selectedCrypto}
                  />

                  <FormControl isRequired>
                    <FormLabel color="#848E9C" fontSize="sm">{t('withdrawModal.amount', 'Withdrawal Amount')}</FormLabel>
                    <InputGroup>
                      <NumberInput
                        value={amount}
                        onChange={setAmount}
                        max={availableBalance}
                        min={0.001}
                        precision={2}
                        w="100%"
                      >
                        <NumberInputField
                          placeholder="0.00"
                          bg="#0B0E11"
                          borderColor="#2B3139"
                          color="#EAECEF"
                          _hover={{ borderColor: "#F0B90B" }}
                        />
                        <NumberInputStepper>
                          <NumberIncrementStepper color="#EAECEF" />
                          <NumberDecrementStepper color="#EAECEF" />
                        </NumberInputStepper>
                      </NumberInput>
                    </InputGroup>
                    <FormHelperText color="#848E9C" fontSize="xs">
                      {t('withdrawModal.minAmount', 'Minimum withdrawal amount: $0.001')}
                    </FormHelperText>
                  </FormControl>

                  <HStack spacing={2} mt={2}>
                    <Button
                      size="sm"
                      onClick={() => setAmount((availableBalance * 0.25).toFixed(2))}
                      variant="outline"
                      borderColor="#2B3139"
                      color="#848E9C"
                      _hover={{ borderColor: "#F0B90B" }}
                    >
                      % 25
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => setAmount((availableBalance * 0.5).toFixed(2))}
                      variant="outline"
                      borderColor="#2B3139"
                      color="#848E9C"
                      _hover={{ borderColor: "#F0B90B" }}
                    >
                      % 50
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => setAmount((availableBalance * 0.75).toFixed(2))}
                      variant="outline"
                      borderColor="#2B3139"
                      color="#848E9C"
                      _hover={{ borderColor: "#F0B90B" }}
                    >
                      % 75
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => setAmount(availableBalance.toFixed(2))}
                      variant="outline"
                      borderColor="#2B3139"
                      color="#848E9C"
                      _hover={{ borderColor: "#F0B90B" }}
                    >
                      % 100
                    </Button>
                  </HStack>

                  <Text color="#848E9C" fontSize="xs" mt={2}>
                    {t('withdrawModal.minAmount', 'Minimum withdrawal amount: $0.001')}
                  </Text>
                </VStack>

                <Flex justify="flex-end" mt={6}>
                  <Button
                    bg="#F0B90B"
                    color="#0B0E11"
                    _hover={{ bg: "#F8D12F" }}
                    onClick={nextStep}
                    isDisabled={!amount || parseFloat(amount) <= 0 || parseFloat(amount) > availableBalance || !selectedNetwork}
                    w="100%"
                  >
                    {t('common.next', 'Next')}
                  </Button>
                </Flex>
              </TabPanel>

              {/* Step 2: Wallet Address */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  {currentNetworkDetails && (
                    <Alert status="info" borderRadius="md" mb={4}>
                      <AlertIcon />
                      <Box>
                        <Text fontWeight="bold" color="#EAECEF">
                          {t('withdrawModal.selectedNetwork', 'Selected Network')}
                        </Text>
                        <Text fontSize="sm" color="#EAECEF">
                          {currentNetworkDetails.name} - {currentNetworkDetails.description}
                        </Text>
                        <Text fontSize="xs" color="#EAECEF" mt={1}>
                          {t('withdrawModal.processingTime', 'Processing Time')}: {currentNetworkDetails.processingTime}
                        </Text>
                        <Text fontSize="xs" color="#EAECEF">
                          {t('withdrawModal.networkFee', 'Network Fee')}: {currentNetworkDetails.fee} {selectedCrypto}
                        </Text>
                      </Box>
                    </Alert>
                  )}

                  {currentNetworkDetails?.warningMessage && (
                    <Alert status="warning" borderRadius="md" mb={4}>
                      <AlertIcon />
                      <Box>
                        <Text fontWeight="bold">{t('withdrawModal.networkWarning', 'Important Network Information')}</Text>
                        <Text fontSize="sm">{currentNetworkDetails.warningMessage}</Text>
                      </Box>
                    </Alert>
                  )}

                  <FormControl isRequired>
                    <FormLabel color="#848E9C" fontSize="sm">
                      {t('withdrawModal.walletAddress', `${selectedCrypto} Wallet Address`)}
                    </FormLabel>
                    <Input
                      value={walletAddress}
                      onChange={(e) => setWalletAddress(e.target.value)}
                      placeholder={t('withdrawModal.walletAddressPlaceholder', 'Enter your wallet address')}
                      bg="#0B0E11"
                      borderColor="#2B3139"
                      color="#EAECEF"
                      _hover={{ borderColor: "#F0B90B" }}
                      fontFamily="monospace"
                    />
                    <FormHelperText color="#848E9C" fontSize="xs">
                      {t('withdrawModal.walletAddressHelp', `Please enter a valid ${selectedCrypto} wallet address for the ${currentNetworkDetails?.name || 'selected'} network.`)}
                    </FormHelperText>
                  </FormControl>

                  <Alert status="warning" borderRadius="md" mt={4}>
                    <AlertIcon />
                    <Box>
                      <Text fontWeight="bold" color="#EAECEF">
                        {t('withdrawModal.addressWarning', 'Address Verification')}
                      </Text>
                      <Text fontSize="sm" color="#EAECEF">
                        {t('withdrawModal.addressWarningDetail', 'Please ensure your wallet address is correct. Funds sent to the wrong address cannot be recovered.')}
                      </Text>
                      {currentNetworkDetails && (
                        <Text fontSize="sm" fontWeight="bold" color="#F0B90B" mt={2}>
                          {t('withdrawModal.networkWarningDetail', `Make sure your wallet supports the ${currentNetworkDetails.name}. Using an incompatible wallet may result in permanent loss of funds.`)}
                        </Text>
                      )}
                    </Box>
                  </Alert>
                </VStack>

                <Flex justify="space-between" mt={6}>
                  <Button
                    variant="outline"
                    onClick={prevStep}
                    borderColor="#2B3139"
                    color="#EAECEF"
                    _hover={{ borderColor: "#F0B90B" }}
                    flex="1"
                    mr={2}
                  >
                    {t('common.back', 'Back')}
                  </Button>
                  <Button
                    bg="#F0B90B"
                    color="#0B0E11"
                    _hover={{ bg: "#F8D12F" }}
                    onClick={nextStep}
                    isDisabled={!walletAddress}
                    flex="1"
                    ml={2}
                  >
                    {t('common.next', 'Next')}
                  </Button>
                </Flex>
              </TabPanel>

              {/* Step 3: Confirmation */}
              <TabPanel>
                <VStack spacing={4} align="stretch">
                  <Box p={4} bg="#1E2329" borderRadius="md">
                    <Text color="#848E9C" fontSize="sm" mb={2}>{t('withdrawModal.confirmationTitle', 'Transaction Confirmation')}</Text>

                    <VStack spacing={3} align="stretch">
                      <Flex justify="space-between">
                        <Text color="#848E9C" fontSize="sm">{t('withdrawModal.cryptoCurrency', 'Cryptocurrency')}</Text>
                        <Text color="#EAECEF" fontWeight="bold" fontSize="sm">{selectedCrypto}</Text>
                      </Flex>

                      {currentNetworkDetails && (
                        <Flex justify="space-between">
                          <Text color="#848E9C" fontSize="sm">{t('withdrawModal.network', 'Network')}</Text>
                          <Text color="#EAECEF" fontWeight="bold" fontSize="sm">{currentNetworkDetails.name}</Text>
                        </Flex>
                      )}

                      <Flex justify="space-between">
                        <Text color="#848E9C" fontSize="sm">{t('withdrawModal.amount', 'Amount')}</Text>
                        <Text color="#EAECEF" fontWeight="bold" fontSize="sm">{parseFloat(amount).toFixed(2)} {selectedCrypto}</Text>
                      </Flex>

                      <Flex justify="space-between">
                        <Text color="#848E9C" fontSize="sm">{t('withdrawModal.fee', 'Network Fee')}</Text>
                        <Text color="#EAECEF" fontSize="sm">{withdrawalFee} {selectedCrypto}</Text>
                      </Flex>

                      <Flex justify="space-between">
                        <Text color="#848E9C" fontSize="sm">{t('withdrawModal.netAmount', 'Net Amount')}</Text>
                        <Text color="#F0B90B" fontWeight="bold" fontSize="sm">{netAmount.toFixed(8)} {selectedCrypto}</Text>
                      </Flex>

                      <Divider my={1} borderColor="#2B3139" />

                      <Flex justify="space-between">
                        <Text color="#848E9C" fontSize="sm">{t('withdrawModal.walletAddress', 'Wallet Address')}</Text>
                        <Text color="#EAECEF" fontWeight="bold" fontFamily="monospace" fontSize="xs" maxW="180px" isTruncated>
                          {walletAddress}
                        </Text>
                      </Flex>

                      {currentNetworkDetails && (
                        <Flex justify="space-between">
                          <Text color="#848E9C" fontSize="sm">{t('withdrawModal.processingTime', 'Processing Time')}</Text>
                          <Text color="#EAECEF" fontSize="sm">{currentNetworkDetails.processingTime}</Text>
                        </Flex>
                      )}
                    </VStack>
                  </Box>

                  <Alert status="warning" borderRadius="md" mt={2}>
                    <AlertIcon />
                    <Box>
                      <Text fontSize="xs" color="#EAECEF">
                        {t('withdrawModal.finalWarning', 'Withdrawal cannot be cancelled once confirmed. Please ensure all information is correct.')}
                      </Text>
                      {currentNetworkDetails && (
                        <Text fontSize="xs" fontWeight="bold" color="#F0B90B" mt={1}>
                          {t('withdrawModal.networkFinalWarning', `IMPORTANT: This withdrawal will be processed on the ${currentNetworkDetails.name}. Make sure your receiving wallet supports this network.`)}
                        </Text>
                      )}
                    </Box>
                  </Alert>
                </VStack>

                <Flex justify="space-between" mt={6}>
                  <Button
                    variant="outline"
                    onClick={prevStep}
                    borderColor="#2B3139"
                    color="#EAECEF"
                    _hover={{ borderColor: "#F0B90B" }}
                    flex="1"
                    mr={2}
                  >
                    {t('common.back', 'Back')}
                  </Button>
                  <Button
                    bg="#F0B90B"
                    color="#0B0E11"
                    _hover={{ bg: "#F8D12F" }}
                    onClick={handleSubmit}
                    isLoading={isSubmitting}
                    loadingText={t('common.processing', 'Processing...')}
                    flex="1"
                    ml={2}
                  >
                    {t('withdrawModal.confirmButton', 'Confirm')}
                  </Button>
                </Flex>

                {isSubmitting && (
                  <Box mt={4}>
                    <Text fontSize="xs" color="#848E9C" mb={2}>
                      {t('withdrawModal.processing', 'Processing your transaction...')}
                    </Text>
                    <Progress value={uploadProgress} size="xs" colorScheme="yellow" borderRadius="full" />
                  </Box>
                )}
              </TabPanel>
            </TabPanels>
          </Tabs>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default WithdrawModal;
