import { Request, Response, NextFunction } from 'express';
import { body, validationResult } from 'express-validator';

// Supported currencies
const SUPPORTED_CURRENCIES = ['BTC', 'ETH', 'USDT', 'BNB', 'ADA', 'DOT', 'LINK', 'UNI'];

/**
 * Validation middleware for investment package creation
 */
export const validateInvestmentPackage = [
  body('amount')
    .isNumeric()
    .withMessage('Amount must be a number')
    .isFloat({ min: 0.000001 })
    .withMessage('Amount must be greater than 0')
    .custom((value) => {
      if (value > 1000000) {
        throw new Error('Amount cannot exceed 1,000,000');
      }
      return true;
    }),

  body('currency')
    .optional()
    .isString()
    .withMessage('Currency must be a string')
    .isLength({ min: 2, max: 10 })
    .withMessage('Currency must be between 2-10 characters')
    .custom((value) => {
      if (value && !SUPPORTED_CURRENCIES.includes(value.toUpperCase())) {
        throw new Error(`Currency must be one of: ${SUPPORTED_CURRENCIES.join(', ')}`);
      }
      return true;
    }),

  body('compoundEnabled')
    .optional()
    .isBoolean()
    .withMessage('Compound enabled must be a boolean'),

  // Validation result handler
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        status: 'error',
        message: 'Validation failed',
        errors: errors.array().map(error => ({
          field: (error as any).param || (error as any).path,
          message: error.msg,
          value: (error as any).value
        }))
      });
      return;
    }
    next();
  }
];

/**
 * Validation middleware for withdrawal requests
 */
export const validateWithdrawal = [
  body('amount')
    .isNumeric()
    .withMessage('Amount must be a number')
    .isFloat({ min: 0.000001 })
    .withMessage('Amount must be greater than 0')
    .custom((value) => {
      if (value > 100000) {
        throw new Error('Single withdrawal cannot exceed 100,000');
      }
      return true;
    }),

  body('currency')
    .optional()
    .isString()
    .withMessage('Currency must be a string')
    .isLength({ min: 2, max: 10 })
    .withMessage('Currency must be between 2-10 characters')
    .custom((value) => {
      if (value && !SUPPORTED_CURRENCIES.includes(value.toUpperCase())) {
        throw new Error(`Currency must be one of: ${SUPPORTED_CURRENCIES.join(', ')}`);
      }
      return true;
    }),

  body('emergency')
    .optional()
    .isBoolean()
    .withMessage('Emergency flag must be a boolean'),

  body('walletAddress')
    .optional()
    .isString()
    .withMessage('Wallet address must be a string')
    .isLength({ min: 10, max: 100 })
    .withMessage('Wallet address must be between 10-100 characters'),

  // Validation result handler
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        status: 'error',
        message: 'Validation failed',
        errors: errors.array().map(error => ({
          field: (error as any).param || (error as any).path,
          message: error.msg,
          value: (error as any).value
        }))
      });
      return;
    }
    next();
  }
];

/**
 * Validation middleware for admin operations
 */
export const validateAdminPackageUpdate = [
  body('status')
    .optional()
    .isIn(['pending', 'active', 'completed', 'withdrawn'])
    .withMessage('Status must be one of: pending, active, completed, withdrawn'),

  body('interestRate')
    .optional()
    .isFloat({ min: 0, max: 1 })
    .withMessage('Interest rate must be between 0 and 1'),

  body('totalEarned')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Total earned must be non-negative'),

  // Validation result handler
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        status: 'error',
        message: 'Validation failed',
        errors: errors.array().map(error => ({
          field: (error as any).param || (error as any).path,
          message: error.msg,
          value: (error as any).value
        }))
      });
      return;
    }
    next();
  }
];

/**
 * Validation middleware for time service operations
 */
export const validateTimeOperation = [
  body('durationHours')
    .optional()
    .isInt({ min: 1, max: 168 }) // Max 1 week
    .withMessage('Duration must be between 1 and 168 hours'),

  body('durationMinutes')
    .optional()
    .isInt({ min: 1, max: 1440 }) // Max 24 hours
    .withMessage('Duration must be between 1 and 1440 minutes'),

  body('reason')
    .optional()
    .isString()
    .withMessage('Reason must be a string')
    .isLength({ min: 5, max: 200 })
    .withMessage('Reason must be between 5-200 characters'),

  // Validation result handler
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        status: 'error',
        message: 'Validation failed',
        errors: errors.array().map(error => ({
          field: (error as any).param || (error as any).path,
          message: error.msg,
          value: (error as any).value
        }))
      });
      return;
    }
    next();
  }
];

/**
 * Validation middleware for crypto API operations
 */
export const validateCurrencyConversion = [
  body('amount')
    .isNumeric()
    .withMessage('Amount must be a number')
    .isFloat({ min: 0.000001 })
    .withMessage('Amount must be greater than 0'),

  body('fromCurrency')
    .isString()
    .withMessage('From currency must be a string')
    .custom((value) => {
      if (!SUPPORTED_CURRENCIES.includes(value.toUpperCase())) {
        throw new Error(`From currency must be one of: ${SUPPORTED_CURRENCIES.join(', ')}`);
      }
      return true;
    }),

  body('toCurrency')
    .isString()
    .withMessage('To currency must be a string')
    .custom((value) => {
      if (!SUPPORTED_CURRENCIES.includes(value.toUpperCase())) {
        throw new Error(`To currency must be one of: ${SUPPORTED_CURRENCIES.join(', ')}`);
      }
      return true;
    }),

  // Validation result handler
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        status: 'error',
        message: 'Validation failed',
        errors: errors.array().map(error => ({
          field: (error as any).param || (error as any).path,
          message: error.msg,
          value: (error as any).value
        }))
      });
      return;
    }
    next();
  }
];

/**
 * General purpose validation error handler
 */
export const handleValidationErrors = (req: Request, res: Response, next: NextFunction): void => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({
      status: 'error',
      message: 'Validation failed',
      errors: errors.array().map(error => ({
        field: (error as any).param || (error as any).path,
        message: error.msg,
        value: (error as any).value
      }))
    });
    return;
  }
  next();
};

/**
 * Custom validation for package ID parameter
 */
export const validatePackageId = (req: Request, res: Response, next: NextFunction) => {
  const { packageId } = req.params;

  if (!packageId || !packageId.match(/^[0-9a-fA-F]{24}$/)) {
    return res.status(400).json({
      status: 'error',
      message: 'Invalid package ID format'
    });
  }

  next();
};

/**
 * Rate limiting validation for sensitive operations
 */
export const validateRateLimit = (maxRequests: number = 5, windowMs: number = 60000) => {
  const requests = new Map<string, { count: number; resetTime: number }>();

  return (req: Request, res: Response, next: NextFunction) => {
    const clientId = req.ip + (req.user?._id || 'anonymous');
    const now = Date.now();

    const clientData = requests.get(clientId);

    if (!clientData || now > clientData.resetTime) {
      requests.set(clientId, { count: 1, resetTime: now + windowMs });
      return next();
    }

    if (clientData.count >= maxRequests) {
      return res.status(429).json({
        status: 'error',
        message: 'Too many requests, please try again later',
        retryAfter: Math.ceil((clientData.resetTime - now) / 1000)
      });
    }

    clientData.count++;
    next();
  };
};
