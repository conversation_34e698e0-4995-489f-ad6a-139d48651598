FROM node:20-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci

# Copy source code and dist directory
COPY . .

# Create uploads directory with proper permissions
RUN mkdir -p uploads/receipts && \
    chmod -R 777 uploads

# Expose port
EXPOSE 5000

# Set environment to production
ENV NODE_ENV=production

# Start production server
CMD ["npm", "start"]
