version: '3.8'

services:
  mongodb:
    image: mongo:latest
    container_name: cryptoyield-mongodb
    restart: always
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_USER}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD}
    volumes:
      - mongodb_data:/data/db
    networks:
      - cryptoyield-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: cryptoyield-backend
    restart: always
    depends_on:
      mongodb:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - MONGO_URI=mongodb://${MONGO_USER}:${MONGO_PASSWORD}@mongodb:27017/cryptoyield?authSource=admin
      - JWT_SECRET=${JWT_SECRET}
      - PORT=5000
      - FRONTEND_URL=${FRONTEND_URL}
      - CONTRACT_ADDRESS=${CONTRACT_ADDRESS}
      - PROVIDER_URL=${PROVIDER_URL}
    volumes:
      - uploads_data:/app/uploads
    networks:
      - cryptoyield-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5000/health"]
      interval: 10s
      timeout: 5s
      retries: 5

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - API_URL=${API_URL}
        - CONTRACT_ADDRESS=${CONTRACT_ADDRESS}
        - INFURA_ID=${INFURA_ID}
        - STORAGE_KEY=${STORAGE_KEY}
        - SOCKET_URL=${SOCKET_URL}
    container_name: cryptoyield-frontend
    restart: always
    depends_on:
      backend:
        condition: service_healthy
      socket-server:
        condition: service_started
    ports:
      - "80:80"
    networks:
      - cryptoyield-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:80/health"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M



  prometheus:
    image: prom/prometheus:latest
    container_name: cryptoyield-prometheus
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
    ports:
      - "9090:9090"
    networks:
      - cryptoyield-network

  grafana:
    image: grafana/grafana:latest
    container_name: cryptoyield-grafana
    volumes:
      - grafana_data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - "3000:3000"
    networks:
      - cryptoyield-network

  node-exporter:
    image: prom/node-exporter:latest
    container_name: cryptoyield-node-exporter
    restart: always
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - cryptoyield-network

  mongodb-exporter:
    image: percona/mongodb_exporter:0.44.0
    container_name: cryptoyield-mongodb-exporter
    command:
      - '--mongodb.uri=mongodb://${MONGO_USER}:${MONGO_PASSWORD}@mongodb:27017/admin'
    depends_on:
      - mongodb
    networks:
      - cryptoyield-network

  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: cryptoyield-cadvisor
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    networks:
      - cryptoyield-network

  alertmanager:
    image: prom/alertmanager:latest
    container_name: cryptoyield-alertmanager
    volumes:
      - ./alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - alertmanager_data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
    ports:
      - "9093:9093"
    networks:
      - cryptoyield-network

  nginx-exporter:
    image: nginx/nginx-prometheus-exporter:latest
    container_name: cryptoyield-nginx-exporter
    command:
      - -nginx.scrape-uri=http://frontend:80/nginx_status
    depends_on:
      - frontend
    networks:
      - cryptoyield-network

  contract-metrics:
    build:
      context: ./contracts
      dockerfile: Dockerfile.metrics
    container_name: cryptoyield-contract-metrics
    restart: always
    environment:
      - PROVIDER_URL=${PROVIDER_URL}
      - CONTRACT_ADDRESS=${CONTRACT_ADDRESS}
    networks:
      - cryptoyield-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/metrics"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  cryptoyield-network:
    driver: bridge

volumes:
  mongodb_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  alertmanager_data:
    driver: local
  uploads_data:
    driver: local