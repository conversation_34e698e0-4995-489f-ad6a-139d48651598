import NodeCache from 'node-cache';
import { logger } from '../utils/logger';

class CacheService {
  private static instance: CacheService;
  private cache: NodeCache;

  private constructor() {
    // Standard TTL: 10 minutes, check period: 60 seconds
    this.cache = new NodeCache({
      stdTTL: 600,
      checkperiod: 60,
      useClones: false // For better performance
    });

    // Log cache statistics periodically
    setInterval(() => {
      const stats = this.cache.getStats();
      logger.debug('Cache statistics:', stats);
    }, 300000); // Every 5 minutes
  }

  public static getInstance(): CacheService {
    if (!CacheService.instance) {
      CacheService.instance = new CacheService();
    }
    return CacheService.instance;
  }

  /**
   * Get a value from cache
   * @param key Cache key
   * @returns Cached value or undefined if not found
   */
  public get<T>(key: string): T | undefined {
    return this.cache.get<T>(key);
  }

  /**
   * Set a value in cache
   * @param key Cache key
   * @param value Value to cache
   * @param ttl Time to live in seconds (optional)
   * @returns true if successful
   */
  public set<T>(key: string, value: T, ttl?: number): boolean {
    return this.cache.set(key, value, ttl || 0);
  }

  /**
   * Delete a value from cache
   * @param key Cache key
   * @returns true if successful
   */
  public delete(key: string): boolean {
    return this.cache.del(key) > 0;
  }

  /**
   * Clear all cache
   */
  public clear(): void {
    this.cache.flushAll();
  }

  /**
   * Get or set cache value with a factory function
   * @param key Cache key
   * @param factory Function to generate value if not in cache
   * @param ttl Time to live in seconds (optional)
   * @returns Cached or newly generated value
   */
  public async getOrSet<T>(key: string, factory: () => Promise<T>, ttl?: number): Promise<T> {
    const cachedValue = this.get<T>(key);
    if (cachedValue !== undefined) {
      return cachedValue;
    }

    try {
      const value = await factory();
      this.set(key, value, ttl);
      return value;
    } catch (error) {
      logger.error(`Error generating cached value for key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Invalidate cache keys by prefix
   * @param prefix Key prefix to invalidate
   * @returns Number of keys removed
   */
  public invalidateByPrefix(prefix: string): number {
    const keys = this.cache.keys().filter(key => key.startsWith(prefix));
    return this.cache.del(keys);
  }
}

export const cacheService = CacheService.getInstance();
