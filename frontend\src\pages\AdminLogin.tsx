import { useState, useRef, useEffect } from 'react';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  Stack,
  Heading,
  Text,
  Alert,
  AlertIcon,
  Container,
  Flex,
  InputGroup,
  InputRightElement,
  Icon,
  useToast,
  FormHelperText,
  HStack,
  VStack,
  Divider,
  keyframes,
} from '@chakra-ui/react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  FaEye,
  FaEyeSlash,
  FaShieldAlt,
  FaLock,
  FaEnvelope,
  FaUserShield,
  FaCrown,
  FaKey
} from 'react-icons/fa';
import useAuth from '../hooks/useAuth';

// Enhanced modern animations for Admin Login with updated color scheme
const float = keyframes`
  0% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(5px) rotate(-1deg); }
  100% { transform: translateY(0px) rotate(0deg); }
`;

const glow = keyframes`
  0% { box-shadow: 0 0 20px rgba(252, 213, 53, 0.3); }
  50% { box-shadow: 0 0 30px rgba(252, 213, 53, 0.6); }
  100% { box-shadow: 0 0 20px rgba(252, 213, 53, 0.3); }
`;

const shimmer = keyframes`
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
`;

const pulse = keyframes`
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
`;

const AdminLogin = () => {
  const { t } = useTranslation();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [captchaValue, setCaptchaValue] = useState('');
  const [captchaAnswer, setCaptchaAnswer] = useState('');
  const [captchaError, setCaptchaError] = useState(false);

  const { user, adminLogin, loading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const toast = useToast();
  const captchaRef = useRef<HTMLInputElement>(null);

  // Redirect if user is already logged in as admin
  useEffect(() => {
    if (user && user.isAdmin) {
      // Get the redirect path from location state or default to admin dashboard
      const from = location.state?.from?.pathname || '/admin';

      console.log('Admin already logged in, redirecting to:', from);

      // Show notification
      toast({
        title: t('adminLogin.alreadyLoggedIn', 'Already Logged In'),
        description: t('adminLogin.redirectingToAdmin', 'You are already logged in as admin. Redirecting...'),
        status: 'info',
        duration: 3000,
        isClosable: true,
      });

      // Navigate to admin dashboard or the page they were trying to access
      navigate(from, { replace: true });
    }
  }, [user, navigate, location, toast, t]);

  // Generate CAPTCHA
  const generateCaptcha = () => {
    const num1 = Math.floor(Math.random() * 10);
    const num2 = Math.floor(Math.random() * 10);
    setCaptchaValue(`${num1} + ${num2} = ?`);
    setCaptchaAnswer((num1 + num2).toString());
  };

  // Generate CAPTCHA on component mount
  useEffect(() => {
    generateCaptcha();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    setCaptchaError(false);

    // Check email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError(t('login.invalidEmail', 'Please enter a valid email address'));
      return;
    }

    // Check password
    if (!password) {
      setError(t('login.invalidPassword', 'Please enter a valid password'));
      return;
    }

    // CAPTCHA verification
    const userCaptchaAnswer = captchaRef.current?.value;
    if (!userCaptchaAnswer || userCaptchaAnswer !== captchaAnswer) {
      setCaptchaError(true);
      setError(t('login.invalidCaptcha', 'CAPTCHA verification failed'));
      generateCaptcha(); // Generate new CAPTCHA
      if (captchaRef.current) captchaRef.current.value = '';
      return;
    }

    try {
      // Call the admin login function from AuthContext
      await adminLogin(email, password);

      // Show successful login notification
      toast({
        title: t('adminLogin.successTitle', 'Admin Login Successful!'),
        description: t('adminLogin.successDescription', 'Welcome to the admin panel!'),
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      setSuccess(t('adminLogin.loginSuccessful', 'Login successful! Redirecting to admin dashboard...'));

      // Get the redirect path from location state or default to admin dashboard
      const from = location.state?.from?.pathname || '/admin';

      // Navigate to admin dashboard after a short delay
      setTimeout(() => {
        navigate(from);
      }, 1500);
    } catch (error: any) {
      console.error('Admin login error:', error);

      // Display the error message from the backend if available
      const errorMessage = error.response?.data?.message ||
                          error.message ||
                          t('adminLogin.loginFailed', 'Login failed. Please check your credentials.');
      setError(errorMessage);

      // Show error notification
      toast({
        title: t('adminLogin.errorTitle', 'Login Failed'),
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  return (
    <Box
      minH="100vh"
      bgGradient="linear(135deg, #0B0E11 0%, #1A1D29 50%, #0B0E11 100%)"
      position="relative"
      overflow="hidden"
    >
      {/* Enhanced animated background elements with admin theme and mobile optimization */}
      <Box
        position="absolute"
        top="15%"
        left="15%"
        w={{ base: "60px", md: "80px" }}
        h={{ base: "60px", md: "80px" }}
        borderRadius="full"
        bg="linear-gradient(45deg, rgba(252, 213, 53, 0.15), rgba(252, 213, 53, 0.08))"
        animation={`${float} 7s ease-in-out infinite`}
        zIndex={0}
      />
      <Box
        position="absolute"
        top="65%"
        right="20%"
        w={{ base: "45px", md: "60px" }}
        h={{ base: "45px", md: "60px" }}
        borderRadius="full"
        bg="linear-gradient(45deg, rgba(252, 213, 53, 0.12), rgba(252, 213, 53, 0.06))"
        animation={`${float} 9s ease-in-out infinite reverse`}
        zIndex={0}
      />
      <Box
        position="absolute"
        top="30%"
        right="10%"
        w={{ base: "30px", md: "40px" }}
        h={{ base: "30px", md: "40px" }}
        borderRadius="full"
        bg="linear-gradient(45deg, rgba(252, 213, 53, 0.08), rgba(252, 213, 53, 0.04))"
        animation={`${float} 11s ease-in-out infinite`}
        zIndex={0}
        display={{ base: "none", sm: "block" }}
      />

      <Container
        maxW={{ base: "100%", sm: "400px", md: "450px", lg: "lg" }}
        px={{ base: 4, sm: 6, md: 8 }}
        py={{ base: 6, md: 16 }}
        position="relative"
        zIndex={1}
      >
        <VStack spacing={8} align="center">
          {/* Modern Admin Header */}
          <VStack spacing={6} textAlign="center">
            <Box position="relative">
              <Flex align="center" justify="center" mb={4}>
                <Box
                  w="52px"
                  h="52px"
                  bg="linear-gradient(135deg, #FCD535 0%, #F8D12F 100%)"
                  borderRadius="xl"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  boxShadow="0 8px 32px rgba(252, 213, 53, 0.4)"
                  animation={`${glow} 3s ease-in-out infinite`}
                  position="relative"
                  _before={{
                    content: '""',
                    position: 'absolute',
                    top: '-2px',
                    left: '-2px',
                    right: '-2px',
                    bottom: '-2px',
                    borderRadius: 'xl',
                    background: 'linear-gradient(45deg, #FCD535, #F8D12F, #FCD535)',
                    backgroundSize: '200% 200%',
                    animation: `${shimmer} 2s linear infinite`,
                    zIndex: -1,
                  }}
                >
                  <Icon as={FaCrown} color="#0B0E11" boxSize={6} />
                </Box>
              </Flex>
              <Heading
                fontSize={{ base: '2xl', md: '3xl' }}
                fontWeight="800"
                bgGradient="linear(135deg, #FCD535 0%, #F8D12F 50%, #FCD535 100%)"
                bgClip="text"
                letterSpacing="tight"
                mb={2}
              >
                Shipping Finance
              </Heading>
              <Text
                fontSize={{ base: 'lg', md: 'xl' }}
                color="#EAECEF"
                fontWeight="600"
                opacity={0.9}
                mb={1}
              >
                {t('adminLogin.title', 'Admin Portal')}
              </Text>
              <Text
                fontSize="sm"
                color="#848E9C"
                opacity={0.8}
              >
                {t('adminLogin.subtitle', 'Secure administrative access')}
              </Text>
            </Box>
          </VStack>

          {/* Modern Admin Glassmorphism Card */}
          <Box
            w="full"
            maxW="420px"
            bg="rgba(30, 35, 41, 0.85)"
            backdropFilter="blur(20px)"
            borderRadius="2xl"
            borderWidth="1px"
            borderColor="rgba(252, 213, 53, 0.25)"
            boxShadow="0 25px 50px -12px rgba(0, 0, 0, 0.6), 0 0 0 1px rgba(252, 213, 53, 0.15)"
            p={{ base: 6, md: 8 }}
            position="relative"
            _before={{
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              borderRadius: '2xl',
              background: 'linear-gradient(135deg, rgba(252, 213, 53, 0.12) 0%, transparent 50%, rgba(252, 213, 53, 0.06) 100%)',
              pointerEvents: 'none',
            }}
          >
          {error && (
            <Alert status="error" mb={4} borderRadius="md">
              <AlertIcon />
              {error}
            </Alert>
          )}
          {success && (
            <Alert status="success" mb={4} borderRadius="md">
              <AlertIcon />
              {success}
            </Alert>
          )}
            <form onSubmit={handleSubmit}>
              <VStack spacing={6} position="relative" zIndex={1}>
                {/* Modern Admin Email Field */}
                <FormControl id="email" isRequired>
                  <FormLabel
                    color="#EAECEF"
                    fontSize="sm"
                    fontWeight="600"
                    mb={3}
                  >
                    <HStack spacing={2}>
                      <Box
                        p={1}
                        borderRadius="md"
                        bg="rgba(252, 213, 53, 0.15)"
                      >
                        <Icon as={FaEnvelope} color="#FCD535" boxSize={3} />
                      </Box>
                      <Text>{t('common.email', 'Admin Email')}</Text>
                    </HStack>
                  </FormLabel>
                  <Input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    bg="rgba(11, 14, 17, 0.7)"
                    borderColor="#2B3139"
                    borderWidth="1px"
                    color="#EAECEF"
                    placeholder="<EMAIL>"
                    _placeholder={{ color: '#848E9C' }}
                    _hover={{
                      borderColor: "rgba(252, 213, 53, 0.7)",
                      boxShadow: "0 0 0 1px rgba(252, 213, 53, 0.3)"
                    }}
                    _focus={{
                      borderColor: "#FCD535",
                      boxShadow: "0 0 0 3px rgba(252, 213, 53, 0.15)",
                      bg: "rgba(11, 14, 17, 0.9)"
                    }}
                    size="lg"
                    borderRadius="xl"
                    fontSize="md"
                    transition="all 0.3s ease"
                  />
                </FormControl>

                {/* Modern Admin Password Field */}
                <FormControl id="password" isRequired>
                  <FormLabel
                    color="#EAECEF"
                    fontSize="sm"
                    fontWeight="600"
                    mb={3}
                  >
                    <HStack spacing={2}>
                      <Box
                        p={1}
                        borderRadius="md"
                        bg="rgba(252, 213, 53, 0.15)"
                      >
                        <Icon as={FaLock} color="#FCD535" boxSize={3} />
                      </Box>
                      <Text>{t('common.password', 'Admin Password')}</Text>
                    </HStack>
                  </FormLabel>
                  <InputGroup size="lg">
                    <Input
                      type={showPassword ? 'text' : 'password'}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      bg="rgba(11, 14, 17, 0.7)"
                      borderColor="#2B3139"
                      borderWidth="1px"
                      color="#EAECEF"
                      placeholder="Enter your admin password"
                      _placeholder={{ color: '#848E9C' }}
                      _hover={{
                        borderColor: "rgba(252, 213, 53, 0.7)",
                        boxShadow: "0 0 0 1px rgba(252, 213, 53, 0.3)"
                      }}
                      _focus={{
                        borderColor: "#FCD535",
                        boxShadow: "0 0 0 3px rgba(252, 213, 53, 0.15)",
                        bg: "rgba(11, 14, 17, 0.9)"
                      }}
                      borderRadius="xl"
                      fontSize="md"
                      transition="all 0.3s ease"
                    />
                    <InputRightElement width="3rem">
                      <Button
                        h="2rem"
                        size="sm"
                        onClick={() => setShowPassword(!showPassword)}
                        bg="transparent"
                        _hover={{
                          bg: "rgba(252, 213, 53, 0.15)",
                          transform: "scale(1.1)"
                        }}
                        _active={{ transform: "scale(0.95)" }}
                        color="#848E9C"
                        borderRadius="lg"
                        transition="all 0.2s ease"
                      >
                        <Icon as={showPassword ? FaEyeSlash : FaEye} />
                      </Button>
                    </InputRightElement>
                  </InputGroup>
                </FormControl>

                {/* Modern Admin CAPTCHA Section */}
                <Box
                  bg="rgba(252, 213, 53, 0.04)"
                  borderRadius="xl"
                  borderWidth="1px"
                  borderColor="rgba(252, 213, 53, 0.2)"
                  p={5}
                  position="relative"
                  _before={{
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    borderRadius: 'xl',
                    background: 'linear-gradient(135deg, rgba(252, 213, 53, 0.08) 0%, transparent 50%, rgba(252, 213, 53, 0.03) 100%)',
                    pointerEvents: 'none',
                  }}
                >
                  <HStack mb={4} spacing={3}>
                    <Box
                      bg="linear-gradient(135deg, rgba(252, 213, 53, 0.25), rgba(252, 213, 53, 0.15))"
                      p={2}
                      borderRadius="lg"
                      display="flex"
                      alignItems="center"
                      justifyContent="center"
                    >
                      <Icon as={FaShieldAlt} color="#FCD535" boxSize={4} />
                    </Box>
                    <VStack align="start" spacing={0}>
                      <Text color="#EAECEF" fontSize="sm" fontWeight="600">
                        {t('login.captcha', 'Admin Security Verification')}
                      </Text>
                      <Text color="#848E9C" fontSize="xs">
                        {t('login.captchaSubtitle', 'Enhanced protection for admin access')}
                      </Text>
                    </VStack>
                  </HStack>

                  <FormControl isRequired isInvalid={captchaError}>
                    <Box
                      bg="rgba(11, 14, 17, 0.7)"
                      borderRadius="lg"
                      borderWidth="1px"
                      borderColor={captchaError ? "red.400" : "rgba(43, 49, 57, 0.8)"}
                      p={4}
                      mb={4}
                      textAlign="center"
                      position="relative"
                      _before={{
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        borderRadius: 'lg',
                        background: captchaError
                          ? 'linear-gradient(135deg, rgba(248, 73, 96, 0.1), transparent)'
                          : 'linear-gradient(135deg, rgba(252, 213, 53, 0.08), transparent)',
                        pointerEvents: 'none',
                      }}
                    >
                      <Text
                        fontSize="xl"
                        fontWeight="bold"
                        letterSpacing="wider"
                        color="#EAECEF"
                        fontFamily="monospace"
                        textShadow="2px 2px 4px rgba(0,0,0,0.5)"
                        position="relative"
                        zIndex={1}
                      >
                        {captchaValue}
                      </Text>
                    </Box>

                    <InputGroup size="lg">
                      <Input
                        ref={captchaRef}
                        placeholder={t('login.captchaPlaceholder', 'Enter the result')}
                        bg="rgba(11, 14, 17, 0.7)"
                        borderColor={captchaError ? "red.400" : "rgba(43, 49, 57, 0.8)"}
                        borderWidth="1px"
                        color="#EAECEF"
                        textAlign="center"
                        fontSize="lg"
                        fontWeight="600"
                        _placeholder={{ color: '#848E9C' }}
                        _hover={{
                          borderColor: captchaError ? "red.300" : "rgba(252, 213, 53, 0.7)",
                          boxShadow: captchaError
                            ? "0 0 0 1px rgba(248, 73, 96, 0.3)"
                            : "0 0 0 1px rgba(252, 213, 53, 0.3)"
                        }}
                        _focus={{
                          borderColor: captchaError ? "red.400" : "#FCD535",
                          boxShadow: captchaError
                            ? "0 0 0 3px rgba(248, 73, 96, 0.15)"
                            : "0 0 0 3px rgba(252, 213, 53, 0.15)",
                          bg: "rgba(11, 14, 17, 0.9)"
                        }}
                        borderRadius="xl"
                        transition="all 0.3s ease"
                      />
                      <InputRightElement width="4rem">
                        <Button
                          h="2.5rem"
                          size="sm"
                          onClick={generateCaptcha}
                          bg="transparent"
                          _hover={{
                            bg: "rgba(252, 213, 53, 0.15)",
                            transform: "rotate(180deg)"
                          }}
                          _active={{ transform: "scale(0.95)" }}
                          color="#848E9C"
                          borderRadius="lg"
                          transition="all 0.3s ease"
                          title={t('login.refreshCaptcha', 'Refresh')}
                        >
                          <Icon as={FaShieldAlt} />
                        </Button>
                      </InputRightElement>
                    </InputGroup>

                    {captchaError && (
                      <FormHelperText color="#F84960" mt={2} fontSize="sm">
                        <Icon as={FaShieldAlt} mr={1} />
                        {t('login.captchaError', 'Verification failed. Please try again.')}
                      </FormHelperText>
                    )}
                  </FormControl>
                </Box>

                {/* Modern Admin Login Button */}
                <Button
                  type="submit"
                  bg="linear-gradient(135deg, #FCD535 0%, #F8D12F 100%)"
                  color="#0B0E11"
                  _hover={{
                    bg: "linear-gradient(135deg, #F8D12F 0%, #FCD535 100%)",
                    transform: "translateY(-3px)",
                    boxShadow: "0 12px 30px rgba(252, 213, 53, 0.5)"
                  }}
                  _active={{
                    transform: "translateY(-1px)",
                    boxShadow: "0 6px 20px rgba(252, 213, 53, 0.4)"
                  }}
                  transition="all 0.3s ease"
                  isLoading={loading}
                  loadingText="Authenticating..."
                  size="lg"
                  borderRadius="xl"
                  fontWeight="700"
                  fontSize="md"
                  w="full"
                  h="56px"
                  position="relative"
                  _before={{
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    borderRadius: 'xl',
                    background: 'linear-gradient(135deg, rgba(255,255,255,0.2), transparent)',
                    pointerEvents: 'none',
                  }}
                >
                  <HStack spacing={2}>
                    <Icon as={FaUserShield} />
                    <Text>{t('adminLogin.loginButton', 'Admin Access')}</Text>
                  </HStack>
                </Button>

                {/* Admin Security Notice */}
                <VStack spacing={2} pt={4}>
                  <Divider borderColor="rgba(252, 213, 53, 0.2)" />
                  <HStack spacing={2} opacity={0.8}>
                    <Icon as={FaShieldAlt} color="#FCD535" boxSize={3} />
                    <Text fontSize="xs" color="#848E9C" textAlign="center">
                      {t('adminLogin.securityNotice', 'This is a secure admin portal. All activities are logged.')}
                    </Text>
                  </HStack>
                </VStack>
              </VStack>
            </form>
          </Box>
        </VStack>
      </Container>
    </Box>
  );
};

export default AdminLogin;
