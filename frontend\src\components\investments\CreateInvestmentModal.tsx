import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>lay,
  ModalContent,
  Modal<PERSON>eader,
  Modal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  Select,
  VStack,
  HStack,
  Text,
  Alert,
  AlertIcon,
  Switch,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Divider,
  Badge,
  useToast,
  Box,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText
} from '@chakra-ui/react';
import { FaCoins, FaCalculator, FaClock } from 'react-icons/fa';

interface CreateInvestmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const SUPPORTED_CURRENCIES = [
  { value: 'USDT', label: 'USDT (Tether)', rate: 1 },
  { value: 'BTC', label: 'Bitcoin', rate: 45000 },
  { value: 'ETH', label: 'Ethereum', rate: 3000 },
  { value: 'BNB', label: 'Binance Coin', rate: 300 },
  { value: 'ADA', label: 'Cardano', rate: 0.5 },
  { value: 'DOT', label: 'Polkadot', rate: 8 },
  { value: 'LINK', label: 'Chainlink', rate: 15 },
  { value: 'UNI', label: 'Uniswap', rate: 7 }
];

const CreateInvestmentModal: React.FC<CreateInvestmentModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const [amount, setAmount] = useState<number>(100);
  const [currency, setCurrency] = useState<string>('USDT');
  const [compoundEnabled, setCompoundEnabled] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  
  const toast = useToast();

  // Get selected currency info
  const selectedCurrency = SUPPORTED_CURRENCIES.find(c => c.value === currency);
  const usdtValue = selectedCurrency ? amount * selectedCurrency.rate : 0;
  
  // Calculate interest rates
  const dailyInterestRate = usdtValue >= 100000 ? 1.2 : 1.0; // 1.2% for 100k+ USDT
  const dailyEarnings = (usdtValue * dailyInterestRate) / 100;
  const monthlyEarnings = dailyEarnings * 30;
  const yearlyEarnings = dailyEarnings * 365;

  // Calculate activation time (next 03:00 Turkey time)
  const getActivationTime = () => {
    const now = new Date();
    const turkeyTime = new Date(now.getTime() + (3 * 60 * 60 * 1000)); // UTC+3
    const nextActivation = new Date(turkeyTime);
    nextActivation.setHours(3, 0, 0, 0);
    
    // If current time is after 03:00, activate tomorrow at 03:00
    if (turkeyTime.getHours() >= 3) {
      nextActivation.setDate(nextActivation.getDate() + 1);
    }
    
    return nextActivation;
  };

  const activationTime = getActivationTime();
  const timeUntilActivation = activationTime.getTime() - Date.now();
  const hoursUntilActivation = Math.floor(timeUntilActivation / (1000 * 60 * 60));
  const minutesUntilActivation = Math.floor((timeUntilActivation % (1000 * 60 * 60)) / (1000 * 60));

  const handleSubmit = async () => {
    if (amount <= 0) {
      toast({
        title: 'Hata',
        description: 'Yatırım miktarı 0\'dan büyük olmalıdır',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    if (usdtValue < 1) {
      toast({
        title: 'Hata',
        description: 'Minimum yatırım tutarı 1 USDT eşdeğeridir',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    setLoading(true);

    try {
      const response = await fetch('/api/investment-packages/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          amount,
          currency,
          compoundEnabled
        })
      });

      const data = await response.json();

      if (response.ok) {
        toast({
          title: 'Başarılı!',
          description: `${amount} ${currency} yatırım paketi oluşturuldu`,
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
        
        onSuccess();
        onClose();
        
        // Reset form
        setAmount(100);
        setCurrency('USDT');
        setCompoundEnabled(false);
      } else {
        toast({
          title: 'Hata',
          description: data.message || 'Yatırım paketi oluşturulamadı',
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      }
    } catch (error) {
      console.error('Error creating investment:', error);
      toast({
        title: 'Hata',
        description: 'Yatırım paketi oluşturulurken hata oluştu',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="xl" closeOnOverlayClick={!loading}>
      <ModalOverlay bg="blackAlpha.800" />
      <ModalContent bg="gray.800" borderColor="gold.400" borderWidth="1px">
        <ModalHeader color="white">
          <HStack spacing={2}>
            <FaCoins color="#FCD535" />
            <Text>Yeni Yatırım Paketi Oluştur</Text>
          </HStack>
        </ModalHeader>
        <ModalCloseButton color="white" isDisabled={loading} />
        
        <ModalBody>
          <VStack spacing={6} align="stretch">
            {/* Investment Amount */}
            <FormControl>
              <FormLabel color="gray.300">Yatırım Miktarı</FormLabel>
              <NumberInput
                value={amount}
                onChange={(_, value) => setAmount(value || 0)}
                min={0.000001}
                max={1000000}
                precision={6}
                step={0.1}
              >
                <NumberInputField
                  bg="gray.700"
                  borderColor="gray.600"
                  color="white"
                  _focus={{ borderColor: 'gold.400' }}
                />
                <NumberInputStepper>
                  <NumberIncrementStepper color="gray.400" />
                  <NumberDecrementStepper color="gray.400" />
                </NumberInputStepper>
              </NumberInput>
            </FormControl>

            {/* Currency Selection */}
            <FormControl>
              <FormLabel color="gray.300">Kripto Para Birimi</FormLabel>
              <Select
                value={currency}
                onChange={(e) => setCurrency(e.target.value)}
                bg="gray.700"
                borderColor="gray.600"
                color="white"
                _focus={{ borderColor: 'gold.400' }}
              >
                {SUPPORTED_CURRENCIES.map((curr) => (
                  <option key={curr.value} value={curr.value} style={{ backgroundColor: '#2D3748' }}>
                    {curr.label}
                  </option>
                ))}
              </Select>
            </FormControl>

            {/* Compound Interest Option */}
            <FormControl>
              <HStack justify="space-between">
                <VStack align="start" spacing={1}>
                  <FormLabel color="gray.300" mb={0}>Bileşik Faiz</FormLabel>
                  <Text fontSize="sm" color="gray.500">
                    Kazançları otomatik olarak yeniden yatır
                  </Text>
                </VStack>
                <Switch
                  isChecked={compoundEnabled}
                  onChange={(e) => setCompoundEnabled(e.target.checked)}
                  colorScheme="yellow"
                />
              </HStack>
            </FormControl>

            <Divider borderColor="gray.600" />

            {/* Investment Summary */}
            <Box>
              <Text fontSize="lg" fontWeight="bold" color="white" mb={4}>
                <FaCalculator style={{ display: 'inline', marginRight: '8px' }} />
                Yatırım Özeti
              </Text>
              
              <VStack spacing={4} align="stretch">
                {/* USDT Value */}
                <HStack justify="space-between">
                  <Text color="gray.400">USDT Değeri:</Text>
                  <Text color="green.400" fontWeight="bold">
                    ${usdtValue.toFixed(2)} USDT
                  </Text>
                </HStack>

                {/* Interest Rate */}
                <HStack justify="space-between">
                  <Text color="gray.400">Günlük Faiz Oranı:</Text>
                  <Badge colorScheme={dailyInterestRate > 1 ? "purple" : "blue"}>
                    %{dailyInterestRate}
                    {dailyInterestRate > 1 && " (Premium)"}
                  </Badge>
                </HStack>

                {/* Earnings Projections */}
                <Box bg="gray.700" p={4} borderRadius="md" borderColor="gray.600" borderWidth="1px">
                  <Text fontSize="md" fontWeight="bold" color="white" mb={3}>
                    Tahmini Kazançlar
                  </Text>
                  
                  <VStack spacing={2} align="stretch">
                    <HStack justify="space-between">
                      <Text color="gray.400" fontSize="sm">Günlük:</Text>
                      <Text color="green.400" fontSize="sm" fontWeight="bold">
                        ${dailyEarnings.toFixed(2)}
                      </Text>
                    </HStack>
                    <HStack justify="space-between">
                      <Text color="gray.400" fontSize="sm">Aylık:</Text>
                      <Text color="green.400" fontSize="sm" fontWeight="bold">
                        ${monthlyEarnings.toFixed(2)}
                      </Text>
                    </HStack>
                    <HStack justify="space-between">
                      <Text color="gray.400" fontSize="sm">Yıllık:</Text>
                      <Text color="green.400" fontSize="sm" fontWeight="bold">
                        ${yearlyEarnings.toFixed(2)}
                      </Text>
                    </HStack>
                  </VStack>
                </Box>

                {/* Activation Time */}
                <Alert status="info" bg="blue.900" borderColor="blue.400" borderWidth="1px">
                  <AlertIcon color="blue.400" />
                  <VStack align="start" spacing={1}>
                    <Text color="blue.200" fontSize="sm" fontWeight="bold">
                      <FaClock style={{ display: 'inline', marginRight: '4px' }} />
                      Aktivasyon Zamanı
                    </Text>
                    <Text color="blue.300" fontSize="xs">
                      {activationTime.toLocaleString('tr-TR', { 
                        timeZone: 'Europe/Istanbul',
                        dateStyle: 'short',
                        timeStyle: 'short'
                      })} (Türkiye Saati)
                    </Text>
                    <Text color="blue.300" fontSize="xs">
                      Yaklaşık {hoursUntilActivation} saat {minutesUntilActivation} dakika sonra
                    </Text>
                  </VStack>
                </Alert>
              </VStack>
            </Box>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <HStack spacing={3}>
            <Button 
              variant="outline" 
              onClick={handleClose}
              isDisabled={loading}
              colorScheme="gray"
            >
              İptal
            </Button>
            <Button
              bg="gold.400"
              color="gray.900"
              _hover={{ bg: 'gold.500' }}
              onClick={handleSubmit}
              isLoading={loading}
              loadingText="Oluşturuluyor..."
            >
              Yatırım Paketi Oluştur
            </Button>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default CreateInvestmentModal;
