import logging
from datetime import datetime, timedelta

class InterestCalculator:
    """Faiz hesaplayan sınıf"""
    
    def __init__(self, interest_rates=None):
        if interest_rates is None:
            # Varsayılan faiz oranları (yıllık)
            interest_rates = {
                "BTC": 0.03,  # %3
                "ETH": 0.04,  # %4
                "BNB": 0.05,  # %5
                "SOL": 0.06,  # %6
                "ADA": 0.07,  # %7
                "USDT": 0.08,  # %8
                "default": 0.05  # %5
            }
        
        self.interest_rates = interest_rates
        self.logger = logging.getLogger('InterestCalculator')
        self.logger.info("InterestCalculator başlatıldı")
    
    def calculate(self, asset, amount, days):
        """Belirli bir varlık ve süre için faiz hesapla"""
        try:
            # Varlık için faiz oranını al, yoksa varsayılanı kullan
            annual_rate = self.interest_rates.get(asset, self.interest_rates.get("default", 0.05))
            
            # Günlük faiz oranı
            daily_rate = annual_rate / 365
            
            # Faiz hesapla
            interest = amount * daily_rate * days
            
            self.logger.info(f"Faiz hesaplandı: {asset}, {amount}, {days} gün, {annual_rate} yıllık oran = {interest}")
            return interest
        except Exception as e:
            self.logger.error(f"Faiz hesaplanırken hata: {str(e)}")
            return 0
    
    def set_interest_rate(self, asset, new_rate):
        """Belirli bir varlık için faiz oranını güncelle"""
        try:
            self.interest_rates[asset] = new_rate
            self.logger.info(f"Faiz oranı güncellendi: {asset}, {new_rate}")
            return True
        except Exception as e:
            self.logger.error(f"Faiz oranı güncellenirken hata: {str(e)}")
            return False
    
    def calculate_compound_interest(self, asset, amount, days, compound_frequency=1):
        """Bileşik faiz hesapla (günlük, haftalık, aylık)"""
        try:
            # Varlık için faiz oranını al, yoksa varsayılanı kullan
            annual_rate = self.interest_rates.get(asset, self.interest_rates.get("default", 0.05))
            
            # Bileşik faiz hesaplama
            # compound_frequency: 1=günlük, 7=haftalık, 30=aylık
            periods = days / compound_frequency
            period_rate = annual_rate / (365 / compound_frequency)
            
            # Bileşik faiz formülü: A = P(1 + r)^n
            final_amount = amount * (1 + period_rate) ** periods
            interest = final_amount - amount
            
            self.logger.info(f"Bileşik faiz hesaplandı: {asset}, {amount}, {days} gün, {compound_frequency} frekans = {interest}")
            return interest
        except Exception as e:
            self.logger.error(f"Bileşik faiz hesaplanırken hata: {str(e)}")
            return 0
