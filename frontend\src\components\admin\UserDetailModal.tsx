import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>oot<PERSON>,
  <PERSON>dalBody,
  ModalCloseButton,
  Button,
  Text,
  VStack,
  HStack,
  Box,
  Badge,
  Divider,
  Spinner,
  useToast,
  Flex,
  Stat,
  StatLabel,
  StatNumber,
  StatGroup,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  useColorModeValue
} from '@chakra-ui/react';
import { adminApiService } from '../../services/adminApi';
import { formatDate } from '../../utils/formatters';
import { useTranslation } from 'react-i18next';

interface UserDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  userId: string;
}

interface UserDetail {
  _id: string;
  email: string;
  firstName: string;
  lastName: string;
  createdAt: string;
  walletAddress?: string;
  kycVerified: boolean;
  isAdmin: boolean;
  status?: string;
  totalDeposits?: number;
  totalWithdrawals?: number;
  referralCode?: string;
  referralCount?: number;
  referralEarnings?: number;
  country?: string;
  city?: string;
  phoneNumber?: string;
}

const UserDetailModal: React.FC<UserDetailModalProps> = ({ isOpen, onClose, userId }) => {
  const [user, setUser] = useState<UserDetail | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const toast = useToast();
  const { t } = useTranslation();

  // Colors
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const textColor = useColorModeValue('gray.800', 'white');
  const secondaryTextColor = useColorModeValue('gray.600', 'gray.400');

  useEffect(() => {
    if (isOpen && userId) {
      fetchUserDetails();
    }
  }, [isOpen, userId]);

  const fetchUserDetails = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await adminApiService.getUserById(userId);

      // Transform API response to match our interface
      if (response.data) {
        const userData = {
          _id: response.data._id,
          email: response.data.email || '',
          firstName: response.data.firstName || '',
          lastName: response.data.lastName || '',
          createdAt: response.data.createdAt || new Date().toISOString(),
          walletAddress: response.data.walletAddress || '',
          kycVerified: response.data.kycVerified || false,
          isAdmin: response.data.isAdmin || false,
          status: response.data.kycVerified ? 'active' : response.data.isAdmin ? 'admin' : 'pending',
          totalDeposits: response.data.totalDeposits || 0,
          totalWithdrawals: response.data.totalWithdrawals || 0,
          referralCode: response.data.referralCode || '',
          referralCount: response.data.referralCount || 0,
          referralEarnings: response.data.referralEarnings || 0,
          country: response.data.country || '',
          city: response.data.city || '',
          phoneNumber: response.data.phoneNumber || ''
        };

        setUser(userData);
        console.log('User data loaded:', userData);
      } else {
        setError('No user data found');
      }
    } catch (err) {
      console.error('Error fetching user details:', err);
      setError('Failed to load user details. Please try again.');
      toast({
        title: 'Error',
        description: 'Failed to load user details',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status?: string) => {
    if (!status) return null;

    let colorScheme = 'gray';
    switch (status.toLowerCase()) {
      case 'active':
        colorScheme = 'green';
        break;
      case 'inactive':
        colorScheme = 'red';
        break;
      case 'pending':
        colorScheme = 'yellow';
        break;
      default:
        colorScheme = 'gray';
    }

    return (
      <Badge colorScheme={colorScheme} borderRadius="full" px={2}>
        {status}
      </Badge>
    );
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl" scrollBehavior="inside">
      <ModalOverlay bg="blackAlpha.300" backdropFilter="blur(10px)" />
      <ModalContent bg={bgColor} borderRadius="md" boxShadow="xl">
        <ModalHeader borderBottomWidth="1px" borderColor={borderColor}>
          {t('admin.userDetails', 'User Details')}
          <ModalCloseButton />
        </ModalHeader>

        <ModalBody py={4}>
          {loading ? (
            <Flex justify="center" align="center" minH="300px">
              <Spinner size="xl" thickness="4px" speed="0.65s" color="blue.500" />
            </Flex>
          ) : error ? (
            <Flex justify="center" align="center" minH="300px">
              <Text color="red.500">{error}</Text>
            </Flex>
          ) : user ? (
            <Tabs variant="enclosed" colorScheme="blue">
              <TabList>
                <Tab>{t('admin.basicInfo', 'Basic Info')}</Tab>
                <Tab>{t('admin.financialInfo', 'Financial Info')}</Tab>
                <Tab>{t('admin.activityLog', 'Activity Log')}</Tab>
              </TabList>

              <TabPanels>
                {/* Basic Info Tab */}
                <TabPanel>
                  <VStack align="stretch" spacing={4}>
                    <HStack justify="space-between">
                      <Text fontSize="lg" fontWeight="bold">
                        {user.firstName} {user.lastName}
                      </Text>
                      {getStatusBadge(user.status || (user.kycVerified ? 'active' : 'pending'))}
                    </HStack>

                    <Divider />

                    <Box>
                      <Text fontWeight="semibold" mb={1}>{t('common.email', 'Email')}</Text>
                      <Text>{user.email}</Text>
                    </Box>

                    <Box>
                      <Text fontWeight="semibold" mb={1}>{t('common.joinDate', 'Join Date')}</Text>
                      <Text>{formatDate(user.createdAt)}</Text>
                    </Box>

                    {user.walletAddress && (
                      <Box>
                        <Text fontWeight="semibold" mb={1}>{t('common.walletAddress', 'Wallet Address')}</Text>
                        <Text fontSize="sm" fontFamily="monospace">{user.walletAddress}</Text>
                      </Box>
                    )}

                    <HStack>
                      <Box>
                        <Text fontWeight="semibold" mb={1}>{t('common.kycStatus', 'KYC Status')}</Text>
                        <Badge colorScheme={user.kycVerified ? 'green' : 'yellow'}>
                          {user.kycVerified ? t('common.verified', 'Verified') : t('common.pending', 'Pending')}
                        </Badge>
                      </Box>

                      <Box>
                        <Text fontWeight="semibold" mb={1}>{t('common.adminStatus', 'Admin Status')}</Text>
                        <Badge colorScheme={user.isAdmin ? 'purple' : 'gray'}>
                          {user.isAdmin ? t('common.admin', 'Admin') : t('common.user', 'User')}
                        </Badge>
                      </Box>
                    </HStack>

                    {(user.country || user.city) && (
                      <Box>
                        <Text fontWeight="semibold" mb={1}>{t('common.location', 'Location')}</Text>
                        <Text>{[user.city, user.country].filter(Boolean).join(', ')}</Text>
                      </Box>
                    )}

                    {user.phoneNumber && (
                      <Box>
                        <Text fontWeight="semibold" mb={1}>{t('common.phoneNumber', 'Phone Number')}</Text>
                        <Text>{user.phoneNumber}</Text>
                      </Box>
                    )}
                  </VStack>
                </TabPanel>

                {/* Financial Info Tab */}
                <TabPanel>
                  <VStack align="stretch" spacing={4}>
                    <StatGroup>
                      <Stat>
                        <StatLabel>{t('admin.totalDeposits', 'Total Deposits')}</StatLabel>
                        <StatNumber>${user.totalDeposits || 0}</StatNumber>
                      </Stat>

                      <Stat>
                        <StatLabel>{t('admin.totalWithdrawals', 'Total Withdrawals')}</StatLabel>
                        <StatNumber>${user.totalWithdrawals || 0}</StatNumber>
                      </Stat>
                    </StatGroup>

                    <Divider />

                    {user.referralCode && (
                      <Box>
                        <Text fontWeight="semibold" mb={1}>{t('common.referralCode', 'Referral Code')}</Text>
                        <Text fontFamily="monospace">{user.referralCode}</Text>
                      </Box>
                    )}

                    <HStack>
                      <Box>
                        <Text fontWeight="semibold" mb={1}>{t('common.referralCount', 'Referral Count')}</Text>
                        <Text>{user.referralCount || 0}</Text>
                      </Box>

                      <Box>
                        <Text fontWeight="semibold" mb={1}>{t('common.referralEarnings', 'Referral Earnings')}</Text>
                        <Text>${user.referralEarnings || 0}</Text>
                      </Box>
                    </HStack>
                  </VStack>
                </TabPanel>

                {/* Activity Log Tab */}
                <TabPanel>
                  <Text color={secondaryTextColor} mb={4}>
                    {t('admin.activityLogDescription', 'Recent user activity will be displayed here.')}
                  </Text>

                  <Table variant="simple" size="sm">
                    <Thead>
                      <Tr>
                        <Th>{t('common.date', 'Date')}</Th>
                        <Th>{t('common.action', 'Action')}</Th>
                        <Th>{t('common.details', 'Details')}</Th>
                      </Tr>
                    </Thead>
                    <Tbody>
                      <Tr>
                        <Td colSpan={3} textAlign="center" py={4}>
                          <Text color={secondaryTextColor}>
                            {t('admin.noActivityData', 'No activity data available')}
                          </Text>
                        </Td>
                      </Tr>
                    </Tbody>
                  </Table>
                </TabPanel>
              </TabPanels>
            </Tabs>
          ) : (
            <Flex justify="center" align="center" minH="300px">
              <Text color={secondaryTextColor}>{t('admin.userNotFound', 'User not found')}</Text>
            </Flex>
          )}
        </ModalBody>

        <ModalFooter borderTopWidth="1px" borderColor={borderColor}>
          <Button variant="ghost" mr={3} onClick={onClose}>
            {t('common.close', 'Close')}
          </Button>
          {user && (
            <Button colorScheme="blue">
              {t('common.edit', 'Edit')}
            </Button>
          )}
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default UserDetailModal;
