import mongoose from 'mongoose';
import dotenv from 'dotenv';
import path from 'path';
import User from '../models/userModel';
import { db } from '../config/database';
import readline from 'readline';

// Load environment variables from .env.local
dotenv.config({ path: path.resolve(__dirname, '../../.env.production') });

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Prompt for user input
const prompt = (question: string): Promise<string> => {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
};

// Validate email
const isValidEmail = (email: string): boolean => {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
};

// Validate password
const isStrongPassword = (password: string): boolean => {
  return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/.test(password);
};

// Create admin user
const createAdminUser = async () => {
  try {
    // Log MongoDB URI for debugging
    console.log(`Connecting to MongoDB at: ${process.env.MONGO_URI}`);

    await db.connect();
    console.log('MongoDB connected');

    // Get user input
    const email = await prompt('Enter admin email: ');
    if (!isValidEmail(email)) {
      console.error('Invalid email format');
      process.exit(1);
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      console.log(`User with email ${email} already exists`);
      const updateExisting = await prompt('Do you want to update this user with new information? (y/n): ');

      if (updateExisting.toLowerCase() === 'y') {
        // Get user details for update
        const firstName = await prompt(`Enter first name [${existingUser.firstName}]: `);
        const lastName = await prompt(`Enter last name [${existingUser.lastName}]: `);
        const updatePassword = await prompt('Do you want to update password? (y/n): ');

        let password = existingUser.password;
        if (updatePassword.toLowerCase() === 'y') {
          password = await prompt('Enter new password (min 8 chars, must include uppercase, lowercase, number, special char): ');

          while (!isStrongPassword(password)) {
            console.log('Password is not strong enough. It must contain at least 8 characters, including uppercase, lowercase, number, and special character.');
            password = await prompt('Enter password: ');
          }
        }

        try {
          // Prepare update data
          const updateData: any = {
            isAdmin: true,
            firstName: firstName || existingUser.firstName,
            lastName: lastName || existingUser.lastName
          };

          if (updatePassword.toLowerCase() === 'y') {
            try {
              // Sử dụng bcrypt trực tiếp để hash mật khẩu
              const bcrypt = require('bcrypt');
              const salt = await bcrypt.genSalt(12);
              const hashedPassword = await bcrypt.hash(password, salt);

              // Add the hashed password to update data
              updateData.password = hashedPassword;
            } catch (error) {
              console.error('Error hashing password:', error);
            }
          }

          // Update the user with new information
          await User.updateOne({ _id: existingUser._id }, updateData);
          console.log(`User ${email} has been updated with new information and set as admin`);
          console.log(`Email: ${email}`);
          console.log(`Name: ${updateData.firstName} ${updateData.lastName}`);
        } catch (error) {
          console.error('Error updating user:', error);
        }
      } else {
        console.log('Operation cancelled');
      }

      rl.close();
      process.exit(0);
    }

    // Get user details
    const firstName = await prompt('Enter first name: ');
    const lastName = await prompt('Enter last name: ');
    let password = await prompt('Enter password (min 8 chars, must include uppercase, lowercase, number, special char): ');

    while (!isStrongPassword(password)) {
      console.log('Password is not strong enough. It must contain at least 8 characters, including uppercase, lowercase, number, and special character.');
      password = await prompt('Enter password: ');
    }

    // Create admin user
    const user = await User.create({
      firstName,
      lastName,
      email,
      password,
      isAdmin: true
    });

    console.log(`Admin user created successfully`);
    console.log(`Email: ${email}`);
    console.log(`Name: ${firstName} ${lastName}`);

    rl.close();
    process.exit(0);
  } catch (error) {
    console.error('Error creating admin user:', error);
    rl.close();
    process.exit(1);
  }
};

createAdminUser();
