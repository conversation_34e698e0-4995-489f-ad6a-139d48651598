import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// API URL from environment variables
export const API_URL = import.meta.env.VITE_API_URL || 'https://api.shpnfinance.com/api';

/**
 * Create axios instance with default config
 */
const api: AxiosInstance = axios.create({
  baseURL: API_URL,
  withCredentials: true,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * Request interceptor for API calls
 */
api.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    const token = localStorage.getItem('token');
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  }
);

/**
 * Response interceptor for API calls
 */
api.interceptors.response.use(
  (response: AxiosResponse) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };

    // Handle token expiration
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh token
        const refreshResponse = await axios.post(`${API_URL}/users/refresh-token`, {}, {
          withCredentials: true
        });

        const { token } = refreshResponse.data;
        localStorage.setItem('token', token);

        // Update authorization header
        if (originalRequest.headers) {
          originalRequest.headers.Authorization = `Bearer ${token}`;
        }

        // Retry the original request
        return api(originalRequest);
      } catch (refreshError) {
        // If refresh token fails, logout user
        localStorage.removeItem('token');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    // Handle other errors
    return Promise.reject(error);
  }
);

/**
 * API service for user-related endpoints
 */
export const userService = {
  register: (userData: any) => api.post('/users/register', userData),
  login: (credentials: { email: string; password: string }) => api.post('/users/login', credentials),
  getProfile: () => api.get('/users/profile'),
  updateProfile: (userData: any) => api.put('/users/profile', userData),
  getReferrals: () => api.get('/users/referrals'),
};

/**
 * API service for wallet-related endpoints
 */
export const walletService = {
  getBalance: () => api.get('/wallets/balance'),
  getTransactions: (params?: any) => api.get('/wallets/transactions', { params }),
  deposit: (data: { token: string; amount: number; mode: 'commission' | 'interest' }) =>
    api.post('/wallets/deposit', data),
  withdraw: (data: { token: string; amount: number }) =>
    api.post('/wallets/withdraw', data),
  toggleMode: (data: { token: string }) =>
    api.post('/wallets/toggle-mode', data),
};

/**
 * API service for transaction-related endpoints
 */
export const transactionService = {
  getAll: (params?: any) => api.get('/transactions', { params }),
  getById: (id: string) => api.get(`/transactions/${id}`),
};

/**
 * API service for investment-related endpoints
 */
export const investmentService = {
  createInvestment: (data: {
    currency: string;
    amount: number;
    description?: string;
  }) => api.post('/investments', data),

  uploadReceipt: (id: string, formData: FormData) =>
    api.post(`/investments/${id}/receipt`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }),

  getInvestments: (params?: {
    page?: number;
    limit?: number;
    status?: string;
    currency?: string;
  }) => api.get('/investments', { params }),

  getInvestmentById: (id: string) => api.get(`/investments/${id}`),

  updateTransactionHash: (id: string, txHash: string) =>
    api.put(`/investments/${id}/txhash`, { txHash }),
};

export default api;
