import { useState, useEffect, useCallback } from 'react';
import Web3Modal from 'web3modal';
import { ethers } from 'ethers';
import WalletConnectProvider from '@walletconnect/web3-provider';
import CoinbaseWalletSDK from '@coinbase/wallet-sdk';

// Provider options for Web3Modal
const providerOptions = {
  walletconnect: {
    package: WalletConnectProvider,
    options: {
      infuraId: import.meta.env.VITE_INFURA_ID || '********************************' // Default Infura ID
    }
  },
  coinbasewallet: {
    package: CoinbaseWalletSDK,
    options: {
      appName: "CryptoYieldHub",
      infuraId: import.meta.env.VITE_INFURA_ID || '********************************'
    }
  }
};

// Initialize Web3Modal
const web3Modal = new Web3Modal({
  cacheProvider: true, // Cache the provider for future use
  providerOptions,
  theme: {
    background: "#1E2329",
    main: "#FFFFFF",
    secondary: "#858585",
    border: "#3B4149",
    hover: "#0B0E11"
  }
});

/**
 * Custom hook for Web3 wallet connection
 */
export const useWeb3 = () => {
  const [provider, setProvider] = useState<ethers.providers.Web3Provider | null>(null);
  const [signer, setSigner] = useState<ethers.Signer | null>(null);
  const [account, setAccount] = useState<string | null>(null);
  const [chainId, setChainId] = useState<number | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  /**
   * Connect to wallet
   */
  const connect = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Connect to wallet
      const instance = await web3Modal.connect();
      const provider = new ethers.providers.Web3Provider(instance);
      const signer = provider.getSigner();
      const accounts = await provider.listAccounts();
      const network = await provider.getNetwork();
      
      setProvider(provider);
      setSigner(signer);
      setAccount(accounts[0]);
      setChainId(network.chainId);
      
      // Setup event listeners
      instance.on("accountsChanged", (accounts: string[]) => {
        setAccount(accounts[0]);
      });
      
      instance.on("chainChanged", (chainId: string) => {
        setChainId(parseInt(chainId, 16));
        // Reload page on chain change as recommended by MetaMask
        window.location.reload();
      });
      
      instance.on("disconnect", () => {
        disconnect();
      });
      
      return true;
    } catch (error) {
      console.error("Connection error:", error);
      setError(error as Error);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Disconnect wallet
   */
  const disconnect = useCallback(async () => {
    try {
      await web3Modal.clearCachedProvider();
      setProvider(null);
      setSigner(null);
      setAccount(null);
      setChainId(null);
    } catch (error) {
      console.error("Disconnect error:", error);
      setError(error as Error);
    }
  }, []);

  /**
   * Switch network
   */
  const switchNetwork = useCallback(async (targetChainId: number) => {
    if (!provider) return false;
    
    try {
      setLoading(true);
      
      // Format chain ID to hex
      const chainIdHex = `0x${targetChainId.toString(16)}`;
      
      await provider.send('wallet_switchEthereumChain', [{ chainId: chainIdHex }]);
      return true;
    } catch (switchError: any) {
      // This error code indicates that the chain has not been added to MetaMask
      if (switchError.code === 4902) {
        try {
          // Add the network
          // This would need network-specific parameters
          return false;
        } catch (addError) {
          setError(addError as Error);
          return false;
        }
      }
      
      setError(switchError);
      return false;
    } finally {
      setLoading(false);
    }
  }, [provider]);

  /**
   * Initialize connection if cached provider exists
   */
  useEffect(() => {
    if (web3Modal.cachedProvider) {
      connect();
    }
  }, [connect]);

  return {
    provider,
    signer,
    account,
    chainId,
    error,
    loading,
    connect,
    disconnect,
    switchNetwork,
    isConnected: !!account
  };
};
