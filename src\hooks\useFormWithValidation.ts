import { useForm, UseFormProps, FieldValues, UseFormReturn } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useToast } from '../context/ToastContext';

/**
 * Custom hook for form handling with validation
 * @param schema - Yup validation schema
 * @param options - React Hook Form options
 * @returns React Hook Form methods with additional helpers
 */
export const useFormWithValidation = <T extends FieldValues>(
  schema: yup.ObjectSchema<any>,
  options?: Omit<UseFormProps<T>, 'resolver'>
): UseFormReturn<T> & {
  handleSubmitWithToast: (
    onValid: (data: T) => Promise<void> | void,
    successMessage?: string
  ) => (e?: React.BaseSyntheticEvent) => Promise<void>;
} => {
  const { addToast } = useToast();
  
  // Initialize form with yup resolver
  const methods = useForm<T>({
    ...options,
    resolver: yupResolver(schema),
  });
  
  /**
   * Enhanced submit handler with toast notifications
   * @param onValid - Function to call when form is valid
   * @param successMessage - Optional success message to show
   */
  const handleSubmitWithToast = (
    onValid: (data: T) => Promise<void> | void,
    successMessage?: string
  ) => {
    return methods.handleSubmit(async (data) => {
      try {
        await onValid(data);
        
        if (successMessage) {
          addToast(successMessage, 'success');
        }
      } catch (error: any) {
        // Handle error
        const errorMessage = error.response?.data?.message || error.message || 'An error occurred';
        addToast(errorMessage, 'error');
        
        // If there are field-specific errors, set them in the form
        if (error.response?.data?.errors) {
          const fieldErrors = error.response.data.errors;
          
          Object.keys(fieldErrors).forEach((field) => {
            methods.setError(field as any, {
              type: 'manual',
              message: fieldErrors[field]
            });
          });
        }
      }
    });
  };
  
  return {
    ...methods,
    handleSubmitWithToast
  };
};

/**
 * Common validation schemas
 */
export const validationSchemas = {
  /**
   * Login form validation schema
   */
  login: yup.object({
    email: yup
      .string()
      .email('Please enter a valid email')
      .required('Email is required'),
    password: yup
      .string()
      .required('Password is required')
  }),
  
  /**
   * Registration form validation schema
   */
  register: yup.object({
    firstName: yup
      .string()
      .required('First name is required'),
    lastName: yup
      .string()
      .required('Last name is required'),
    email: yup
      .string()
      .email('Please enter a valid email')
      .required('Email is required'),
    password: yup
      .string()
      .min(8, 'Password must be at least 8 characters')
      .matches(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
        'Password must contain at least one uppercase letter, one lowercase letter, one number and one special character'
      )
      .required('Password is required'),
    confirmPassword: yup
      .string()
      .oneOf([yup.ref('password')], 'Passwords must match')
      .required('Please confirm your password'),
    walletAddress: yup
      .string()
      .matches(/^0x[a-fA-F0-9]{40}$/, 'Please enter a valid Ethereum wallet address')
      .required('Wallet address is required')
  }),
  
  /**
   * Deposit form validation schema
   */
  deposit: yup.object({
    asset: yup
      .string()
      .required('Asset is required'),
    amount: yup
      .number()
      .positive('Amount must be positive')
      .required('Amount is required'),
    mode: yup
      .string()
      .oneOf(['commission', 'interest'], 'Mode must be either commission or interest')
      .required('Mode is required')
  }),
  
  /**
   * Withdrawal form validation schema
   */
  withdraw: yup.object({
    asset: yup
      .string()
      .required('Asset is required'),
    amount: yup
      .number()
      .positive('Amount must be positive')
      .required('Amount is required')
  }),
  
  /**
   * Profile update form validation schema
   */
  profileUpdate: yup.object({
    firstName: yup
      .string()
      .required('First name is required'),
    lastName: yup
      .string()
      .required('Last name is required'),
    email: yup
      .string()
      .email('Please enter a valid email')
      .required('Email is required'),
    walletAddress: yup
      .string()
      .matches(/^0x[a-fA-F0-9]{40}$/, 'Please enter a valid Ethereum wallet address')
      .required('Wallet address is required')
  })
};
