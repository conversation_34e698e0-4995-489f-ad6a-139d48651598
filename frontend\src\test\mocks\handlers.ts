// Temporarily disabled for compilation
/*
import { http, HttpResponse } from 'msw';

const API_URL = import.meta.env.VITE_API_URL || 'https://api.shpnfinance.com';

export const handlers = [
  // Auth handlers
  http.post(`${API_URL}/api/users/login`, async ({ request }) => {
    const { email } = await request.json() as { email: string };

    if (email === '<EMAIL>') {
      return HttpResponse.json({
        _id: '123',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        token: 'mock-jwt-token',
      }, { status: 200 });
    }

    return HttpResponse.json(
      { message: 'Invalid credentials' },
      { status: 401 }
    );
  }),

  // Wallet handlers
  http.get(`${API_URL}/api/wallets/balance`, ({ request }) => {
    const authHeader = request.headers.get('Authorization');

    if (!authHeader?.includes('Bearer')) {
      return HttpResponse.json(
        { message: 'Not authorized' },
        { status: 401 }
      );
    }

    return HttpResponse.json({
      address: '******************************************',
      assets: [
        {
          symbol: 'BTC',
          balance: 0.5,
          commissionBalance: 0.005,
          interestBalance: 0,
          mode: 'commission'
        },
        {
          symbol: 'ETH',
          balance: 5,
          commissionBalance: 0,
          interestBalance: 0.1,
          mode: 'interest'
        }
      ],
      totalCommissionEarned: 0.005,
      totalInterestEarned: 0.1
    }, { status: 200 });
  }),

  // Transaction handlers
  http.get(`${API_URL}/api/transactions`, ({ request }) => {
    const authHeader = request.headers.get('Authorization');

    if (!authHeader?.includes('Bearer')) {
      return HttpResponse.json(
        { message: 'Not authorized' },
        { status: 401 }
      );
    }

    return HttpResponse.json([
      {
        _id: '1',
        type: 'deposit',
        asset: 'BTC',
        amount: 0.5,
        status: 'completed',
        createdAt: new Date().toISOString()
      },
      {
        _id: '2',
        type: 'commission',
        asset: 'BTC',
        amount: 0.005,
        status: 'completed',
        createdAt: new Date().toISOString()
      }
    ], { status: 200 });
  }),

  // Admin handlers
  http.get(`${API_URL}/api/admin/transactions`, ({ request }) => {
    const authHeader = request.headers.get('Authorization');

    if (!authHeader?.includes('Bearer mock-admin-token')) {
      return HttpResponse.json(
        { message: 'Not authorized as admin' },
        { status: 403 }
      );
    }

    return HttpResponse.json([
      {
        _id: '1',
        userId: '123',
        type: 'deposit',
        asset: 'BTC',
        amount: 0.5,
        status: 'completed',
        createdAt: new Date().toISOString()
      }
    ], { status: 200 });
  }),

  // Error handler for unhandled requests
  http.all(`${API_URL}/*`, ({ request }) => {
    console.error(`Unhandled ${request.method} request to ${request.url}`);
    return HttpResponse.json(
      { message: 'Please add request handler' },
      { status: 500 }
    );
  })
];
*/

// Temporary export to avoid compilation errors
export const handlers = [];