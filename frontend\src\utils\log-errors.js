// Utility to log errors to console and potentially to a server
export const setupErrorLogging = () => {
  // Store the original console.error
  const originalConsoleError = console.error;
  
  // Override console.error
  console.error = (...args) => {
    // Call the original console.error
    originalConsoleError.apply(console, args);
    
    // Log the error to a file or server if needed
    try {
      const errorMessage = args.map(arg => {
        if (arg instanceof Error) {
          return `${arg.name}: ${arg.message}\n${arg.stack}`;
        } else if (typeof arg === 'object') {
          return JSON.stringify(arg, null, 2);
        } else {
          return String(arg);
        }
      }).join(' ');
      
      // Here you could send the error to a server endpoint
      // For now, we'll just log it with a timestamp
      console.log(`[ERROR LOG ${new Date().toISOString()}]`, errorMessage);
    } catch (e) {
      originalConsoleError('Error in error logging:', e);
    }
  };

  // Capture unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled Promise Rejection:', event.reason);
  });

  // Capture global errors
  window.addEventListener('error', (event) => {
    console.error('Global Error:', event.error || event.message);
  });
};
