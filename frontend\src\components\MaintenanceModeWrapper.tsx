import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Box, Spinner, VStack, Text } from '@chakra-ui/react';
import { useMaintenanceMode } from '../hooks/useMaintenanceMode';
import useAuth from '../hooks/useAuth';
import MaintenancePage from '../pages/MaintenancePage';

interface MaintenanceModeWrapperProps {
  children: React.ReactNode;
}

/**
 * Check if user has admin token in cookies
 */
const hasAdminToken = (): boolean => {
  try {
    // Check for adminToken cookie
    const adminToken = document.cookie
      .split('; ')
      .find(row => row.startsWith('adminToken='))
      ?.split('=')[1];

    return adminToken === 'true';
  } catch (error) {
    console.error('Error checking admin token:', error);
    return false;
  }
};

/**
 * Wrapper component that checks for maintenance mode and redirects users accordingly
 * Allows admin users to bypass maintenance mode
 */
const MaintenanceModeWrapper: React.FC<MaintenanceModeWrapperProps> = ({ children }) => {
  const { isMaintenanceMode, isLoading } = useMaintenanceMode(30000); // Check every 30 seconds
  const { user } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [isCheckingAdmin, setIsCheckingAdmin] = useState(true);

  // Routes that should be accessible even during maintenance mode
  const allowedRoutes = [
    '/maintenance',
    '/login',
    '/register',
    '/forgot-password',
    '/reset-password',
  ];

  // Admin routes that should always be accessible
  const adminRoutes = [
    '/admin',
    '/admin/settings',
    '/admin/users',
    '/admin/transactions',
    '/admin/system',
  ];

  // Check if user is admin using multiple methods
  const isAdminUser = (user && user.isAdmin) || hasAdminToken();
  const isAllowedRoute = allowedRoutes.some(route => location.pathname.startsWith(route));
  const isAdminRoute = adminRoutes.some(route => location.pathname.startsWith(route));

  // Effect to check admin status
  useEffect(() => {
    const checkAdminStatus = async () => {
      try {
        // If we have admin token in cookies, verify it with backend
        if (hasAdminToken()) {
          const response = await fetch('/api/admin/check-auth', {
            method: 'GET',
            credentials: 'include', // Include cookies
            headers: {
              'Content-Type': 'application/json',
            },
          });

          if (response.ok) {
            const data = await response.json();
            console.log('Admin status verified:', data.isAdmin);
          }
        }
      } catch (error) {
        console.error('Error verifying admin status:', error);
      } finally {
        setIsCheckingAdmin(false);
      }
    };

    checkAdminStatus();
  }, []);

  useEffect(() => {
    // If maintenance mode is active
    if (isMaintenanceMode && !isLoading) {
      // Allow admin users to access admin routes
      if (isAdminUser && isAdminRoute) {
        return;
      }

      // Allow access to specific allowed routes
      if (isAllowedRoute) {
        return;
      }

      // Redirect all other users to maintenance page
      if (location.pathname !== '/maintenance') {
        navigate('/maintenance', { replace: true });
      }
    }
  }, [isMaintenanceMode, isLoading, isAdminUser, isAdminRoute, isAllowedRoute, location.pathname, navigate]);

  // Show loading spinner while checking maintenance status or admin status
  if (isLoading || isCheckingAdmin) {
    return (
      <Box
        minH="100vh"
        bg="#0B0E11"
        display="flex"
        alignItems="center"
        justifyContent="center"
      >
        <VStack spacing={4}>
          <Spinner size="xl" color="#F0B90B" thickness="4px" />
          <Text color="#EAECEF">Checking system status...</Text>
        </VStack>
      </Box>
    );
  }

  // If we're on the maintenance page, always show it
  if (location.pathname === '/maintenance') {
    return <MaintenancePage />;
  }

  // If maintenance mode is active and user should be redirected
  if (isMaintenanceMode) {
    // Allow admin users to access admin routes
    if (isAdminUser && isAdminRoute) {
      return <>{children}</>;
    }

    // Allow access to specific allowed routes
    if (isAllowedRoute) {
      return <>{children}</>;
    }

    // Show maintenance page for all other cases
    return <MaintenancePage />;
  }

  // Normal operation - render children
  return <>{children}</>;
};

export default MaintenanceModeWrapper;
