import { Express, Request, Response, NextFunction } from 'express';
import compression from 'compression';
import { logger } from '../utils/logger';

/**
 * Apply performance middleware to Express app
 * @param app Express application
 */
export const performanceMiddleware = (app: Express): void => {
  // Compression middleware
  app.use(compression());

  // Request logging middleware
  app.use((req: Request, res: Response, next: NextFunction) => {
    // Log request start
    const start = Date.now();
    const { method, originalUrl, ip } = req;
    
    logger.info(`${method} ${originalUrl} - Request received from ${ip}`);

    // Log response when finished
    res.on('finish', () => {
      const duration = Date.now() - start;
      const { statusCode } = res;
      
      if (statusCode >= 400) {
        logger.warn(`${method} ${originalUrl} - Response: ${statusCode} - ${duration}ms`);
      } else {
        logger.info(`${method} ${originalUrl} - Response: ${statusCode} - ${duration}ms`);
      }
    });

    next();
  });

  // Response time header
  app.use((req: Request, res: Response, next: NextFunction) => {
    const start = Date.now();
    
    res.on('finish', () => {
      const duration = Date.now() - start;
      res.setHeader('X-Response-Time', `${duration}ms`);
    });
    
    next();
  });
};
