import { Express } from 'express';
import helmet from 'helmet';
import cors from 'cors';
import compression from 'compression';
import { metricsMiddleware } from './metricsMiddleware';
import { errorHandlerMiddleware } from './errorHandlerMiddleware';
import { performanceMonitor } from './performanceMiddleware';
import { maintenanceMiddleware } from './maintenanceMiddleware';

/**
 * Apply all middleware to Express app
 * @param app Express application
 */
export const applyMiddleware = (app: Express): void => {
  // Apply security middleware first
  app.use(helmet());

  // Enable CORS with credentials support
  app.use(cors({
    origin: function(origin, callback) {
      // Allow requests with no origin (like mobile apps, curl, etc)
      if (!origin) return callback(null, true);

      // Define allowed origins
      const allowedOrigins = [
        process.env.FRONTEND_URL || 'https://shpnfinance.com',
        'https://shpnfinance.com',
        'https://www.shpnfinance.com'
      ];

      // Add development origins only in development mode
      if (process.env.NODE_ENV === 'development') {
        allowedOrigins.push(
          'http://localhost',
          'http://localhost:80',
          'http://localhost:3000',
          'http://localhost:3003',
          'http://localhost:3004',
          'http://localhost:3005',
          'http://localhost:3006',
          'http://localhost:3007',
          'http://localhost:5173',
          'http://frontend',
          'http://frontend:80',
          'http://cryptoyield-frontend',
          'http://cryptoyield-frontend:80'
        );
      }

      // Check if origin is allowed
      if (allowedOrigins.indexOf(origin) !== -1 || !origin) {
        callback(null, true);
      } else {
        console.log('CORS blocked origin:', origin);
        callback(null, false);
      }
    },
    credentials: true, // Allow credentials (cookies)
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH', 'HEAD'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin', 'Cache-Control', 'Pragma'],
    exposedHeaders: ['Content-Length', 'Content-Type', 'Content-Disposition']
  }));

  // Apply compression middleware
  app.use(compression());

  // Apply performance monitoring
  app.use(performanceMonitor);

  // Apply maintenance mode middleware (should be after security but before routes)
  app.use(maintenanceMiddleware);

  // Apply metrics middleware
  app.use(metricsMiddleware);
};

// Export individual middleware for direct use
export * from './authMiddleware';
export {
  globalLimiter,
  limiter,
  apiLimiter,
  loginLimiter,
  walletLimiter,
  sanitizeData,
  preventXSS,
  preventHPP,
  securityHeaders
  // Don't export cacheMiddleware from here to avoid conflicts
} from './securityMiddleware';
export { performanceMonitor } from './performanceMiddleware';
export {
  metricsMiddleware,
  metricsHandler,
  activeUsers,
  walletOperations,
  transactionVolume,
  errorRate
} from './metricsMiddleware';
export { cacheMiddleware, clearCache } from './cacheMiddleware';
export * from './validateRequest';
export { errorHandlerMiddleware, notFoundHandler, globalErrorHandler } from './errorHandlerMiddleware';
export { maintenanceMiddleware } from './maintenanceMiddleware';
