const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');

let mainWindow;
let pythonProcess;

function createWindow() {
  // Ana pencereyi oluştur
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 1000,
    minHeight: 600,
    backgroundColor: '#0B0E11',
    icon: path.join(__dirname, 'assets/icon.ico'),
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    }
  });

  // Pencere başlığını ayarla
  mainWindow.setTitle('CryptoBasriko Otomasyon');

  // Python scriptini başlat
  startPythonApp();

  // Pencere kapatıldığında
  mainWindow.on('closed', function () {
    mainWindow = null;
    if (pythonProcess) {
      pythonProcess.kill();
    }
  });
}

function startPythonApp() {
  // Python scriptinin yolu
  const scriptPath = path.join(__dirname, 'CryptoBasriko_Otomasyon.py');
  
  // Python scriptini çalıştır
  pythonProcess = spawn('python', [scriptPath]);
  
  // Python çıktılarını dinle
  pythonProcess.stdout.on('data', (data) => {
    console.log(`Python stdout: ${data}`);
  });
  
  pythonProcess.stderr.on('data', (data) => {
    console.error(`Python stderr: ${data}`);
  });
  
  pythonProcess.on('close', (code) => {
    console.log(`Python process exited with code ${code}`);
    if (code !== 0) {
      dialog.showErrorBox('Hata', 'Python uygulaması beklenmedik şekilde kapandı.');
    }
    app.quit();
  });
}

// Electron hazır olduğunda
app.on('ready', createWindow);

// Tüm pencereler kapatıldığında
app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', function () {
  if (mainWindow === null) {
    createWindow();
  }
});

// Uygulama kapatılırken
app.on('will-quit', () => {
  if (pythonProcess) {
    pythonProcess.kill();
  }
});
