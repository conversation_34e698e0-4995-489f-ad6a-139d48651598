import { apiClient } from '../utils/apiClient';

/**
 * API service for investment-related endpoints
 */
export const investmentService = {
  /**
   * Create a new investment
   * @param data Investment data
   */
  createInvestment: (data: {
    currency: string;
    amount: number;
    description?: string;
    network?: string;
  }) => apiClient.post('/investments', data),

  /**
   * Upload receipt for an investment
   * @param id Investment ID
   * @param formData FormData containing the receipt file
   */
  uploadReceipt: (id: string, formData: FormData) =>
    apiClient.post(`/investments/${id}/receipt`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }),

  /**
   * Get all investments for the current user
   * @param params Optional query parameters
   */
  getInvestments: (params?: {
    page?: number;
    limit?: number;
    status?: string;
    currency?: string;
    grouped?: boolean;
  }) => apiClient.get('/investments', { params }),

  /**
   * Get grouped investments for the current user
   * This is a convenience method that sets grouped=true
   */
  getGroupedInvestments: (params?: {
    status?: string;
    currency?: string;
  }) => apiClient.get('/investments', { params: { ...params, grouped: true } }),

  /**
   * Get investment by ID
   * @param id Investment ID
   */
  getInvestmentById: (id: string) => apiClient.get(`/investments/${id}`),

  /**
   * Update transaction hash for an investment
   * @param id Investment ID
   * @param txHash Transaction hash
   */
  updateTransactionHash: (id: string, txHash: string) =>
    apiClient.put(`/investments/${id}/txhash`, { txHash }),

  /**
   * Get deposit address for a currency
   * @param currency Currency symbol (e.g., BTC, ETH)
   * @param network Optional network ID
   */
  getDepositAddress: (() => {
    // Cache for deposit addresses
    const cache: { [key: string]: { data: any, timestamp: number } } = {};
    const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes cache

    return (currency: string, network?: string) => {
      const cacheKey = `${currency}:${network || 'default'}`;
      const now = Date.now();

      // Check if we have cached data that's still valid
      if (cache[cacheKey] && (now - cache[cacheKey].timestamp) < CACHE_DURATION) {
        console.log(`Using cached deposit address for ${cacheKey}`);
        return Promise.resolve(cache[cacheKey].data);
      }

      // Otherwise make the API call
      return apiClient.get(`/wallets/deposit-address/${currency}`, {
        params: network ? { network } : undefined
      }).then(response => {
        // Cache the response
        cache[cacheKey] = {
          data: response,
          timestamp: now
        };
        return response;
      });
    };
  })(),

  /**
   * Get available wallet addresses for all currencies or a specific currency with network information
   * @param currency Optional currency symbol (e.g., BTC, ETH)
   */
  getAvailableWallets: (() => {
    // Cache for wallet data
    const cache: { [key: string]: { data: any, timestamp: number } } = {};
    const CACHE_DURATION = 60 * 1000; // 1 minute cache

    return (currency?: string) => {
      const cacheKey = currency || 'all';
      const now = Date.now();

      // Check if we have cached data that's still valid
      if (cache[cacheKey] && (now - cache[cacheKey].timestamp) < CACHE_DURATION) {
        console.log(`Using cached wallet data for ${cacheKey}`);
        return Promise.resolve(cache[cacheKey].data);
      }

      // Otherwise make the API call
      return apiClient.get('/wallets/available', {
        params: currency ? { currency } : undefined
      }).then(response => {
        // Cache the response
        cache[cacheKey] = {
          data: response,
          timestamp: now
        };
        return response;
      });
    };
  })(),
};

export default investmentService;
