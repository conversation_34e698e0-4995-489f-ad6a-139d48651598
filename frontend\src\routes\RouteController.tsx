import React, { lazy, Suspense, useEffect } from 'react';
import { Routes, Route, Navigate, useLocation } from 'react-router-dom';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorBoundary from '../components/ErrorBoundary';
import useAuth from '../hooks/useAuth';
import AdminLayout from '../layouts/AdminLayout';
import NotFoundPage from '../pages/NotFoundPage';

// Route types for better organization
enum RouteType {
  PUBLIC = 'public',
  PROTECTED = 'protected',
  ADMIN = 'admin'
}

// Route definition interface
interface RouteDefinition {
  path: string;
  component: React.LazyExoticComponent<React.ComponentType<any>>;
  type: RouteType;
  title: string;
  description?: string;
  exact?: boolean;
  children?: RouteDefinition[];
}

// Lazy load pages with code splitting and preloading hints
const Home = lazy(() => import(/* webpackChunkName: "home" */ '../pages/Home'));
const Login = lazy(() => import(/* webpackChunkName: "auth" */ '../pages/Login'));
const Register = lazy(() => import(/* webpackChunkName: "auth" */ '../pages/Register'));
const Dashboard = lazy(() => import(/* webpackChunkName: "dashboard" */ '../pages/Dashboard'));
const Profile = lazy(() => import(/* webpackChunkName: "profile" */ '../pages/Profile'));
const Referrals = lazy(() => import(/* webpackChunkName: "referrals" */ '../pages/Referrals'));
const About = lazy(() => import(/* webpackChunkName: "about" */ '../pages/About'));
const Contact = lazy(() => import(/* webpackChunkName: "contact" */ '../pages/Contact'));
const FAQ = lazy(() => import(/* webpackChunkName: "faq" */ '../pages/FAQ'));
const Wallet = lazy(() => import(/* webpackChunkName: "wallet" */ '../pages/WalletPage'));

const BasicConverter = lazy(() => import(/* webpackChunkName: "converter" */ '../pages/BasicConverter'));

// Admin pages - grouped in chunks for better loading
const AdminDashboard = lazy(() => import(/* webpackChunkName: "admin-core" */ '../pages/admin/AdminDashboard'));
const AdminUsers = lazy(() => import(/* webpackChunkName: "admin-users" */ '../pages/admin/AdminUsers'));
const AdminTransactions = lazy(() => import(/* webpackChunkName: "admin-transactions" */ '../pages/admin/AdminTransactions'));
const AdminDeposits = lazy(() => import(/* webpackChunkName: "admin-finance" */ '../pages/admin/AdminDeposits'));
const AdminWithdrawals = lazy(() => import(/* webpackChunkName: "admin-finance" */ '../pages/admin/AdminWithdrawals'));
const AdminReferrals = lazy(() => import(/* webpackChunkName: "admin-referrals" */ '../pages/admin/AdminReferrals'));
const AdminContent = lazy(() => import(/* webpackChunkName: "admin-content" */ '../pages/admin/AdminContent'));
const AdminSettings = lazy(() => import(/* webpackChunkName: "admin-settings" */ '../pages/admin/AdminSettings'));
const TransactionDetail = lazy(() => import(/* webpackChunkName: "admin-transactions" */ '../pages/admin/TransactionDetail'));
const AdminUserDetail = lazy(() => import(/* webpackChunkName: "admin-users" */ '../pages/admin/AdminUserDetail'));
const HomeManagement = lazy(() => import(/* webpackChunkName: "admin-home" */ '../pages/admin/HomeManagement'));
const ProfileManagement = lazy(() => import(/* webpackChunkName: "admin-profile" */ '../pages/admin/ProfileManagement'));
const CommissionSettings = lazy(() => import(/* webpackChunkName: "admin-commission" */ '../pages/admin/CommissionSettings'));
const SiteManagement = lazy(() => import(/* webpackChunkName: "admin-site" */ '../pages/admin/SiteManagement'));
const SystemManagement = lazy(() => import(/* webpackChunkName: "admin-system" */ '../pages/admin/SystemManagement'));

// Route definitions
const routes: RouteDefinition[] = [
  // Public routes
  {
    path: '/',
    component: Home,
    type: RouteType.PUBLIC,
    title: 'Home | Shipping Finance',
    description: 'Welcome to Shipping Finance - Your trusted crypto investment platform'
  },
  {
    path: '/login',
    component: Login,
    type: RouteType.PUBLIC,
    title: 'Login | Shipping Finance',
    description: 'Login to your Shipping Finance account'
  },
  {
    path: '/register',
    component: Register,
    type: RouteType.PUBLIC,
    title: 'Register | Shipping Finance',
    description: 'Create a new Shipping Finance account'
  },
  {
    path: '/about',
    component: About,
    type: RouteType.PUBLIC,
    title: 'About Us | Shipping Finance',
    description: 'Learn more about Shipping Finance and our mission'
  },
  {
    path: '/contact',
    component: Contact,
    type: RouteType.PUBLIC,
    title: 'Contact Us | Shipping Finance',
    description: 'Get in touch with the Shipping Finance team'
  },
  {
    path: '/faq',
    component: FAQ,
    type: RouteType.PUBLIC,
    title: 'FAQ | Shipping Finance',
    description: 'Frequently asked questions about Shipping Finance'
  },

  // Protected routes
  {
    path: '/dashboard',
    component: Dashboard,
    type: RouteType.PROTECTED,
    title: 'Dashboard | Shipping Finance',
    description: 'Your Shipping Finance investment dashboard'
  },
  {
    path: '/profile',
    component: Profile,
    type: RouteType.PROTECTED,
    title: 'Profile | Shipping Finance',
    description: 'Manage your Shipping Finance profile'
  },

  {
    path: '/referrals',
    component: Referrals,
    type: RouteType.PROTECTED,
    title: 'Referrals | Shipping Finance',
    description: 'Manage your referrals and earn commissions'
  },

  {
    path: '/converter',
    component: BasicConverter,
    type: RouteType.PROTECTED,
    title: 'Converter | Shipping Finance',
    description: 'Convert between different cryptocurrencies'
  },

  {
    path: '/wallet',
    component: Wallet,
    type: RouteType.PROTECTED,
    title: 'Wallet | Shipping Finance',
    description: 'Manage your crypto assets, deposits, and withdrawals'
  },

  // Admin routes
  {
    path: '/admin',
    component: AdminDashboard,
    type: RouteType.ADMIN,
    title: 'Admin Dashboard | Shipping Finance',
    description: 'Shipping Finance administration dashboard',
    exact: true
  },
  {
    path: '/admin/users',
    component: AdminUsers,
    type: RouteType.ADMIN,
    title: 'User Management | Admin | Shipping Finance',
    description: 'Manage users on Shipping Finance'
  },
  {
    path: '/admin/users/:id',
    component: AdminUserDetail,
    type: RouteType.ADMIN,
    title: 'User Detail | Admin | Shipping Finance',
    description: 'View user details on Shipping Finance'
  },
  {
    path: '/admin/transactions',
    component: AdminTransactions,
    type: RouteType.ADMIN,
    title: 'Transactions | Admin | Shipping Finance',
    description: 'Manage transactions on Shipping Finance'
  },
  {
    path: '/admin/deposits',
    component: AdminDeposits,
    type: RouteType.ADMIN,
    title: 'Deposits | Admin | Shipping Finance',
    description: 'Manage deposits on Shipping Finance'
  },
  {
    path: '/admin/withdrawals',
    component: AdminWithdrawals,
    type: RouteType.ADMIN,
    title: 'Withdrawals | Admin | Shipping Finance',
    description: 'Manage withdrawals on Shipping Finance'
  },
  {
    path: '/admin/referrals',
    component: AdminReferrals,
    type: RouteType.ADMIN,
    title: 'Referrals | Admin | Shipping Finance',
    description: 'Manage referrals on Shipping Finance'
  },
  {
    path: '/admin/content',
    component: AdminContent,
    type: RouteType.ADMIN,
    title: 'Content Management | Admin | Shipping Finance',
    description: 'Manage content on Shipping Finance'
  },
  {
    path: '/admin/settings',
    component: AdminSettings,
    type: RouteType.ADMIN,
    title: 'Settings | Admin | Shipping Finance',
    description: 'Manage settings on Shipping Finance'
  },
  {
    path: '/admin/transaction/:id',
    component: TransactionDetail,
    type: RouteType.ADMIN,
    title: 'Transaction Detail | Admin | Shipping Finance',
    description: 'View transaction details on Shipping Finance'
  },
  {
    path: '/admin/home-management',
    component: HomeManagement,
    type: RouteType.ADMIN,
    title: 'Home Management | Admin | Shipping Finance',
    description: 'Manage home page on Shipping Finance'
  },
  {
    path: '/admin/profile-management',
    component: ProfileManagement,
    type: RouteType.ADMIN,
    title: 'Profile Management | Admin | Shipping Finance',
    description: 'Manage profile settings on Shipping Finance'
  },
  {
    path: '/admin/commission',
    component: CommissionSettings,
    type: RouteType.ADMIN,
    title: 'Commission Settings | Admin | Shipping Finance',
    description: 'Manage commission settings on Shipping Finance'
  },
  {
    path: '/admin/site-management',
    component: SiteManagement,
    type: RouteType.ADMIN,
    title: 'Site Management | Admin | Shipping Finance',
    description: 'Manage site settings on Shipping Finance'
  },
  {
    path: '/admin/system-management',
    component: SystemManagement,
    type: RouteType.ADMIN,
    title: 'System Management | Admin | Shipping Finance',
    description: 'Manage system settings on Shipping Finance'
  }
];

// Protected route wrapper component
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, loading } = useAuth();
  const location = useLocation();

  if (loading) {
    return <LoadingSpinner />;
  }

  if (!user) {
    // Save the location they were trying to access for redirect after login
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return <>{children}</>;
};

// Admin route wrapper component
const AdminRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, loading } = useAuth();
  const location = useLocation();

  if (loading) {
    return <LoadingSpinner />;
  }

  if (!user) {
    // Save the location they were trying to access for redirect after login
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (!user.isAdmin) {
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
};

// Route controller component
const RouteController = () => {
  const location = useLocation();

  // Update document title and meta description based on current route
  const updateDocumentMeta = () => {
    const currentRoute = routes.find(route => {
      if (route.exact) {
        return route.path === location.pathname;
      }
      return location.pathname.startsWith(route.path);
    });

    if (currentRoute) {
      document.title = currentRoute.title;

      // Update meta description
      const metaDescription = document.querySelector('meta[name="description"]');
      if (metaDescription && currentRoute.description) {
        metaDescription.setAttribute('content', currentRoute.description);
      }
    } else {
      document.title = 'Shipping Finance';
    }
  };

  // Update meta tags when location changes
  useEffect(() => {
    updateDocumentMeta();
  }, [location]);

  return (
    <Suspense fallback={<LoadingSpinner />}>
      <Routes>
        {/* Public Routes */}
        {routes
          .filter(route => route.type === RouteType.PUBLIC)
          .map(route => (
            <Route
              key={route.path}
              path={route.path}
              element={
                <ErrorBoundary>
                  <route.component />
                </ErrorBoundary>
              }
            />
          ))}

        {/* Protected Routes */}
        {routes
          .filter(route => route.type === RouteType.PROTECTED)
          .map(route => (
            <Route
              key={route.path}
              path={route.path}
              element={
                <ProtectedRoute>
                  <ErrorBoundary>
                    <route.component />
                  </ErrorBoundary>
                </ProtectedRoute>
              }
            />
          ))}

        {/* Admin Routes */}
        <Route
          path="/admin"
          element={
            <AdminRoute>
              <ErrorBoundary>
                <AdminLayout />
              </ErrorBoundary>
            </AdminRoute>
          }
        >
          {/* Index route for /admin */}
          <Route index element={<AdminDashboard />} />

          {/* Nested admin routes */}
          <Route path="users" element={<AdminUsers />} />
          <Route path="transactions" element={<AdminTransactions />} />
          <Route path="deposits" element={<AdminDeposits />} />
          <Route path="withdrawals" element={<AdminWithdrawals />} />
          <Route path="referrals" element={<AdminReferrals />} />
          <Route path="content" element={<AdminContent />} />
          <Route path="settings" element={<AdminSettings />} />
          <Route path="transaction/:id" element={<TransactionDetail />} />
          <Route path="users/:id" element={<AdminUserDetail />} />
          <Route path="home-management" element={<HomeManagement />} />
          <Route path="profile-management" element={<ProfileManagement />} />
          <Route path="commission" element={<CommissionSettings />} />
          <Route path="site-management" element={<SiteManagement />} />
          <Route path="system-management" element={<SystemManagement />} />
        </Route>

        {/* 404 Route - Catch all unmatched routes */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </Suspense>
  );
};

export default RouteController;
export { RouteType, routes };
