import React from 'react';
import { Box, Heading, Text, Button, VStack, useColorModeValue, Container, Icon } from '@chakra-ui/react';
import { FaExclamationTriangle } from 'react-icons/fa';
import { Link as RouterLink } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const NotFound = () => {
  const { t } = useTranslation();
  const bgColor = useColorModeValue('gray.50', '#0B0E11');
  const textColor = useColorModeValue('gray.800', 'white');
  const secondaryTextColor = useColorModeValue('gray.600', 'gray.400');

  return (
    <Container maxW="container.xl" py={20}>
      <Box 
        textAlign="center" 
        py={10} 
        px={6} 
        borderRadius="lg"
        bg={bgColor}
        boxShadow="md"
      >
        <Box display="inline-block" mb={6}>
          <Icon 
            as={FaExclamationTriangle} 
            boxSize="50px" 
            color="yellow.400" 
          />
        </Box>
        <Heading as="h1" size="xl" mb={4} color={textColor}>
          {t('notFound.title', '404 - Page Not Found')}
        </Heading>
        <Text fontSize="lg" color={secondaryTextColor} mb={8}>
          {t('notFound.message', 'The page you are looking for does not exist or has been moved.')}
        </Text>
        <VStack spacing={4}>
          <Button
            as={RouterLink}
            to="/"
            colorScheme="yellow"
            size="lg"
            fontWeight="bold"
          >
            {t('notFound.returnHome', 'Return to Home')}
          </Button>
          <Button
            as={RouterLink}
            to="/contact"
            variant="outline"
            colorScheme="yellow"
            size="md"
          >
            {t('notFound.contactSupport', 'Contact Support')}
          </Button>
        </VStack>
      </Box>
    </Container>
  );
};

export default NotFound;
