import mongoose from 'mongoose';
import { logger } from '../utils/logger';

interface ConnectionOptions extends mongoose.ConnectOptions {
  maxPoolSize: number;
  minPoolSize: number;
  connectTimeoutMS: number;
  socketTimeoutMS: number;
}

class Database {
  private static instance: Database;
  private retryAttempts: number = 5;
  private retryDelay: number = 5000;
  private isConnected: boolean = false;
  private mockMode: boolean = false;

  private constructor() {
    // Check if we're in development mode with mock database
    this.mockMode = process.env.NODE_ENV === 'development' && (process.env.USE_MOCK_DATABASE === 'true' || false);

    // Only initialize mongoose if not in mock mode
    if (!this.mockMode) {
      this.initializeMongoose();
    }
  }

  public static getInstance(): Database {
    if (!Database.instance) {
      Database.instance = new Database();
    }
    return Database.instance;
  }

  private initializeMongoose() {
    mongoose.connection.on('connected', () => {
      this.isConnected = true;
      logger.info('MongoDB connection successful');
    });

    mongoose.connection.on('error', (err) => {
      this.isConnected = false;
      logger.error('MongoDB connection error:', err);
    });

    mongoose.connection.on('disconnected', () => {
      this.isConnected = false;
      logger.warn('MongoDB connection disconnected');
      this.reconnect();
    });

    process.on('SIGINT', async () => {
      await this.closeConnection();
      process.exit(0);
    });
  }

  private getConnectionOptions(): ConnectionOptions {
    return {
      maxPoolSize: 50,
      minPoolSize: 10,
      connectTimeoutMS: 10000,
      socketTimeoutMS: 45000,
      serverSelectionTimeoutMS: 5000,
      heartbeatFrequencyMS: 10000,
      retryWrites: true,
      retryReads: true,
      w: 'majority',
      wtimeoutMS: 2500,
    };
  }

  public async connect(retries: number = this.retryAttempts): Promise<void> {
    // If in mock mode, just set connected flag and return
    if (this.mockMode) {
      if (!this.isConnected) {
        logger.info('Running in MOCK DATABASE mode - no actual MongoDB connection');
        this.isConnected = true;
      }
      return;
    }

    try {
      // Get MongoDB URI from environment variables
      const mongoUri = process.env.MONGO_URI || 'mongodb://localhost:27017/cryptoyieldhub';

      // Log connection attempt (without credentials)
      const sanitizedUri = mongoUri.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@');
      logger.info(`Attempting to connect to MongoDB: ${sanitizedUri}`);

      // Get connection options
      const options = this.getConnectionOptions();

      // Connect to MongoDB
      await mongoose.connect(mongoUri, options);

      // Log successful connection
      logger.info('MongoDB connection successful');

      // Create indexes
      await this.createIndexes();

    } catch (error) {
      // Check if error is related to hostname resolution (common in Docker)
      const errorMessage = error.message || '';
      const isHostnameError = errorMessage.includes('getaddrinfo') ||
                              errorMessage.includes('ENOTFOUND') ||
                              errorMessage.includes('EHOSTUNREACH');

      if (isHostnameError) {
        logger.error(`MongoDB hostname resolution error. If using Docker, make sure the container name matches the hostname in MONGO_URI: ${error.message}`);
      } else {
        logger.error('MongoDB connection error:', error);
      }

      if (retries > 0) {
        const waitTime = this.retryDelay * (this.retryAttempts - retries + 1); // Exponential backoff
        logger.info(`Reconnecting in ${waitTime/1000} seconds... (${retries} attempts remaining)`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
        return this.connect(retries - 1);
      } else {
        logger.error('MongoDB connection failed after multiple attempts');

        // In development mode, switch to mock mode instead of failing
        if (process.env.NODE_ENV === 'development') {
          logger.info('Switching to MOCK DATABASE mode due to connection failure');
          logger.info('Note: If using Docker, make sure the MongoDB container name matches the hostname in MONGO_URI');
          logger.info('You can use "mongodb" as the hostname when connecting from another container in the same Docker network');
          this.mockMode = true;
          this.isConnected = true;
          return;
        }

        throw error;
      }
    }
  }

  private async createIndexes(): Promise<void> {
    // Skip in mock mode
    if (this.mockMode) {
      logger.info('Skipping index creation in MOCK DATABASE mode');
      return;
    }

    try {
      // User indexes
      await mongoose.model('User').createIndexes();

      // Wallet indexes
      await mongoose.model('Wallet').createIndexes();

      // Transaction indexes
      await mongoose.model('Transaction').createIndexes();

      logger.info('Database indexes created successfully');
    } catch (error) {
      logger.error('Error creating database indexes:', error);

      // In development mode, don't throw the error
      if (process.env.NODE_ENV !== 'development') {
        throw error;
      }
    }
  }

  private async reconnect(): Promise<void> {
    // Skip in mock mode
    if (this.mockMode) {
      this.isConnected = true;
      return;
    }

    if (!this.isConnected) {
      logger.info('Attempting to reconnect...');
      await this.connect();
    }
  }

  public async closeConnection(): Promise<void> {
    // Skip in mock mode
    if (this.mockMode) {
      this.isConnected = false;
      logger.info('Mock database connection closed');
      return;
    }

    try {
      await mongoose.connection.close();
      this.isConnected = false;
      logger.info('MongoDB connection safely closed');
    } catch (error) {
      logger.error('Error closing MongoDB connection:', error);

      // In development mode, don't throw the error
      if (process.env.NODE_ENV !== 'development') {
        throw error;
      }
    }
  }

  public getConnection(): mongoose.Connection {
    // In mock mode, return a mock connection or the real one
    if (this.mockMode) {
      logger.warn('Attempted to get database connection in MOCK mode');
    }
    return mongoose.connection;
  }

  public isConnectedToDatabase(): boolean {
    // In mock mode, always return true
    return this.mockMode || this.isConnected;
  }
}

export const db = Database.getInstance();