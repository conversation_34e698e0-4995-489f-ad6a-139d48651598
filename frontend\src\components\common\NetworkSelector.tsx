import React from 'react';
import {
  Box,
  FormControl,
  FormLabel,
  FormHelperText,
  RadioGroup,
  Radio,
  Stack,
  Text,
  Badge,
  Flex,
  Alert,
  AlertIcon,
  useColorModeValue,
} from '@chakra-ui/react';
import { NetworkOption } from '../../utils/cryptoNetworks';

interface NetworkSelectorProps {
  networks: NetworkOption[];
  selectedNetwork: string;
  onChange: (networkId: string) => void;
  isRequired?: boolean;
  label?: string;
  helperText?: string;
  currency: string;
  disabledNetworks?: string[]; // Mảng các network ID bị disable
}

const NetworkSelector: React.FC<NetworkSelectorProps> = ({
  networks,
  selectedNetwork,
  onChange,
  isRequired = true,
  label = 'Select Network',
  helperText = 'Choose the network for your transaction',
  currency,
  disabledNetworks = [],
}) => {
  const bgColor = useColorModeValue('white', '#1E2329');
  const borderColor = useColorModeValue('#E2E8F0', '#2B3139');
  const textColor = useColorModeValue('#1A202C', '#EAECEF');
  const secondaryTextColor = useColorModeValue('#4A5568', '#848E9C');
  const selectedBgColor = useColorModeValue('gray.50', '#2B3139');

  return (
    <FormControl isRequired={isRequired} mb={4}>
      <FormLabel color={secondaryTextColor}>{label}</FormLabel>
      <RadioGroup value={selectedNetwork} onChange={onChange}>
        <Stack spacing={3} maxH="300px" overflowY="auto" pr={1}>
          {networks.map((network) => (
            <Box
              key={network.id}
              p={3}
              borderWidth="1px"
              borderRadius="md"
              borderColor={selectedNetwork === network.id ? (network.isDefault ? 'green.400' : 'blue.400') : borderColor}
              bg={selectedNetwork === network.id ? selectedBgColor : bgColor}
              _hover={{ borderColor: disabledNetworks.includes(network.id) ? borderColor : (selectedNetwork === network.id ? (network.isDefault ? 'green.500' : 'blue.500') : 'gray.300') }}
              cursor={disabledNetworks.includes(network.id) ? "not-allowed" : "pointer"}
              onClick={() => !disabledNetworks.includes(network.id) && onChange(network.id)}
              opacity={disabledNetworks.includes(network.id) ? 0.6 : 1}
            >
              <Flex justifyContent="space-between" alignItems="center">
                <Radio
                  value={network.id}
                  colorScheme={network.isDefault ? 'green' : 'blue'}
                  isDisabled={disabledNetworks.includes(network.id)}
                >
                  <Text fontWeight="medium" color={textColor}>
                    {network.name}
                    {network.isDefault && (
                      <Badge ml={2} colorScheme="green" fontSize="xs">
                        Recommended
                      </Badge>
                    )}
                    {disabledNetworks.includes(network.id) && (
                      <Badge ml={2} colorScheme="red" fontSize="xs">
                        No Address Available
                      </Badge>
                    )}
                  </Text>
                </Radio>
                <Text fontSize="sm" color={secondaryTextColor}>
                  Fee: {network.fee} {currency}
                </Text>
              </Flex>
              <Text fontSize="sm" color={secondaryTextColor} mt={1}>
                {network.description}
              </Text>
              <Text fontSize="xs" color={secondaryTextColor} mt={1}>
                Processing time: {network.processingTime}
              </Text>

              {network.warningMessage && selectedNetwork === network.id && (
                <Alert status="warning" mt={2} py={2} size="sm" borderRadius="md">
                  <AlertIcon />
                  <Text fontSize="xs">{network.warningMessage}</Text>
                </Alert>
              )}
            </Box>
          ))}
        </Stack>
      </RadioGroup>
      {helperText && <FormHelperText color={secondaryTextColor}>{helperText}</FormHelperText>}
    </FormControl>
  );
};

export default NetworkSelector;
