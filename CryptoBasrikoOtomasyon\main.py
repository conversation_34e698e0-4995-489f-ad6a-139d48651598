import sys
import os
import time
import json
import logging
from datetime import datetime
from src.crypto_api import CryptoAPI
from src.portfolio_manager import PortfolioManager
from src.commission_calculator import CommissionCalculator
from src.interest_calculator import InterestCalculator
from src.user_manager import UserManager
from src.config_manager import ConfigManager
from src.gui import CryptoBasrikoGUI

# Logging ayarları
logging.basicConfig(
    filename='logs/app.log',
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class CryptoBasrikoApp:
    def __init__(self):
        self.logger = logging.getLogger('CryptoBasrikoApp')
        self.logger.info("Uygulama başlatılıyor...")
        
        # Konfigürasyon yöneticisini başlat
        self.config_manager = ConfigManager('config/settings.json')
        self.settings = self.config_manager.load_settings()
        
        # API ve diğer bileşenleri başlat
        self.crypto_api = CryptoAPI(self.settings['api_key'], self.settings['api_secret'])
        self.user_manager = UserManager('data/users.json')
        self.portfolio_manager = PortfolioManager(self.crypto_api)
        self.commission_calculator = CommissionCalculator(self.settings['commission_rate'])
        self.interest_calculator = InterestCalculator(self.settings['interest_rates'])
        
        # GUI'yi başlat
        self.gui = CryptoBasrikoGUI(self)
        
    def run(self):
        """Ana uygulama döngüsü"""
        self.logger.info("Ana uygulama döngüsü başlatılıyor...")
        self.gui.run()
    
    def login(self, username, password):
        """Kullanıcı girişi"""
        return self.user_manager.authenticate(username, password)
    
    def get_portfolio(self, user_id):
        """Kullanıcının portföyünü getir"""
        return self.portfolio_manager.get_user_portfolio(user_id)
    
    def calculate_commission(self, amount):
        """Komisyon hesapla"""
        return self.commission_calculator.calculate(amount)
    
    def calculate_interest(self, asset, amount, days):
        """Faiz hesapla"""
        return self.interest_calculator.calculate(asset, amount, days)
    
    def update_portfolio(self, user_id, asset, amount, mode):
        """Portföy güncelle"""
        return self.portfolio_manager.update_asset(user_id, asset, amount, mode)
    
    def get_crypto_prices(self):
        """Güncel kripto fiyatlarını getir"""
        return self.crypto_api.get_current_prices()
    
    def save_settings(self):
        """Ayarları kaydet"""
        self.config_manager.save_settings(self.settings)
    
    def exit(self):
        """Uygulamadan çık"""
        self.logger.info("Uygulama kapatılıyor...")
        self.save_settings()
        sys.exit(0)

if __name__ == "__main__":
    app = CryptoBasrikoApp()
    app.run()
