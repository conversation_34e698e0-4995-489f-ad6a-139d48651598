import { Request, Response } from 'express';
import User from '../models/userModel';
import { logger } from '../utils/logger';
import { catchAsync } from '../utils/errorHandler';
import { AppError } from '../utils/AppError';

/**
 * @desc    Get all referrals for admin
 * @route   GET /api/admin/referrals
 * @access  Admin
 */
export const getAdminReferrals = catchAsync(async (req: Request, res: Response) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const search = req.query.search as string;
  const sortBy = req.query.sortBy as string || 'referralCount';
  const sortOrder = req.query.sortOrder as string || 'desc';

  // Build query
  const query: any = { referralCount: { $gt: 0 } };

  // Add search functionality
  if (search) {
    query.$or = [
      { firstName: { $regex: search, $options: 'i' } },
      { lastName: { $regex: search, $options: 'i' } },
      { email: { $regex: search, $options: 'i' } },
      { referralCode: { $regex: search, $options: 'i' } }
    ];
  }

  // Determine sort options
  const sortOptions: any = {};
  sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1;

  // Execute query with pagination
  const [referrals, total] = await Promise.all([
    User.find(query)
      .select('firstName lastName email referralCode referralCount referralEarnings createdAt')
      .sort(sortOptions)
      .skip((page - 1) * limit)
      .limit(limit),
    User.countDocuments(query)
  ]);

  // Get total stats
  const stats = await User.aggregate([
    {
      $group: {
        _id: null,
        totalReferrals: { $sum: '$referralCount' },
        totalCommissions: { $sum: '$referralEarnings' },
        usersWithReferrals: {
          $sum: {
            $cond: [{ $gt: ['$referralCount', 0] }, 1, 0]
          }
        }
      }
    }
  ]);

  res.status(200).json({
    success: true,
    referrals,
    stats: stats[0] || { totalReferrals: 0, totalCommissions: 0, usersWithReferrals: 0 },
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    }
  });
});

/**
 * @desc    Get referral details by user ID
 * @route   GET /api/admin/referrals/:id
 * @access  Admin
 */
export const getReferralById = catchAsync(async (req: Request, res: Response) => {
  const userId = req.params.id;

  // Find the user
  const user = await User.findById(userId).select('firstName lastName email referralCode referralCount referralEarnings');

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Find users referred by this user
  const referredUsers = await User.find({ referredBy: user.referralCode })
    .select('firstName lastName email createdAt referralEarnings')
    .sort({ createdAt: -1 });

  res.status(200).json({
    success: true,
    referral: user,
    referredUsers
  });
});

/**
 * @desc    Update referral commission rates
 * @route   PUT /api/admin/referrals/commission-rates
 * @access  Admin
 */
export const updateCommissionRates = catchAsync(async (req: Request, res: Response) => {
  // This would be implemented if there's a system config for commission rates
  // For now, we'll just return a success message
  res.status(200).json({
    success: true,
    message: 'Commission rates updated successfully'
  });
});

export default {
  getAdminReferrals,
  getReferralById,
  updateCommissionRates
};
