{"compilerOptions": {"target": "es2016", "module": "commonjs", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": false, "skipLibCheck": true, "outDir": "./dist", "rootDir": "./src", "noImplicitAny": false, "strictNullChecks": false, "noEmitOnError": false, "allowJs": true, "checkJs": false, "resolveJsonModule": true, "declaration": false, "sourceMap": true, "isolatedModules": false, "noEmit": false, "ignoreDeprecations": "5.0", "skipDefaultLibCheck": true, "noErrorTruncation": true}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.test.ts", "**/__tests__/**"], "references": []}