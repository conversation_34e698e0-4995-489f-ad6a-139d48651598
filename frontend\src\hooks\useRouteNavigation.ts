import { useNavigate, useLocation } from 'react-router-dom';
import { useCallback, useEffect, useState } from 'react';
import { routes, RouteType } from '../routes/RouteController';
import useAuth from './useAuth';

interface NavigationState {
  from?: string;
  redirectAfterLogin?: string;
}

/**
 * Custom hook for enhanced route navigation
 * Provides navigation functions with additional features like:
 * - Redirect tracking
 * - Route history
 * - Permission checking
 * - Route metadata access
 */
const useRouteNavigation = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  const [routeHistory, setRouteHistory] = useState<string[]>([]);
  const [previousRoute, setPreviousRoute] = useState<string | null>(null);
  
  // Track route history
  useEffect(() => {
    setRouteHistory(prev => {
      // Don't add duplicate consecutive entries
      if (prev[prev.length - 1] === location.pathname) {
        return prev;
      }
      
      // Set previous route
      if (prev.length > 0) {
        setPreviousRoute(prev[prev.length - 1]);
      }
      
      // Add current route to history
      return [...prev, location.pathname];
    });
  }, [location.pathname]);
  
  // Navigate to a route with permission check
  const navigateTo = useCallback((path: string, options?: { replace?: boolean, state?: any }) => {
    // Find the route definition
    const route = routes.find(r => r.path === path);
    
    if (!route) {
      // If route not found, navigate to 404
      navigate('/not-found', { replace: true });
      return;
    }
    
    // Check permissions
    if (route.type === RouteType.PROTECTED && !user) {
      // Save the intended destination for post-login redirect
      navigate('/login', { 
        replace: true, 
        state: { redirectAfterLogin: path } 
      });
      return;
    }
    
    if (route.type === RouteType.ADMIN && (!user || !user.isAdmin)) {
      // Redirect non-admin users
      navigate(user ? '/dashboard' : '/login', { 
        replace: true,
        state: user ? undefined : { redirectAfterLogin: path }
      });
      return;
    }
    
    // Navigate to the route
    navigate(path, options);
  }, [navigate, user]);
  
  // Navigate back to previous route
  const goBack = useCallback(() => {
    if (previousRoute) {
      navigate(previousRoute);
    } else {
      navigate(-1);
    }
  }, [navigate, previousRoute]);
  
  // Handle post-login redirect
  const handlePostLoginRedirect = useCallback(() => {
    const state = location.state as NavigationState;
    
    if (state?.redirectAfterLogin) {
      // Navigate to the saved redirect path
      navigate(state.redirectAfterLogin, { replace: true });
    } else if (state?.from) {
      // Navigate to the route they were trying to access
      navigate(state.from, { replace: true });
    } else {
      // Default redirect to dashboard
      navigate('/dashboard', { replace: true });
    }
  }, [location.state, navigate]);
  
  // Check if a route is accessible to the current user
  const canAccessRoute = useCallback((path: string): boolean => {
    const route = routes.find(r => r.path === path);
    
    if (!route) return false;
    
    if (route.type === RouteType.PUBLIC) return true;
    if (route.type === RouteType.PROTECTED) return !!user;
    if (route.type === RouteType.ADMIN) return !!user && !!user.isAdmin;
    
    return false;
  }, [user]);
  
  // Get route metadata
  const getRouteMetadata = useCallback((path: string) => {
    return routes.find(r => r.path === path);
  }, []);
  
  // Get current route metadata
  const getCurrentRouteMetadata = useCallback(() => {
    return routes.find(route => {
      if (route.exact) {
        return route.path === location.pathname;
      }
      return location.pathname.startsWith(route.path);
    });
  }, [location.pathname]);
  
  return {
    navigateTo,
    goBack,
    handlePostLoginRedirect,
    canAccessRoute,
    getRouteMetadata,
    getCurrentRouteMetadata,
    routeHistory,
    previousRoute,
    currentPath: location.pathname
  };
};

export default useRouteNavigation;
