import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Flex,
  Text,
  Heading,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Button,
  useToast,
  HStack,
  VStack,
  Stack,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  Card,
  CardBody,
  Divider,
  Icon,
  useColorModeValue
} from '@chakra-ui/react';
import { SearchIcon, CheckIcon, CloseIcon } from '@chakra-ui/icons';
import { FaUsers, FaMoneyBillWave, FaExchangeAlt, FaChartLine } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import useAuth from '../../hooks/useAuth';

// Mock data for transactions
const mockTransactions = [
  { id: '1', user: '<PERSON>', amount: 1500, status: 'pending', date: '2023-04-28', type: 'deposit', wallet: '******************************************' },
  { id: '2', user: 'Jane Smith', amount: 2500, status: 'approved', date: '2023-04-27', type: 'deposit', wallet: '******************************************' },
  { id: '3', user: 'Mike Johnson', amount: 500, status: 'rejected', date: '2023-04-26', type: 'deposit', wallet: '******************************************' },
  { id: '4', user: 'Sarah Williams', amount: 1000, status: 'approved', date: '2023-04-25', type: 'deposit', wallet: '******************************************' },
  { id: '5', user: 'David Brown', amount: 3000, status: 'pending', date: '2023-04-24', type: 'deposit', wallet: '******************************************' },
  { id: '6', user: 'Emily Davis', amount: 800, status: 'approved', date: '2023-04-23', type: 'withdrawal', wallet: '******************************************' },
  { id: '7', user: 'Alex Wilson', amount: 1200, status: 'pending', date: '2023-04-22', type: 'deposit', wallet: '******************************************' },
  { id: '8', user: 'Lisa Taylor', amount: 2000, status: 'approved', date: '2023-04-21', type: 'deposit', wallet: '******************************************' },
];

// Mock data for users
const mockUsers = [
  { id: '1', name: 'John Doe', email: '<EMAIL>', joinDate: '2023-01-15', status: 'active', totalDeposits: 5000 },
  { id: '2', name: 'Jane Smith', email: '<EMAIL>', joinDate: '2023-02-20', status: 'active', totalDeposits: 7500 },
  { id: '3', name: 'Mike Johnson', email: '<EMAIL>', joinDate: '2023-03-10', status: 'inactive', totalDeposits: 1000 },
  { id: '4', name: 'Sarah Williams', email: '<EMAIL>', joinDate: '2023-03-25', status: 'active', totalDeposits: 3000 },
  { id: '5', name: 'David Brown', email: '<EMAIL>', joinDate: '2023-04-05', status: 'active', totalDeposits: 6000 },
];

const AdminDashboard = () => {
  const { user } = useAuth();
  const { t } = useTranslation();
  const toast = useToast();
  const navigate = useNavigate();
  const [transactions, setTransactions] = useState(mockTransactions);
  const [users, setUsers] = useState(mockUsers);
  const [filterStatus, setFilterStatus] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  // Stats calculation
  const totalDeposits = transactions
    .filter(tx => tx.type === 'deposit' && tx.status === 'approved')
    .reduce((sum, tx) => sum + tx.amount, 0);

  const totalWithdrawals = transactions
    .filter(tx => tx.type === 'withdrawal' && tx.status === 'approved')
    .reduce((sum, tx) => sum + tx.amount, 0);

  const pendingTransactions = transactions.filter(tx => tx.status === 'pending').length;
  const activeUsers = users.filter(user => user.status === 'active').length;

  // Filter transactions based on status and search query
  const filteredTransactions = transactions.filter(tx => {
    const matchesStatus = filterStatus === 'all' || tx.status === filterStatus;
    const matchesSearch = tx.user.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         tx.wallet.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesStatus && matchesSearch;
  });

  // Handle transaction approval
  const handleApproveTransaction = (id: string) => {
    setTransactions(transactions.map(tx =>
      tx.id === id ? { ...tx, status: 'approved' } : tx
    ));
    toast({
      title: "Transaction approved",
      description: `Transaction #${id} has been approved successfully.`,
      status: "success",
      duration: 3000,
      isClosable: true,
    });
  };

  // Handle transaction rejection
  const handleRejectTransaction = (id: string) => {
    setTransactions(transactions.map(tx =>
      tx.id === id ? { ...tx, status: 'rejected' } : tx
    ));
    toast({
      title: "Transaction rejected",
      description: `Transaction #${id} has been rejected.`,
      status: "error",
      duration: 3000,
      isClosable: true,
    });
  };

  // Background colors
  const bgColor = useColorModeValue('#1E2329', '#1E2329');
  const cardBgColor = useColorModeValue('#0B0E11', '#0B0E11');
  const borderColor = useColorModeValue('#2B3139', '#2B3139');

  return (
    <Box bg="#0B0E11" minH="100vh" pb={8}>
      <Container maxW="container.xl" px={{ base: 2, md: 4 }} py={8}>
        <Flex justify="space-between" align="center" mb={6}>
          <Heading size="lg" color="#F0B90B">Admin Dashboard</Heading>
          <Text color="#EAECEF">Welcome back, {user?.firstName}!</Text>
        </Flex>

        {/* Stats Overview */}
        <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)", lg: "repeat(4, 1fr)" }} gap={6} mb={8}>
          <Card bg={cardBgColor} borderColor={borderColor} borderWidth="1px">
            <CardBody>
              <Flex align="center">
                <Icon as={FaMoneyBillWave} boxSize={10} color="#F0B90B" mr={4} />
                <Stat>
                  <StatLabel color="#848E9C">Total Deposits</StatLabel>
                  <StatNumber color="#EAECEF">${totalDeposits.toLocaleString()}</StatNumber>
                  <StatHelpText color="#0ECB81">+5.2% from last month</StatHelpText>
                </Stat>
              </Flex>
            </CardBody>
          </Card>

          <Card bg={cardBgColor} borderColor={borderColor} borderWidth="1px">
            <CardBody>
              <Flex align="center">
                <Icon as={FaExchangeAlt} boxSize={10} color="#F0B90B" mr={4} />
                <Stat>
                  <StatLabel color="#848E9C">Total Withdrawals</StatLabel>
                  <StatNumber color="#EAECEF">${totalWithdrawals.toLocaleString()}</StatNumber>
                  <StatHelpText color="#F6465D">-2.1% from last month</StatHelpText>
                </Stat>
              </Flex>
            </CardBody>
          </Card>

          <Card bg={cardBgColor} borderColor={borderColor} borderWidth="1px">
            <CardBody>
              <Flex align="center">
                <Icon as={FaUsers} boxSize={10} color="#F0B90B" mr={4} />
                <Stat>
                  <StatLabel color="#848E9C">Active Users</StatLabel>
                  <StatNumber color="#EAECEF">{activeUsers}</StatNumber>
                  <StatHelpText color="#0ECB81">+12 new this week</StatHelpText>
                </Stat>
              </Flex>
            </CardBody>
          </Card>

          <Card bg={cardBgColor} borderColor={borderColor} borderWidth="1px">
            <CardBody>
              <Flex align="center">
                <Icon as={FaChartLine} boxSize={10} color="#F0B90B" mr={4} />
                <Stat>
                  <StatLabel color="#848E9C">Pending Transactions</StatLabel>
                  <StatNumber color="#EAECEF">{pendingTransactions}</StatNumber>
                  <StatHelpText color={pendingTransactions > 5 ? "#F6465D" : "#0ECB81"}>
                    {pendingTransactions > 5 ? "Needs attention" : "All good"}
                  </StatHelpText>
                </Stat>
              </Flex>
            </CardBody>
          </Card>
        </Grid>

        <Tabs variant="enclosed" colorScheme="yellow" mb={6}>
          <TabList borderColor={borderColor}>
            <Tab color="#EAECEF" _selected={{ color: "#F0B90B", bg: cardBgColor, borderColor: borderColor }}>Transactions</Tab>
            <Tab color="#EAECEF" _selected={{ color: "#F0B90B", bg: cardBgColor, borderColor: borderColor }}>Users</Tab>
            <Tab color="#EAECEF" _selected={{ color: "#F0B90B", bg: cardBgColor, borderColor: borderColor }}>Settings</Tab>
          </TabList>

          <TabPanels>
            {/* Transactions Tab */}
            <TabPanel p={0} pt={4}>
              <Box bg={bgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
                <Flex justify="space-between" align="center" mb={4} flexDir={{ base: "column", md: "row" }} gap={4}>
                  <InputGroup maxW={{ base: "100%", md: "300px" }}>
                    <InputLeftElement pointerEvents="none">
                      <SearchIcon color="#848E9C" />
                    </InputLeftElement>
                    <Input
                      placeholder="Search by user or wallet"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      bg={cardBgColor}
                      borderColor={borderColor}
                      color="#EAECEF"
                    />
                  </InputGroup>

                  <HStack>
                    <Text color="#848E9C" fontSize="sm">Filter:</Text>
                    <Select
                      value={filterStatus}
                      onChange={(e) => setFilterStatus(e.target.value)}
                      width="150px"
                      bg={cardBgColor}
                      borderColor={borderColor}
                      color="#EAECEF"
                    >
                      <option value="all">All</option>
                      <option value="pending">Pending</option>
                      <option value="approved">Approved</option>
                      <option value="rejected">Rejected</option>
                    </Select>
                  </HStack>
                </Flex>

                <Box overflowX="auto" borderRadius="md" border="1px solid" borderColor={borderColor}>
                  <Table variant="simple" size="md" minW="1000px">
                    <Thead bg={cardBgColor}>
                      <Tr>
                        <Th color="#848E9C" borderColor={borderColor} minW="100px" maxW="100px">ID</Th>
                        <Th color="#848E9C" borderColor={borderColor} minW="150px" maxW="180px">User</Th>
                        <Th color="#848E9C" borderColor={borderColor} minW="120px" maxW="150px">Amount</Th>
                        <Th color="#848E9C" borderColor={borderColor} minW="100px" maxW="120px">Type</Th>
                        <Th color="#848E9C" borderColor={borderColor} minW="120px" maxW="150px">Date</Th>
                        <Th color="#848E9C" borderColor={borderColor} minW="100px" maxW="120px">Status</Th>
                        <Th color="#848E9C" borderColor={borderColor} minW="150px">Actions</Th>
                      </Tr>
                    </Thead>
                    <Tbody>
                      {filteredTransactions.map((tx) => (
                        <Tr key={tx.id} _hover={{ bg: "rgba(240, 185, 11, 0.05)" }}>
                          <Td color="#EAECEF" borderColor={borderColor} maxW="100px">
                            <Text fontSize="sm" fontFamily="mono" isTruncated>
                              {tx.id.length > 8 ? `${tx.id.substring(0, 8)}...` : tx.id}
                            </Text>
                          </Td>
                          <Td color="#EAECEF" borderColor={borderColor} maxW="180px">
                            <Text fontSize="sm" fontWeight="medium" isTruncated>
                              {tx.user}
                            </Text>
                          </Td>
                          <Td color="#EAECEF" borderColor={borderColor} maxW="150px">
                            <Text fontSize="sm" fontWeight="bold">
                              ${tx.amount}
                            </Text>
                          </Td>
                          <Td borderColor={borderColor} maxW="120px">
                            <Badge
                              colorScheme={tx.type === 'deposit' ? 'green' : 'red'}
                              borderRadius="full"
                              px={3}
                              py={1}
                              fontSize="xs"
                              fontWeight="bold"
                            >
                              {tx.type}
                            </Badge>
                          </Td>
                          <Td color="#EAECEF" borderColor={borderColor} maxW="150px">
                            <Text fontSize="sm" isTruncated>
                              {tx.date}
                            </Text>
                          </Td>
                          <Td borderColor={borderColor} maxW="120px">
                            <Badge
                              colorScheme={
                                tx.status === 'approved' ? 'green' :
                                tx.status === 'pending' ? 'yellow' : 'red'
                              }
                              borderRadius="full"
                              px={3}
                              py={1}
                              fontSize="xs"
                              fontWeight="bold"
                            >
                              {tx.status}
                            </Badge>
                          </Td>
                          <Td borderColor={borderColor} minW="150px">
                            <Stack direction={{ base: "column", lg: "row" }} spacing={2}>
                              <Button
                                size="sm"
                                colorScheme="blue"
                                onClick={() => navigate(`/admin/transaction/${tx.id}`)}
                                minW="50px"
                              >
                                View
                              </Button>

                              {tx.status === 'pending' && (
                                <>
                                  <Button
                                    size="sm"
                                    colorScheme="green"
                                    leftIcon={<CheckIcon />}
                                    onClick={() => handleApproveTransaction(tx.id)}
                                    minW="80px"
                                  >
                                    Approve
                                  </Button>
                                  <Button
                                    size="sm"
                                    colorScheme="red"
                                    leftIcon={<CloseIcon />}
                                    onClick={() => handleRejectTransaction(tx.id)}
                                    minW="70px"
                                  >
                                    Reject
                                  </Button>
                                </>
                              )}
                            </Stack>
                          </Td>
                        </Tr>
                      ))}
                    </Tbody>
                  </Table>
                </Box>

                {filteredTransactions.length === 0 && (
                  <Box textAlign="center" py={10}>
                    <Text color="#848E9C">No transactions found</Text>
                  </Box>
                )}
              </Box>
            </TabPanel>

            {/* Users Tab */}
            <TabPanel p={0} pt={4}>
              <Box bg={bgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
                <Flex justify="space-between" align="center" mb={4}>
                  <InputGroup maxW="300px">
                    <InputLeftElement pointerEvents="none">
                      <SearchIcon color="#848E9C" />
                    </InputLeftElement>
                    <Input
                      placeholder="Search users"
                      bg={cardBgColor}
                      borderColor={borderColor}
                      color="#EAECEF"
                    />
                  </InputGroup>
                </Flex>

                <Box overflowX="auto" borderRadius="md" border="1px solid" borderColor={borderColor}>
                  <Table variant="simple" size="md" minW="1100px">
                    <Thead bg={cardBgColor}>
                      <Tr>
                        <Th color="#848E9C" borderColor={borderColor} minW="100px" maxW="100px">ID</Th>
                        <Th color="#848E9C" borderColor={borderColor} minW="150px" maxW="180px">Name</Th>
                        <Th color="#848E9C" borderColor={borderColor} minW="200px" maxW="250px">Email</Th>
                        <Th color="#848E9C" borderColor={borderColor} minW="120px" maxW="150px">Join Date</Th>
                        <Th color="#848E9C" borderColor={borderColor} minW="100px" maxW="120px">Status</Th>
                        <Th color="#848E9C" borderColor={borderColor} minW="130px" maxW="150px">Total Deposits</Th>
                        <Th color="#848E9C" borderColor={borderColor} minW="120px">Actions</Th>
                      </Tr>
                    </Thead>
                    <Tbody>
                      {users.map((user) => (
                        <Tr key={user.id} _hover={{ bg: "rgba(240, 185, 11, 0.05)" }}>
                          <Td color="#EAECEF" borderColor={borderColor} maxW="100px">
                            <Text fontSize="sm" fontFamily="mono" isTruncated>
                              {user.id.length > 8 ? `${user.id.substring(0, 8)}...` : user.id}
                            </Text>
                          </Td>
                          <Td color="#EAECEF" borderColor={borderColor} maxW="180px">
                            <Text fontSize="sm" fontWeight="medium" isTruncated>
                              {user.name}
                            </Text>
                          </Td>
                          <Td color="#EAECEF" borderColor={borderColor} maxW="250px">
                            <Text fontSize="sm" isTruncated>
                              {user.email}
                            </Text>
                          </Td>
                          <Td color="#EAECEF" borderColor={borderColor} maxW="150px">
                            <Text fontSize="sm" isTruncated>
                              {user.joinDate}
                            </Text>
                          </Td>
                          <Td borderColor={borderColor} maxW="120px">
                            <Badge
                              colorScheme={user.status === 'active' ? 'green' : 'red'}
                              borderRadius="full"
                              px={3}
                              py={1}
                              fontSize="xs"
                              fontWeight="bold"
                            >
                              {user.status}
                            </Badge>
                          </Td>
                          <Td color="#EAECEF" borderColor={borderColor} maxW="150px">
                            <Text fontSize="sm" fontWeight="bold">
                              ${user.totalDeposits}
                            </Text>
                          </Td>
                          <Td borderColor={borderColor} minW="120px">
                            <Button
                              size="sm"
                              colorScheme="blue"
                              onClick={() => toast({
                                title: "Coming Soon",
                                description: "User details page is under development",
                                status: "info",
                                duration: 3000,
                                isClosable: true,
                              })}
                              minW="100px"
                            >
                              View Details
                            </Button>
                          </Td>
                        </Tr>
                      ))}
                    </Tbody>
                  </Table>
                </Box>
              </Box>
            </TabPanel>

            {/* Settings Tab */}
            <TabPanel p={0} pt={4}>
              <Box bg={bgColor} p={6} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
                <VStack spacing={6} align="stretch">
                  <Heading size="md" color="#EAECEF" mb={2}>System Settings</Heading>

                  <Box>
                    <Text color="#EAECEF" fontWeight="medium" mb={2}>Commission Rate</Text>
                    <Flex align="center" maxW="300px">
                      <Input
                        type="number"
                        defaultValue="5"
                        bg={cardBgColor}
                        borderColor={borderColor}
                        color="#EAECEF"
                      />
                      <Text color="#EAECEF" ml={2}>%</Text>
                    </Flex>
                    <Text color="#848E9C" fontSize="sm" mt={1}>
                      This is the commission rate users will earn on their deposits
                    </Text>
                  </Box>

                  <Divider borderColor={borderColor} />

                  <Box>
                    <Text color="#EAECEF" fontWeight="medium" mb={2}>Bitcoin Wallet Address</Text>
                    <Input
                      defaultValue="******************************************"
                      maxW="500px"
                      bg={cardBgColor}
                      borderColor={borderColor}
                      color="#EAECEF"
                    />
                    <Text color="#848E9C" fontSize="sm" mt={1}>
                      This is the Bitcoin address shown to users for deposits
                    </Text>
                  </Box>

                  <Divider borderColor={borderColor} />

                  <Box>
                    <Text color="#EAECEF" fontWeight="medium" mb={2}>Minimum Deposit Amount</Text>
                    <Flex align="center" maxW="300px">
                      <Input
                        type="number"
                        defaultValue="100"
                        bg={cardBgColor}
                        borderColor={borderColor}
                        color="#EAECEF"
                      />
                      <Text color="#EAECEF" ml={2}>USD</Text>
                    </Flex>
                  </Box>

                  <Divider borderColor={borderColor} />

                  <Flex justify="flex-end">
                    <Button colorScheme="yellow" size="md">
                      Save Settings
                    </Button>
                  </Flex>
                </VStack>
              </Box>
            </TabPanel>
          </TabPanels>
        </Tabs>
      </Container>
    </Box>
  );
};

export default AdminDashboard;
