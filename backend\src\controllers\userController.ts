import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import User from '../models/userModel';
import { logger } from '../utils/logger';
import { catchAsync } from '../utils/errorHandler';
import { AppError } from '../utils/AppError';
import { cacheService } from '../services/cacheService';

// Generate JWT with improved security
const generateToken = (id: string) => {
  const secret = process.env.JWT_SECRET || 'fallback_secret';
  const expiresIn = process.env.JWT_EXPIRES_IN || '30d';

  return jwt.sign({ id }, secret, {
    expiresIn,
    algorithm: 'HS512' // <PERSON>ha g<PERSON> algoritma
  } as jwt.SignOptions);
};

// Validate email format
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Validate password strength
const isStrongPassword = (password: string): boolean => {
  const minLength = 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

  return password.length >= minLength &&
         hasUpperCase &&
         hasLowerCase &&
         hasNumbers &&
         hasSpecialChar;
};

// @desc    Register a new user
// @route   POST /api/users/register
// @access  Public
export const register = catchAsync(async (req: Request, res: Response) => {
  const { email, password, firstName, lastName, phoneNumber, country, city, referralCode, marketingConsent } = req.body;

  // Enhanced validation
  const validationErrors: any = {};

  if (!email || !isValidEmail(email)) {
    validationErrors.email = 'Geçerli bir email adresi giriniz';
  }
  if (!password || !isStrongPassword(password)) {
    validationErrors.password = 'Şifre en az 8 karakter uzunluğunda olmalı ve büyük harf, küçük harf, rakam ve özel karakter içermelidir';
  }
  if (!firstName?.trim()) {
    validationErrors.firstName = 'Ad alanı gereklidir';
  }
  if (!lastName?.trim()) {
    validationErrors.lastName = 'Soyad alanı gereklidir';
  }

  if (Object.keys(validationErrors).length > 0) {
    res.status(400).json({
      status: 'fail',
      message: 'Validasyon hatası',
      errors: validationErrors
    });
    return;
  }

  // Check if user exists
  const userExists = await User.findOne({ email: email.toLowerCase() });
  if (userExists) {
    throw new AppError('Bu email adresi zaten kayıtlı', 400);
  }

  // Validate referral code if provided
  let referredBy = null;
  let referrerId = null;
  if (referralCode) {
    const referrer = await User.findOne({ referralCode });
    if (!referrer) {
      throw new AppError('Geçersiz referans kodu', 400);
    }
    referredBy = referralCode;
    referrerId = referrer._id;
  }

  let user: any;
  try {
    // Create user
    user = await User.create({
      email: email.toLowerCase(),
      password,
      firstName: firstName.trim(),
      lastName: lastName.trim(),
      phoneNumber: phoneNumber?.trim(),
      country: country?.trim(),
      city: city?.trim(),
      referredBy,
      referrerId,
      level: 1,
      totalCommission: 0,
      marketingConsent: marketingConsent || false
    });
  } catch (error: any) {
    logger.error('User creation error:', error);
    throw new AppError(
      error.code === 11000
        ? 'Bu email adresi zaten kayıtlı'
        : 'Kayıt işlemi başarısız oldu. Lütfen tekrar deneyin.',
      400
    );
  }

  // Update referrer stats
  if (referredBy) {
    try {
      await User.findOneAndUpdate(
        { referralCode: referredBy },
        { $inc: { referralCount: 1, referralEarnings: 0 } }
      );
      logger.info(`User ${user._id} registered with referral code ${referredBy}`);
    } catch (error) {
      logger.error('Error updating referrer stats:', error);
      // Continue with registration even if referrer update fails
    }
  }

  // Generate token
  const token = generateToken(user._id?.toString() || '');

  // Set token in HTTP-only cookie
  res.cookie('token', token, {
    httpOnly: true, // Prevents JavaScript access
    secure: process.env.NODE_ENV === 'production', // HTTPS only in production
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax', // Use 'none' in production for cross-site requests
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    path: '/' // Available across the entire site
  });

  res.status(201).json({
    status: 'success',
    data: {
      _id: user._id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      phoneNumber: user.phoneNumber,
      country: user.country,
      city: user.city,
      referralCode: user.referralCode,
      marketingConsent: user.marketingConsent
      // Token is now sent in cookie, not in response body
    }
  });
});

// @desc    Auth user & get token
// @route   POST /api/users/login
// @access  Public
export const login = catchAsync(async (req: Request, res: Response) => {
  const { email, password } = req.body;

  // Input validation
  if (!email || !password) {
    res.status(400).json({
      status: 'fail',
      message: 'Lütfen tüm alanları doldurun',
      errors: {
        email: !email ? 'Email alanı zorunludur' : undefined,
        password: !password ? 'Şifre alanı zorunludur' : undefined
      }
    });
    return;
  }

  // Check for user email
  const user = await User.findOne({ email: email.toLowerCase() });

  if (!user || !(await user.comparePassword(password))) {
    // Log failed login attempt
    logger.warn(`Failed login attempt for email: ${email}`);

    throw new AppError('Email veya şifre hatalı', 401);
  }

  // Log successful login
  logger.info(`User ${user._id} logged in successfully`);

  // Update last login timestamp
  user.lastLogin = new Date();
  await user.save({ validateBeforeSave: false });

  // Generate token
  const token = generateToken(user._id?.toString() || '');

  // Set token in HTTP-only cookie
  res.cookie('token', token, {
    httpOnly: true, // Prevents JavaScript access
    secure: process.env.NODE_ENV === 'production', // HTTPS only in production
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax', // Use 'none' in production for cross-site requests
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    path: '/' // Available across the entire site
  });

  res.json({
    status: 'success',
    data: {
      _id: user._id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      phoneNumber: user.phoneNumber,
      country: user.country,
      city: user.city,
      walletAddress: user.walletAddress,
      kycVerified: user.kycVerified,
      twoFactorEnabled: user.twoFactorEnabled,
      referralCode: user.referralCode,
      referralCount: user.referralCount,
      referralEarnings: user.referralEarnings,
      marketingConsent: user.marketingConsent,
      isAdmin: user.isAdmin
      // Token is now sent in cookie, not in response body
    }
  });
});

// @desc    Logout user
// @route   POST /api/users/logout
// @access  Public
export const logout = catchAsync(async (req: Request, res: Response) => {
  // Clear the token cookie
  res.clearCookie('token', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
    path: '/'
  });

  // Clear the admin token cookie if it exists
  res.clearCookie('adminToken', {
    httpOnly: false,
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
    path: '/'
  });

  // Clear the ws_auth cookie if it exists
  res.clearCookie('ws_auth', {
    httpOnly: false,
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
    path: '/'
  });

  res.status(200).json({
    status: 'success',
    message: 'Başarıyla çıkış yapıldı'
  });
});

// @desc    Get user profile
// @route   GET /api/users/profile
// @access  Private
export const getProfile = catchAsync(async (req: Request, res: Response) => {
  const cacheKey = `user:profile:${req.user._id}`;

  // Try to get from cache first
  const cachedProfile = cacheService.get(cacheKey);
  if (cachedProfile) {
    res.json(cachedProfile);
    return;
  }

  const user = await User.findById(req.user._id);

  if (!user) {
    throw new AppError('Kullanıcı bulunamadı', 404);
  }

  const userProfile = {
    _id: user._id,
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    walletAddress: user.walletAddress,
    kycVerified: user.kycVerified,
    twoFactorEnabled: user.twoFactorEnabled,
    referralCode: user.referralCode,
    referralCount: user.referralCount,
    referralEarnings: user.referralEarnings,
  };

  // Cache the profile for 5 minutes
  cacheService.set(cacheKey, userProfile, 300);

  res.json(userProfile);
});

// @desc    Update user profile
// @route   PUT /api/users/profile
// @access  Private
export const updateProfile = catchAsync(async (req: Request, res: Response) => {
  const user = await User.findById(req.user._id);

  if (!user) {
    throw new AppError('Kullanıcı bulunamadı', 404);
  }

  // Validate email if it's being updated
  if (req.body.email && req.body.email !== user.email) {
    if (!isValidEmail(req.body.email)) {
      throw new AppError('Geçerli bir email adresi giriniz', 400);
    }

    // Check if email is already in use
    const existingUser = await User.findOne({ email: req.body.email.toLowerCase() });
    if (existingUser && existingUser._id?.toString() !== user._id?.toString()) {
      throw new AppError('Bu email adresi zaten kullanımda', 400);
    }
  }

  // Validate password if it's being updated
  if (req.body.password && !isStrongPassword(req.body.password)) {
    throw new AppError('Şifre en az 8 karakter uzunluğunda olmalı ve büyük harf, küçük harf, rakam ve özel karakter içermelidir', 400);
  }

  // Update user fields
  user.firstName = req.body.firstName || user.firstName;
  user.lastName = req.body.lastName || user.lastName;
  user.email = req.body.email ? req.body.email.toLowerCase() : user.email;
  user.walletAddress = req.body.walletAddress || user.walletAddress;

  if (req.body.password) {
    user.password = req.body.password;
  }

  const updatedUser = await user.save();

  // Clear user profile cache
  cacheService.delete(`user:profile:${user._id}`);

  // Generate new token
  const token = generateToken(updatedUser._id?.toString() || '');

  // Update token in HTTP-only cookie
  res.cookie('token', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
    maxAge: 30 * 24 * 60 * 60 * 1000,
    path: '/'
  });

  res.json({
    _id: updatedUser._id,
    firstName: updatedUser.firstName,
    lastName: updatedUser.lastName,
    email: updatedUser.email,
    walletAddress: updatedUser.walletAddress,
    kycVerified: updatedUser.kycVerified,
    twoFactorEnabled: updatedUser.twoFactorEnabled,
    referralCode: updatedUser.referralCode,
    referralCount: updatedUser.referralCount,
    referralEarnings: updatedUser.referralEarnings
    // Token is now sent in cookie, not in response body
  });
});

// @desc    Enable two-factor authentication
// @route   POST /api/users/enable-2fa
// @access  Private
export const enableTwoFactor = catchAsync(async (req: Request, res: Response) => {
  const user = await User.findById(req.user._id);

  if (!user) {
    throw new AppError('Kullanıcı bulunamadı', 404);
  }

  // In a real implementation, this would generate a 2FA secret and QR code
  // For demo purposes, we'll just toggle the 2FA status
  user.twoFactorEnabled = !user.twoFactorEnabled;
  await user.save();

  // Clear user profile cache
  cacheService.delete(`user:profile:${user._id}`);

  res.json({
    message: user.twoFactorEnabled
      ? 'İki faktörlü doğrulama etkinleştirildi'
      : 'İki faktörlü doğrulama devre dışı bırakıldı',
    twoFactorEnabled: user.twoFactorEnabled
  });
});

// @desc    Verify KYC documents
// @route   POST /api/users/verify-kyc
// @access  Private
export const verifyKyc = catchAsync(async (req: Request, res: Response) => {
  const user = await User.findById(req.user._id);

  if (!user) {
    throw new AppError('Kullanıcı bulunamadı', 404);
  }

  // In a real implementation, this would process KYC documents
  // For demo purposes, we'll just toggle the KYC status
  user.kycVerified = !user.kycVerified;
  await user.save();

  // Clear user profile cache
  cacheService.delete(`user:profile:${user._id}`);

  res.json({
    message: user.kycVerified
      ? 'KYC doğrulaması başarılı'
      : 'KYC doğrulaması iptal edildi',
    kycVerified: user.kycVerified
  });
});

// @desc    Get referral statistics
// @route   GET /api/users/referrals
// @access  Private
export const getReferrals = catchAsync(async (req: Request, res: Response) => {
  const cacheKey = `user:referrals:${req.user._id}`;

  // Try to get from cache first
  const cachedReferrals = cacheService.get(cacheKey);
  if (cachedReferrals) {
    res.json(cachedReferrals);
    return;
  }

  const user = await User.findById(req.user._id);

  if (!user) {
    throw new AppError('Kullanıcı bulunamadı', 404);
  }

  // Find users referred by this user
  const referredUsers = await User.find({ referredBy: user.referralCode })
    .select('firstName lastName email createdAt')
    .sort({ createdAt: -1 });

  const referralStats = {
    referralCode: user.referralCode,
    referralLink: `${process.env.FRONTEND_URL || 'https://shippingFinance.com'}/register?ref=${user.referralCode}`,
    referralCount: user.referralCount,
    referralEarnings: user.referralEarnings,
    referralRate: 3, // 3% referral rate
    referredUsers: referredUsers.map((u: any) => ({
      name: `${u.firstName} ${u.lastName}`,
      email: u.email,
      joinedAt: u.createdAt
    }))
  };

  // Cache the referrals for 5 minutes
  cacheService.set(cacheKey, referralStats, 300);

  res.json(referralStats);
});