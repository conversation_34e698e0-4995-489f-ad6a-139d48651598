import json
import os
import logging
import hashlib
import uuid
from datetime import datetime

class UserManager:
    """Kullanıcı yönetimi sınıfı"""
    
    def __init__(self, users_file):
        self.users_file = users_file
        self.logger = logging.getLogger('UserManager')
        self.users = self._load_users()
        self.logger.info("UserManager başlatıldı")
    
    def _load_users(self):
        """Kullanıcı verilerini yükle"""
        try:
            if os.path.exists(self.users_file):
                with open(self.users_file, 'r') as f:
                    return json.load(f)
            else:
                # Dosya yoksa demo kullanıcı oluştur
                demo_user = {
                    "demo": {
                        "id": "demo",
                        "username": "demo",
                        "password_hash": self._hash_password("demo123"),
                        "email": "<EMAIL>",
                        "firstName": "Demo",
                        "lastName": "User",
                        "walletAddress": "******************************************",
                        "kycVerified": False,
                        "twoFactorEnabled": False,
                        "created_at": datetime.now().isoformat(),
                        "last_login": None
                    }
                }
                
                # Klasör yoksa oluştur
                os.makedirs(os.path.dirname(self.users_file), exist_ok=True)
                
                with open(self.users_file, 'w') as f:
                    json.dump(demo_user, f, indent=4)
                return demo_user
        except Exception as e:
            self.logger.error(f"Kullanıcılar yüklenirken hata: {str(e)}")
            return {}
    
    def _save_users(self):
        """Kullanıcı verilerini kaydet"""
        try:
            # Klasör yoksa oluştur
            os.makedirs(os.path.dirname(self.users_file), exist_ok=True)
            
            with open(self.users_file, 'w') as f:
                json.dump(self.users, f, indent=4)
            return True
        except Exception as e:
            self.logger.error(f"Kullanıcılar kaydedilirken hata: {str(e)}")
            return False
    
    def _hash_password(self, password):
        """Şifreyi hashle"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def authenticate(self, username, password):
        """Kullanıcı girişi doğrula"""
        try:
            if username in self.users:
                user = self.users[username]
                password_hash = self._hash_password(password)
                
                if user["password_hash"] == password_hash:
                    # Başarılı giriş
                    user["last_login"] = datetime.now().isoformat()
                    self._save_users()
                    
                    # Hassas bilgileri çıkar
                    user_copy = user.copy()
                    user_copy.pop("password_hash", None)
                    
                    self.logger.info(f"Kullanıcı girişi başarılı: {username}")
                    return user_copy
            
            self.logger.warning(f"Kullanıcı girişi başarısız: {username}")
            return None
        except Exception as e:
            self.logger.error(f"Kimlik doğrulama hatası: {str(e)}")
            return None
    
    def register(self, username, password, email, first_name, last_name):
        """Yeni kullanıcı kaydı"""
        try:
            if username in self.users:
                self.logger.warning(f"Kullanıcı adı zaten var: {username}")
                return None
            
            user_id = str(uuid.uuid4())
            new_user = {
                "id": user_id,
                "username": username,
                "password_hash": self._hash_password(password),
                "email": email,
                "firstName": first_name,
                "lastName": last_name,
                "walletAddress": "",
                "kycVerified": False,
                "twoFactorEnabled": False,
                "created_at": datetime.now().isoformat(),
                "last_login": None
            }
            
            self.users[username] = new_user
            self._save_users()
            
            # Hassas bilgileri çıkar
            user_copy = new_user.copy()
            user_copy.pop("password_hash", None)
            
            self.logger.info(f"Yeni kullanıcı kaydedildi: {username}")
            return user_copy
        except Exception as e:
            self.logger.error(f"Kullanıcı kaydı hatası: {str(e)}")
            return None
    
    def update_user(self, username, data):
        """Kullanıcı bilgilerini güncelle"""
        try:
            if username not in self.users:
                self.logger.warning(f"Kullanıcı bulunamadı: {username}")
                return None
            
            user = self.users[username]
            
            # Şifre değişikliği varsa hashle
            if "password" in data:
                user["password_hash"] = self._hash_password(data["password"])
                data.pop("password")
            
            # Diğer alanları güncelle
            for key, value in data.items():
                if key != "password_hash" and key != "id" and key != "username":
                    user[key] = value
            
            self._save_users()
            
            # Hassas bilgileri çıkar
            user_copy = user.copy()
            user_copy.pop("password_hash", None)
            
            self.logger.info(f"Kullanıcı güncellendi: {username}")
            return user_copy
        except Exception as e:
            self.logger.error(f"Kullanıcı güncelleme hatası: {str(e)}")
            return None
