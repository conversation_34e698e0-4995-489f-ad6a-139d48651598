// This is a disabled version of the image optimization script
// It does nothing but log a message that optimization is disabled

console.log('Image optimization is disabled. Using original images.');
console.log('To re-enable image optimization, use the build:prod:with-images script');

// Export a dummy function that does nothing
export async function optimizeImages() {
  console.log('Image optimization skipped!');
  return true;
}

// Run the function
optimizeImages();
