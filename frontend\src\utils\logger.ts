interface LogMessage {
  message: string;
  level: 'error' | 'warn' | 'info' | 'debug';
  context?: Record<string, any>;
  timestamp?: string;
}

class Logger {
  private static instance: Logger;
  private readonly logsQueue: LogMessage[] = [];
  private readonly maxQueueSize = 100;
  private readonly apiEndpoint = `${import.meta.env.VITE_API_URL}/logs` || 'https://api.shpnfinance.com/api/logs';

  private constructor() {
    window.addEventListener('unload', () => this.flush());
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  private addToQueue(log: LogMessage) {
    this.logsQueue.push({
      ...log,
      timestamp: new Date().toISOString()
    });

    if (this.logsQueue.length >= this.maxQueueSize) {
      this.flush();
    }
  }

  private async flush() {
    if (this.logsQueue.length === 0) return;

    try {
      const logsToSend = [...this.logsQueue];
      this.logsQueue.length = 0;

      await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ logs: logsToSend })
      });
    } catch (error) {
      console.error('Failed to send logs to server:', error);
      // Store failed logs in localStorage as backup
      const failedLogs = localStorage.getItem('failedLogs');
      const logs = failedLogs ? [...JSON.parse(failedLogs), ...this.logsQueue] : this.logsQueue;
      localStorage.setItem('failedLogs', JSON.stringify(logs));
    }
  }

  private formatMessage(message: string, context?: Record<string, any>): string {
    return context ? `${message} ${JSON.stringify(context)}` : message;
  }

  public error(message: string, context?: Record<string, any>) {
    const formattedMessage = this.formatMessage(message, context);
    console.error(formattedMessage);

    this.addToQueue({
      message: formattedMessage,
      level: 'error',
      context
    });

    // For critical errors, flush immediately
    this.flush();
  }

  public warn(message: string, context?: Record<string, any>) {
    const formattedMessage = this.formatMessage(message, context);
    console.warn(formattedMessage);

    this.addToQueue({
      message: formattedMessage,
      level: 'warn',
      context
    });
  }

  public info(message: string, context?: Record<string, any>) {
    const formattedMessage = this.formatMessage(message, context);
    if (process.env.NODE_ENV === 'development') {
      console.info(formattedMessage);
    }

    this.addToQueue({
      message: formattedMessage,
      level: 'info',
      context
    });
  }

  public debug(message: string, context?: Record<string, any>) {
    if (process.env.NODE_ENV === 'development') {
      const formattedMessage = this.formatMessage(message, context);
      console.debug(formattedMessage);

      this.addToQueue({
        message: formattedMessage,
        level: 'debug',
        context
      });
    }
  }

  public async retryFailedLogs() {
    const failedLogs = localStorage.getItem('failedLogs');
    if (!failedLogs) return;

    try {
      const logs = JSON.parse(failedLogs);
      await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ logs })
      });
      localStorage.removeItem('failedLogs');
    } catch (error) {
      console.error('Failed to retry sending logs:', error);
    }
  }
}

export const logger = Logger.getInstance();