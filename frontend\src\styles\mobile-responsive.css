/* Enhanced Mobile-First Responsive Styles for Shipping Finance Platform */

/* Critical Mobile Viewport Fixes */
@media screen and (max-width: 767px) {
  /* Prevent zoom on input focus and ensure proper viewport handling */
  html {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
    font-size: 16px; /* Prevent iOS zoom */
  }

  body {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
    overflow-x: hidden;
    font-size: 14px;
    line-height: 1.5;
    min-width: 320px; /* Minimum supported width */
  }

  /* Allow text selection for inputs and content */
  input, textarea, [contenteditable], .selectable-text {
    -webkit-user-select: text;
    user-select: text;
  }

  /* Prevent horizontal scrolling issues */
  * {
    max-width: 100%;
    box-sizing: border-box;
  }

  /* Enhanced touch-friendly button sizes */
  button, .chakra-button {
    min-height: 44px !important;
    min-width: 44px !important;
    font-size: 14px !important;
    padding: 12px 16px !important;
    border-radius: 8px !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
  }

  /* Enhanced touch-friendly input sizes */
  input, .chakra-input, textarea, .chakra-textarea, select, .chakra-select {
    min-height: 44px !important;
    font-size: 16px !important; /* Prevents zoom on iOS */
    padding: 12px 16px !important;
    border-radius: 8px !important;
    line-height: 1.4 !important;
  }

  /* Specific input group fixes */
  .chakra-input-group .chakra-input {
    padding-right: 48px !important;
  }

  .chakra-input__right-element {
    width: 44px !important;
    height: 44px !important;
  }

  /* Select dropdown improvements */
  .chakra-select {
    background-position: right 12px center !important;
    background-size: 16px !important;
  }

  /* Enhanced modal optimizations */
  .chakra-modal__content {
    margin: 8px !important;
    max-height: calc(100vh - 16px) !important;
    border-radius: 16px !important;
    max-width: calc(100vw - 16px) !important;
    width: calc(100vw - 16px) !important;
  }

  .chakra-modal__body {
    padding: 16px !important;
    max-height: calc(100vh - 140px) !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }

  .chakra-modal__header {
    padding: 16px 16px 8px 16px !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    border-bottom: 1px solid #2B3139 !important;
  }

  .chakra-modal__footer {
    padding: 8px 16px 16px 16px !important;
    flex-direction: column !important;
    gap: 12px !important;
    border-top: 1px solid #2B3139 !important;
  }

  .chakra-modal__footer .chakra-button {
    width: 100% !important;
    order: 1;
  }

  .chakra-modal__footer .chakra-button[data-cancel] {
    order: 2;
  }

  /* Navigation optimizations */
  .chakra-drawer__content {
    width: 280px !important;
    max-width: 85vw !important;
  }

  /* Enhanced card optimizations */
  .chakra-card, .card {
    margin: 8px 0 !important;
    padding: 16px !important;
    border-radius: 12px !important;
    width: 100% !important;
    max-width: 100% !important;
    overflow: hidden !important;
  }

  /* Container and layout fixes */
  .chakra-container {
    padding-left: 16px !important;
    padding-right: 16px !important;
    max-width: 100% !important;
  }

  /* Text overflow and readability fixes */
  .text-truncate, .chakra-text[data-truncated] {
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
  }

  .text-wrap {
    white-space: normal !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    hyphens: auto !important;
  }

  /* Heading responsive sizes */
  h1, .chakra-heading[data-size="xl"], .chakra-heading[data-size="2xl"] {
    font-size: 1.75rem !important;
    line-height: 1.2 !important;
  }

  h2, .chakra-heading[data-size="lg"] {
    font-size: 1.5rem !important;
    line-height: 1.3 !important;
  }

  h3, .chakra-heading[data-size="md"] {
    font-size: 1.25rem !important;
    line-height: 1.4 !important;
  }

  h4, h5, h6, .chakra-heading[data-size="sm"] {
    font-size: 1.125rem !important;
    line-height: 1.4 !important;
  }

  /* Table optimizations */
  .chakra-table {
    font-size: 12px !important;
  }

  .chakra-table th,
  .chakra-table td {
    padding: 8px 4px !important;
  }

  /* Form optimizations */
  .chakra-form-control {
    margin-bottom: 16px !important;
  }

  .chakra-form-label {
    font-size: 14px !important;
    margin-bottom: 8px !important;
  }

  /* Enhanced grid and layout optimizations */
  .chakra-grid {
    gap: 12px !important;
    grid-template-columns: 1fr !important;
  }

  .chakra-simple-grid {
    grid-template-columns: 1fr !important;
    gap: 12px !important;
  }

  /* Force single column on mobile for complex grids */
  .chakra-grid[data-columns] {
    grid-template-columns: 1fr !important;
  }

  /* Stack optimizations */
  .chakra-stack {
    gap: 12px !important;
  }

  .chakra-stack[data-orientation="horizontal"] {
    flex-direction: column !important;
    gap: 8px !important;
  }

  /* Flex optimizations */
  .chakra-flex {
    flex-wrap: wrap !important;
    gap: 8px !important;
  }

  .chakra-flex[data-direction="row"] {
    flex-direction: column !important;
  }

  /* Responsive spacing utilities */
  .mobile-stack-vertical > * {
    width: 100% !important;
    margin-bottom: 12px !important;
  }

  .mobile-full-width {
    width: 100% !important;
    max-width: 100% !important;
  }

  /* Hide elements on mobile */
  .mobile-hidden {
    display: none !important;
  }

  /* Show only on mobile */
  .mobile-only {
    display: block !important;
  }

  /* Badge optimizations */
  .chakra-badge {
    font-size: 11px !important;
    padding: 4px 8px !important;
    min-height: 20px !important;
  }

  /* Icon optimizations */
  .chakra-icon {
    width: 20px !important;
    height: 20px !important;
  }

  /* Avatar optimizations */
  .chakra-avatar {
    width: 32px !important;
    height: 32px !important;
  }

  /* Menu optimizations */
  .chakra-menu__list {
    min-width: 200px !important;
    max-width: calc(100vw - 32px) !important;
  }

  .chakra-menu__menuitem {
    min-height: 44px !important;
    padding: 12px 16px !important;
  }

  /* Toast optimizations */
  .chakra-toast {
    margin: 16px !important;
    max-width: calc(100vw - 32px) !important;
  }

  /* Accordion optimizations */
  .chakra-accordion__button {
    min-height: 44px !important;
    padding: 12px 16px !important;
  }

  /* Tab optimizations */
  .chakra-tabs__tab {
    min-height: 44px !important;
    padding: 8px 12px !important;
    font-size: 13px !important;
  }

  /* Slider optimizations */
  .chakra-slider__thumb {
    width: 24px !important;
    height: 24px !important;
  }

  /* Switch optimizations */
  .chakra-switch__thumb {
    width: 20px !important;
    height: 20px !important;
  }

  /* Checkbox optimizations */
  .chakra-checkbox__control {
    width: 20px !important;
    height: 20px !important;
  }

  /* Radio optimizations */
  .chakra-radio__control {
    width: 20px !important;
    height: 20px !important;
  }

  /* Table optimizations for mobile */
  .chakra-table__container {
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
    border-radius: 8px !important;
    border: 1px solid #2B3139 !important;
    margin: 0 -16px !important;
  }

  .chakra-table {
    min-width: 800px !important;
    font-size: 13px !important;
  }

  .chakra-table th {
    background-color: #0B0E11 !important;
    position: sticky !important;
    top: 0 !important;
    z-index: 1 !important;
    padding: 12px 8px !important;
    font-size: 12px !important;
    font-weight: 600 !important;
  }

  .chakra-table td {
    padding: 12px 8px !important;
    font-size: 13px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  /* Enhanced admin panel and table improvements */
  .admin-table-wrapper {
    margin: 0 -16px !important;
    border-radius: 0 !important;
  }

  .admin-table-actions {
    min-width: 120px !important;
    max-width: 120px !important;
  }

  .admin-table-actions .chakra-stack {
    flex-direction: column !important;
    gap: 4px !important;
  }

  .admin-table-actions .chakra-button {
    width: 100% !important;
    min-width: 60px !important;
    font-size: 11px !important;
    padding: 6px 8px !important;
    min-height: 32px !important;
  }

  /* Page-specific optimizations */

  /* Homepage optimizations */
  .home-hero-section {
    padding: 16px 0 !important;
  }

  .home-hero-section .chakra-heading {
    font-size: 1.5rem !important;
    text-align: center !important;
  }

  .home-feature-grid {
    grid-template-columns: 1fr !important;
    gap: 16px !important;
  }

  /* Profile page optimizations */
  .profile-tabs .chakra-tabs__tablist {
    flex-wrap: wrap !important;
    justify-content: center !important;
  }

  .profile-tabs .chakra-tabs__tab {
    flex: 1 1 auto !important;
    min-width: 80px !important;
    font-size: 12px !important;
    padding: 8px 4px !important;
  }

  .profile-stats-grid {
    grid-template-columns: 1fr !important;
    gap: 12px !important;
  }

  .profile-wallet-cards {
    grid-template-columns: 1fr !important;
  }

  /* Investment cards optimizations */
  .investment-card {
    margin: 8px 0 !important;
    padding: 12px !important;
  }

  .investment-card .chakra-stat {
    text-align: center !important;
  }

  /* Transaction table mobile optimizations */
  .transaction-table-mobile {
    display: block !important;
  }

  .transaction-table-mobile .chakra-table {
    display: block !important;
  }

  .transaction-table-mobile .chakra-thead {
    display: none !important;
  }

  .transaction-table-mobile .chakra-tbody {
    display: block !important;
  }

  .transaction-table-mobile .chakra-tr {
    display: block !important;
    border: 1px solid #2B3139 !important;
    border-radius: 8px !important;
    margin-bottom: 12px !important;
    padding: 12px !important;
    background: #1E2329 !important;
  }

  .transaction-table-mobile .chakra-td {
    display: block !important;
    border: none !important;
    padding: 4px 0 !important;
    text-align: left !important;
  }

  .transaction-table-mobile .chakra-td:before {
    content: attr(data-label) ": " !important;
    font-weight: bold !important;
    color: #F0B90B !important;
    display: inline-block !important;
    width: 100px !important;
  }
}

/* Large Mobile (480px and up) */
@media screen and (min-width: 480px) and (max-width: 767px) {
  body {
    font-size: 15px;
  }

  button, .chakra-button {
    font-size: 15px !important;
  }

  .chakra-modal__content {
    margin: 24px !important;
    border-radius: 16px !important;
  }

  .chakra-modal__body {
    padding: 20px !important;
  }

  .chakra-modal__header {
    padding: 20px !important;
    font-size: 20px !important;
  }

  .chakra-drawer__content {
    width: 320px !important;
  }

  .chakra-card, .card {
    margin: 12px !important;
    padding: 20px !important;
  }
}

/* Tablet (768px and up) */
@media screen and (min-width: 768px) and (max-width: 1023px) {
  /* Tablet-specific optimizations */
  .chakra-modal__content {
    max-width: 600px !important;
    margin: 40px auto !important;
  }

  .chakra-drawer__content {
    width: 360px !important;
  }

  /* Touch targets can be slightly smaller on tablet */
  button, .chakra-button {
    min-height: 40px !important;
    min-width: 40px !important;
  }

  input, .chakra-input, textarea, .chakra-textarea {
    min-height: 40px !important;
  }
}

/* Desktop (1024px and up) */
@media screen and (min-width: 1024px) {
  /* Desktop optimizations */
  .chakra-modal__content {
    max-width: 800px !important;
  }

  /* Hover effects only on desktop */
  button:hover, .chakra-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(240, 185, 11, 0.3);
  }

  .chakra-card:hover, .card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
}

/* Landscape orientation optimizations */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .chakra-modal__content {
    max-height: calc(100vh - 20px) !important;
  }

  .chakra-modal__body {
    max-height: calc(100vh - 80px) !important;
  }
}

/* High DPI displays */
@media screen and (-webkit-min-device-pixel-ratio: 2),
       screen and (min-resolution: 192dpi) {
  /* Optimize for retina displays */
  .chakra-icon, svg {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Dark mode specific mobile optimizations */
@media (prefers-color-scheme: dark) {
  body {
    background-color: #0B0E11 !important;
    color: #EAECEF !important;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus improvements for keyboard navigation */
button:focus, .chakra-button:focus,
input:focus, .chakra-input:focus,
textarea:focus, .chakra-textarea:focus {
  outline: 2px solid #F0B90B !important;
  outline-offset: 2px !important;
}

/* Enhanced safe area handling for devices with notches */
@supports (padding: max(0px)) {
  .safe-area-top {
    padding-top: max(16px, env(safe-area-inset-top)) !important;
  }

  .safe-area-bottom {
    padding-bottom: max(16px, env(safe-area-inset-bottom)) !important;
  }

  .safe-area-left {
    padding-left: max(16px, env(safe-area-inset-left)) !important;
  }

  .safe-area-right {
    padding-right: max(16px, env(safe-area-inset-right)) !important;
  }

  .safe-area-all {
    padding-top: max(16px, env(safe-area-inset-top)) !important;
    padding-bottom: max(16px, env(safe-area-inset-bottom)) !important;
    padding-left: max(16px, env(safe-area-inset-left)) !important;
    padding-right: max(16px, env(safe-area-inset-right)) !important;
  }
}

/* Utility classes for responsive design */
.responsive-text {
  font-size: clamp(0.875rem, 2.5vw, 1rem) !important;
  line-height: 1.5 !important;
}

.responsive-heading {
  font-size: clamp(1.25rem, 4vw, 2rem) !important;
  line-height: 1.2 !important;
}

.responsive-subheading {
  font-size: clamp(1rem, 3vw, 1.5rem) !important;
  line-height: 1.3 !important;
}

/* Prevent layout shift utilities */
.prevent-layout-shift {
  contain: layout style paint !important;
}

.stable-width {
  width: 100% !important;
  min-width: 0 !important;
  max-width: 100% !important;
}

/* Enhanced accessibility for mobile */
@media (max-width: 767px) {
  /* Larger tap targets for better accessibility */
  .chakra-icon-button {
    min-width: 44px !important;
    min-height: 44px !important;
  }

  .chakra-close-button {
    min-width: 44px !important;
    min-height: 44px !important;
  }

  /* Better focus indicators */
  *:focus {
    outline: 2px solid #F0B90B !important;
    outline-offset: 2px !important;
  }

  /* Improved readability */
  .chakra-text {
    line-height: 1.6 !important;
  }

  /* Better spacing for touch interfaces */
  .chakra-list .chakra-list__item {
    padding: 12px 0 !important;
  }
}
