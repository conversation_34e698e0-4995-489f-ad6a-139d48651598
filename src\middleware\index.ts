import { Express } from 'express';
import { securityMiddleware } from './securityMiddleware';
import { performanceMiddleware } from './performanceMiddleware';
import { errorHandlerMiddleware } from './errorHandlerMiddleware';

/**
 * Apply all middleware to Express app
 * @param app Express application
 */
export const applyMiddleware = (app: Express): void => {
  // Apply security middleware first
  securityMiddleware(app);
  
  // Apply performance middleware
  performanceMiddleware(app);
  
  // Error handler middleware should be applied last
  // This is done after routes are defined
};

// Export individual middleware for direct use
export * from './authMiddleware';
export * from './securityMiddleware';
export * from './performanceMiddleware';
export * from './errorHandlerMiddleware';
