/**
 * Error logger utility for capturing and reporting JavaScript errors
 */

// Store errors for later analysis
const errors = [];

// Initialize error logging
export const initErrorLogging = () => {
  // Override console.error to capture errors
  const originalConsoleError = console.error;
  console.error = (...args) => {
    // Call original console.error
    originalConsoleError.apply(console, args);
    
    // Log the error
    try {
      const errorMessage = args.map(arg => {
        if (arg instanceof Error) {
          return `${arg.name}: ${arg.message}\n${arg.stack}`;
        } else if (typeof arg === 'object') {
          return JSON.stringify(arg, null, 2);
        } else {
          return String(arg);
        }
      }).join(' ');
      
      errors.push({
        type: 'console.error',
        message: errorMessage,
        timestamp: new Date().toISOString()
      });
      
      // Display error in UI for debugging
      displayErrorInUI(errorMessage);
    } catch (e) {
      originalConsoleError('Error in error logging:', e);
    }
  };
  
  // Capture unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    const errorInfo = {
      type: 'unhandledRejection',
      message: event.reason?.message || 'Unknown promise rejection',
      stack: event.reason?.stack,
      timestamp: new Date().toISOString()
    };
    
    errors.push(errorInfo);
    console.error('Unhandled Promise Rejection:', errorInfo);
    
    // Display error in UI for debugging
    displayErrorInUI(`Unhandled Promise Rejection: ${errorInfo.message}`);
  });
  
  // Capture global errors
  window.addEventListener('error', (event) => {
    const errorInfo = {
      type: 'globalError',
      message: event.message,
      source: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      stack: event.error?.stack,
      timestamp: new Date().toISOString()
    };
    
    errors.push(errorInfo);
    console.error('Global Error:', errorInfo);
    
    // Display error in UI for debugging
    displayErrorInUI(`Global Error: ${errorInfo.message} at ${errorInfo.source}:${errorInfo.lineno}:${errorInfo.colno}`);
    
    // Don't prevent default error handling
    return false;
  });
  
  console.log('Error logging initialized');
};

// Display error in UI for debugging
const displayErrorInUI = (message) => {
  // Only in development or if explicitly enabled
  if (import.meta.env.DEV || import.meta.env.VITE_SHOW_ERRORS === 'true') {
    const errorDiv = document.createElement('div');
    errorDiv.style.position = 'fixed';
    errorDiv.style.bottom = '10px';
    errorDiv.style.right = '10px';
    errorDiv.style.backgroundColor = 'rgba(255, 0, 0, 0.8)';
    errorDiv.style.color = 'white';
    errorDiv.style.padding = '10px';
    errorDiv.style.borderRadius = '5px';
    errorDiv.style.maxWidth = '80%';
    errorDiv.style.maxHeight = '200px';
    errorDiv.style.overflow = 'auto';
    errorDiv.style.zIndex = '9999';
    errorDiv.style.fontSize = '12px';
    errorDiv.style.fontFamily = 'monospace';
    errorDiv.innerHTML = `<strong>Error:</strong> ${message}`;
    
    // Add close button
    const closeButton = document.createElement('button');
    closeButton.innerHTML = '×';
    closeButton.style.position = 'absolute';
    closeButton.style.top = '5px';
    closeButton.style.right = '5px';
    closeButton.style.background = 'none';
    closeButton.style.border = 'none';
    closeButton.style.color = 'white';
    closeButton.style.fontSize = '16px';
    closeButton.style.cursor = 'pointer';
    closeButton.onclick = () => errorDiv.remove();
    errorDiv.appendChild(closeButton);
    
    document.body.appendChild(errorDiv);
    
    // Auto-remove after 10 seconds
    setTimeout(() => {
      errorDiv.remove();
    }, 10000);
  }
};

// Get all captured errors
export const getErrors = () => {
  return [...errors];
};

// Clear all captured errors
export const clearErrors = () => {
  errors.length = 0;
};

export default {
  initErrorLogging,
  getErrors,
  clearErrors
};
