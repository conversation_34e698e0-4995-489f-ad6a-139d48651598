import { Request, Response, NextFunction } from 'express';
import { cacheService } from '../services/cacheService';
import { logger } from '../utils/logger';

interface CacheOptions {
  ttl?: number;
  keyPrefix?: string;
  keyGenerator?: (req: Request) => string;
}

/**
 * Middleware to cache API responses
 * @param options Cache options
 */
export const cacheMiddleware = (options: CacheOptions = {}) => {
  const {
    ttl = 300, // Default TTL: 5 minutes
    keyPrefix = 'api:cache:',
    keyGenerator = (req: Request) => `${req.originalUrl || req.url}`
  } = options;

  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    // Skip caching for non-GET requests
    if (req.method !== 'GET') {
      return next();
    }

    // Generate cache key
    const key = `${keyPrefix}${keyGenerator(req)}`;

    try {
      // Try to get from cache
      const cachedData = cacheService.get(key);

      if (cachedData) {
        logger.debug(`Cache hit for ${key}`);
        res.json(cachedData);
        return;
      }

      // Cache miss - store original res.json method
      const originalJson = res.json;

      // Override res.json method to cache the response
      res.json = function(body) {
        // Store in cache
        cacheService.set(key, body, ttl);
        logger.debug(`Cached response for ${key}`);

        // Call original method
        return originalJson.call(this, body);
      };

      next();
    } catch (error) {
      logger.error(`Cache error for ${key}:`, error);
      next();
    }
  };
};

/**
 * Middleware to clear cache for specific routes
 * @param keyPrefix Cache key prefix to clear
 */
export const clearCache = (keyPrefix: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    // After response is sent, clear the cache
    res.on('finish', () => {
      const count = cacheService.invalidateByPrefix(keyPrefix);
      logger.debug(`Cleared ${count} cache entries with prefix ${keyPrefix}`);
    });

    next();
  };
};
