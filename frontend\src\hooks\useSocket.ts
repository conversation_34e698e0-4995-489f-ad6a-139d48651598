import { useState, useEffect, useCallback, useRef } from 'react';
import { socketService } from '../services/socket-service';
import { SocketService } from '../utils/socketService';

// <PERSON><PERSON><PERSON> nghĩa kiểu dữ liệu cho callback
type MessageCallback = (data: any) => void;

/**
 * Hook để sử dụng Socket.IO trong các component React
 * Cung cấp các phương thức để kết nối, gửi và nhận tin nhắn từ Socket.IO
 */
const useSocket = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  // Sử dụng ref để lưu trữ các unsubscribe functions
  const unsubscribeFunctions = useRef<(() => void)[]>([]);
  
  // Khởi tạo Socket.IO service
  const socketServiceInstance = SocketService.getInstance();

  // Kiểm tra trạng thái kết nối khi hook được khởi tạo
  useEffect(() => {
    // Kiểm tra trạng thái kết nối hiện tại
    const currentConnectionStatus = socketService.isSocketConnected();
    setIsConnected(currentConnectionStatus);
    
    // Đăng ký callback để theo dõi trạng thái kết nối
    const unsubscribe = socketServiceInstance.subscribeToStatus((status) => {
      setIsConnected(status);
      if (!status) {
        setError(new Error('Socket.IO disconnected'));
      } else {
        setError(null);
      }
    });
    
    // Lưu hàm unsubscribe để cleanup
    unsubscribeFunctions.current.push(unsubscribe);
    
    // Cleanup khi component unmount
    return () => {
      // Gọi tất cả các hàm unsubscribe đã đăng ký
      unsubscribeFunctions.current.forEach(unsub => unsub());
      unsubscribeFunctions.current = [];
    };
  }, [socketServiceInstance]);

  /**
   * Kết nối đến Socket.IO server
   */
  const connect = useCallback(async () => {
    if (isConnected || isConnecting) return;
    
    try {
      setIsConnecting(true);
      setError(null);
      
      // Sử dụng socketService từ services/socket-service.ts
      await socketService.connect();
      
      setIsConnected(true);
    } catch (err) {
      setError(err as Error);
      console.error('Socket.IO connection error:', err);
    } finally {
      setIsConnecting(false);
    }
  }, [isConnected, isConnecting]);

  /**
   * Ngắt kết nối Socket.IO
   */
  const disconnect = useCallback(() => {
    socketService.disconnect();
    setIsConnected(false);
  }, []);

  /**
   * Đăng ký callback cho một loại tin nhắn
   * @param type Loại tin nhắn
   * @param callback Hàm callback được gọi khi nhận được tin nhắn
   * @returns Hàm để hủy đăng ký
   */
  const subscribe = useCallback((type: string, callback: MessageCallback) => {
    const unsubscribe = socketServiceInstance.subscribe(type, callback);
    unsubscribeFunctions.current.push(unsubscribe);
    return unsubscribe;
  }, [socketServiceInstance]);

  /**
   * Gửi tin nhắn đến Socket.IO server
   * @param type Loại tin nhắn
   * @param payload Dữ liệu tin nhắn
   */
  const send = useCallback((type: string, payload: any = {}) => {
    socketServiceInstance.send({
      type,
      payload
    });
  }, [socketServiceInstance]);

  /**
   * Đăng ký nhận cập nhật về giao dịch
   * @param filters Bộ lọc cho các cập nhật giao dịch
   */
  const subscribeToTransactionUpdates = useCallback((filters: any = {}) => {
    socketServiceInstance.send({
      type: 'subscribe_transaction_updates',
      payload: { filters }
    });
  }, [socketServiceInstance]);

  /**
   * Đăng ký nhận cập nhật về tiền gửi
   * @param filters Bộ lọc cho các cập nhật tiền gửi
   */
  const subscribeToDepositUpdates = useCallback((filters: any = {}) => {
    socketServiceInstance.send({
      type: 'subscribe_deposit_updates',
      payload: { filters }
    });
  }, [socketServiceInstance]);

  /**
   * Đăng ký nhận cập nhật về rút tiền
   * @param filters Bộ lọc cho các cập nhật rút tiền
   */
  const subscribeToWithdrawalUpdates = useCallback((filters: any = {}) => {
    socketServiceInstance.send({
      type: 'subscribe_withdrawal_updates',
      payload: { filters }
    });
  }, [socketServiceInstance]);

  /**
   * Đăng ký nhận cập nhật về đầu tư
   * @param filters Bộ lọc cho các cập nhật đầu tư
   */
  const subscribeToInvestmentUpdates = useCallback((filters: any = {}) => {
    socketServiceInstance.send({
      type: 'subscribe_investment_updates',
      payload: { filters }
    });
  }, [socketServiceInstance]);

  /**
   * Đăng ký nhận cập nhật dành cho admin
   */
  const subscribeToAdminUpdates = useCallback(() => {
    socketServiceInstance.send({
      type: 'subscribe_admin_updates',
      payload: {}
    });
  }, [socketServiceInstance]);

  return {
    isConnected,
    isConnecting,
    error,
    connect,
    disconnect,
    subscribe,
    send,
    subscribeToTransactionUpdates,
    subscribeToDepositUpdates,
    subscribeToWithdrawalUpdates,
    subscribeToInvestmentUpdates,
    subscribeToAdminUpdates
  };
};

export default useSocket;
