import React from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  Grid,
  GridItem,
  Image,
  Flex,
  Icon,
  Divider,
  List,
  ListItem,
  ListIcon,
  Button,
  HStack,
} from '@chakra-ui/react';
import { FaShip, FaGlobe, FaChartLine, FaCheckCircle, FaUsers, FaHandshake, FaArrowRight, FaMoneyBillWave } from 'react-icons/fa';
import { Link as RouterLink } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const About = () => {
  const { t } = useTranslation();

  // Colors
  const bgColor = "#0B0E11";
  const cardBgColor = "#1E2329";
  const primaryColor = "#F0B90B";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const borderColor = "#2B3139";

  return (
    <Box bg={bgColor} minH="100vh">
      {/* Hero Section */}
      <Box
        bg={`linear-gradient(rgba(11, 14, 17, 0.8), rgba(11, 14, 17, 0.9)), url('/images/global-trade.jpg')`}
        bgSize="cover"
        bgPosition="center"
        py={20}
      >
        <Container maxW="container.xl">
          <VStack spacing={6} align="center" textAlign="center" maxW="800px" mx="auto">
            <Heading
              as="h1"
              size="2xl"
              color={primaryColor}
              lineHeight="1.2"
            >
              {t('about.hero.title', 'About Us')}
            </Heading>

            <Text fontSize="xl" color={textColor}>
              {t('about.hero.description', 'Shipping Finance is an innovative financial platform that offers investors stable and sustainable returns by leveraging opportunities in international trade.')}
            </Text>
          </VStack>
        </Container>
      </Box>

      {/* Company Info Section */}
      <Box py={16}>
        <Container maxW="container.xl">
          <Grid templateColumns={{ base: "1fr", lg: "1fr 1fr" }} gap={10}>
            <GridItem>
              <VStack align="flex-start" spacing={6}>
                <Heading color={textColor}>{t('about.vision.title', 'Our Vision')}</Heading>
                <Text fontSize="lg" color={secondaryTextColor}>
                  {t('about.vision.description', 'Our vision at Shipping Finance is to provide our investors with a reliable, transparent, and high-yield investment platform by combining the opportunities offered by international trade with innovations in technology and finance.')}
                </Text>

                <Heading color={textColor} size="md" pt={4}>{t('about.mission.title', 'Our Mission')}</Heading>
                <Text fontSize="lg" color={secondaryTextColor}>
                  {t('about.mission.description', 'Our mission is to be a reliable partner in the financial freedom journey of cryptocurrency investors by offering an alternative and sustainable earnings model through our global trade network and professional team.')}
                </Text>

                <Heading color={textColor} size="md" pt={4}>{t('about.values.title', 'Our Values')}</Heading>
                <List spacing={3}>
                  <ListItem color={secondaryTextColor}>
                    <ListIcon as={FaCheckCircle} color={primaryColor} />
                    {t('about.values.transparency', 'Transparency and trust-focused business model')}
                  </ListItem>
                  <ListItem color={secondaryTextColor}>
                    <ListIcon as={FaCheckCircle} color={primaryColor} />
                    {t('about.values.investorSatisfaction', 'Investor satisfaction and sustainable earnings')}
                  </ListItem>
                  <ListItem color={secondaryTextColor}>
                    <ListIcon as={FaCheckCircle} color={primaryColor} />
                    {t('about.values.innovation', 'Innovative and technology-focused approach')}
                  </ListItem>
                  <ListItem color={secondaryTextColor}>
                    <ListIcon as={FaCheckCircle} color={primaryColor} />
                    {t('about.values.internationalStandards', 'Operation management at international standards')}
                  </ListItem>
                  <ListItem color={secondaryTextColor}>
                    <ListIcon as={FaCheckCircle} color={primaryColor} />
                    {t('about.values.socialResponsibility', 'Social and environmental responsibility')}
                  </ListItem>
                </List>
              </VStack>
            </GridItem>

            <GridItem>
              <Box
                bg={cardBgColor}
                p={8}
                borderRadius="lg"
                borderWidth="1px"
                borderColor={borderColor}
                height="100%"
              >
                <VStack spacing={6} align="flex-start">
                  <Heading size="md" color={primaryColor}>{t('about.businessModel.title', 'Faaliyet Modelimiz')}</Heading>

                  <Text color={textColor}>
                    {t('about.businessModel.description', 'Shipping Finance, düşük maliyetli ülkelerden (örneğin Çin, Mısır gibi) temin edilen ürünlerin yüksek talep ve fiyat avantajı sunan pazarlarda (özellikle Avrupa ve diğer gelişmiş bölgelerde) satılması prensibine dayanan bir iş modeli ile çalışmaktadır.')}
                  </Text>

                  <Text color={textColor}>
                    {t('about.businessModel.returns', 'Bu ticaret hacmi sayesinde yüksek kârlılık elde edilmekte ve yatırımcılarımıza günlük ortalama %1 oranında getiri sağlanmaktadır.')}
                  </Text>

                  <Divider borderColor={borderColor} />

                  <Heading size="md" color={primaryColor}>{t('about.howItWorks.title', 'Nasıl Çalışır?')}</Heading>

                  <List spacing={4}>
                    <ListItem color={textColor}>
                      <HStack align="flex-start" spacing={4}>
                        <Flex
                          bg={`${primaryColor}20`}
                          p={2}
                          borderRadius="md"
                          mt={1}
                        >
                          <Icon as={FaShip} color={primaryColor} boxSize={4} />
                        </Flex>
                        <VStack align="flex-start" spacing={1}>
                          <Text color={textColor} fontWeight="bold">{t('about.howItWorks.productSourcing.title', 'Ürün Tedariki')}</Text>
                          <Text color={secondaryTextColor}>
                            {t('about.howItWorks.productSourcing.description', 'Düşük maliyetli üretim bölgelerinden kaliteli ürünlerin tedarik edilmesi')}
                          </Text>
                        </VStack>
                      </HStack>
                    </ListItem>

                    <ListItem color={textColor}>
                      <HStack align="flex-start" spacing={4}>
                        <Flex
                          bg={`${primaryColor}20`}
                          p={2}
                          borderRadius="md"
                          mt={1}
                        >
                          <Icon as={FaGlobe} color={primaryColor} boxSize={4} />
                        </Flex>
                        <VStack align="flex-start" spacing={1}>
                          <Text color={textColor} fontWeight="bold">{t('about.howItWorks.logistics.title', 'Lojistik ve Dağıtım')}</Text>
                          <Text color={secondaryTextColor}>
                            {t('about.howItWorks.logistics.description', 'Global lojistik ağımız ile ürünlerin hedef pazarlara ulaştırılması')}
                          </Text>
                        </VStack>
                      </HStack>
                    </ListItem>

                    <ListItem color={textColor}>
                      <HStack align="flex-start" spacing={4}>
                        <Flex
                          bg={`${primaryColor}20`}
                          p={2}
                          borderRadius="md"
                          mt={1}
                        >
                          <Icon as={FaChartLine} color={primaryColor} boxSize={4} />
                        </Flex>
                        <VStack align="flex-start" spacing={1}>
                          <Text color={textColor} fontWeight="bold">{t('about.howItWorks.sales.title', 'Satış ve Kâr')}</Text>
                          <Text color={secondaryTextColor}>
                            {t('about.howItWorks.sales.description', 'Yüksek talep bölgelerinde optimum fiyatlandırma ile satış ve kâr elde edilmesi')}
                          </Text>
                        </VStack>
                      </HStack>
                    </ListItem>

                    <ListItem color={textColor}>
                      <HStack align="flex-start" spacing={4}>
                        <Flex
                          bg={`${primaryColor}20`}
                          p={2}
                          borderRadius="md"
                          mt={1}
                        >
                          <Icon as={FaUsers} color={primaryColor} boxSize={4} />
                        </Flex>
                        <VStack align="flex-start" spacing={1}>
                          <Text color={textColor} fontWeight="bold">{t('about.howItWorks.investorReturns.title', 'Yatırımcı Kazancı')}</Text>
                          <Text color={secondaryTextColor}>
                            {t('about.howItWorks.investorReturns.description', 'Elde edilen kârın günlük %1 oranında yatırımcılarla paylaşılması')}
                          </Text>
                        </VStack>
                      </HStack>
                    </ListItem>
                  </List>
                </VStack>
              </Box>
            </GridItem>
          </Grid>
        </Container>
      </Box>



      {/* Özet Bölümü */}
      <Box py={16} bg={bgColor}>
        <Container maxW="container.xl">
          <Grid templateColumns={{ base: "1fr", lg: "1fr 1fr" }} gap={10}>
            <GridItem>
              <Box
                bg={cardBgColor}
                p={8}
                borderRadius="lg"
                borderWidth="1px"
                borderColor={borderColor}
                height="100%"
              >
                <VStack spacing={6} align="flex-start">
                  <Heading size="md" color={primaryColor}>{t('about.summary.title', 'Hakkımızda (Özet)')}</Heading>

                  <Text color={textColor}>
                    {t('about.summary.description1', 'Shipping Finance, uluslararası ticaretin gücünü yatırımcılara sunan yenilikçi bir Finance platformudur.')}
                  </Text>

                  <Text color={textColor}>
                    {t('about.summary.description2', 'Kripto paralarınızı borsada bekletmek yerine, gerçek ticari faaliyetlerle büyütme fırsatı sunuyoruz.')}
                  </Text>

                  <Text color={textColor}>
                    {t('about.summary.description3', 'Global lojistik ağımız ve profesyonel ticaret operasyonlarımızla, yatırımlarınızı her gün daha ileri taşıyoruz.')}
                  </Text>




                </VStack>
              </Box>
            </GridItem>

            <GridItem>
              <Box
                bg={`linear-gradient(rgba(30, 35, 41, 0.9), rgba(30, 35, 41, 0.95)), url('/images/bitcoin-ship.jpg')`}
                bgSize="cover"
                bgPosition="center"
                p={8}
                borderRadius="lg"
                borderWidth="1px"
                borderColor={borderColor}
                height="100%"
                position="relative"
                overflow="hidden"
                boxShadow="0 10px 30px -5px rgba(0, 0, 0, 0.3)"
                _before={{
                  content: '""',
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  bgGradient: "linear(to-r, #F0B90B10, #F0B90B30)",
                  opacity: 0.2,
                  zIndex: 0
                }}
              >
                <VStack spacing={6} position="relative" zIndex={1}>
                  <Heading size="md" color={primaryColor}>{t('about.whyChooseUs.title', 'Why Choose Us?')}</Heading>

                  <List spacing={4}>
                    <ListItem color={textColor}>
                      <HStack align="flex-start" spacing={4}>
                        <Flex
                          bg={`${primaryColor}20`}
                          p={2}
                          borderRadius="md"
                          mt={1}
                        >
                          <Icon as={FaShip} color={primaryColor} boxSize={4} />
                        </Flex>
                        <VStack align="flex-start" spacing={1}>
                          <Text color={textColor} fontWeight="bold">{t('about.whyChooseUs.realTrading.title', 'Real Trading Activities')}</Text>
                          <Text color={secondaryTextColor}>
                            {t('about.whyChooseUs.realTrading.description', 'Real earnings from international trade')}
                          </Text>
                        </VStack>
                      </HStack>
                    </ListItem>

                    <ListItem color={textColor}>
                      <HStack align="flex-start" spacing={4}>
                        <Flex
                          bg={`${primaryColor}20`}
                          p={2}
                          borderRadius="md"
                          mt={1}
                        >
                          <Icon as={FaChartLine} color={primaryColor} boxSize={4} />
                        </Flex>
                        <VStack align="flex-start" spacing={1}>
                          <Text color={textColor} fontWeight="bold">{t('about.whyChooseUs.dailyReturns.title', 'Günlük %1 Kazanç')}</Text>
                          <Text color={secondaryTextColor}>
                            {t('about.whyChooseUs.dailyReturns.description', 'Kripto varlıklarınızdan her gün düzenli gelir')}
                          </Text>
                        </VStack>
                      </HStack>
                    </ListItem>

                    <ListItem color={textColor}>
                      <HStack align="flex-start" spacing={4}>
                        <Flex
                          bg={`${primaryColor}20`}
                          p={2}
                          borderRadius="md"
                          mt={1}
                        >
                          <Icon as={FaHandshake} color={primaryColor} boxSize={4} />
                        </Flex>
                        <VStack align="flex-start" spacing={1}>
                          <Text color={textColor} fontWeight="bold">{t('about.whyChooseUs.transparency.title', 'Transparency')}</Text>
                          <Text color={secondaryTextColor}>
                            {t('about.whyChooseUs.transparency.description', 'Full transparency in all operations and transactions')}
                          </Text>
                        </VStack>
                      </HStack>
                    </ListItem>
                  </List>


                </VStack>
              </Box>
            </GridItem>
          </Grid>
        </Container>
      </Box>


    </Box>
  );
};

export default About;
