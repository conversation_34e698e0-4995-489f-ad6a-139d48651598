/**
 * Comprehensive Withdrawal System Integration Test
 * 
 * This script tests the complete withdrawal transaction flow:
 * 1. User initiates withdrawal from frontend
 * 2. Backend processes withdrawal request
 * 3. Admin panel displays withdrawal for approval
 * 4. Admin approves/rejects withdrawal
 * 5. Real-time updates via WebSocket
 * 6. Balance validation with investment packages
 */

const mongoose = require('mongoose');
const axios = require('axios');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/cryptoyield', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    process.exit(1);
  }
};

// Test data
const testData = {
  testUser: {
    email: '<EMAIL>',
    password: 'testpassword123',
    firstName: 'Test',
    lastName: 'User'
  },
  testAdmin: {
    email: '<EMAIL>',
    password: 'adminpassword123',
    firstName: 'Admin',
    lastName: 'User',
    isAdmin: true
  },
  testWithdrawal: {
    asset: 'USDT',
    amount: 100,
    address: '******************************************',
    withdrawalType: 'interest',
    network: 'ethereum'
  }
};

// API base URL
const API_URL = process.env.API_URL || 'http://localhost:5000/api';

// Test functions
class WithdrawalSystemTest {
  constructor() {
    this.userToken = null;
    this.adminToken = null;
    this.testUserId = null;
    this.testWithdrawalId = null;
  }

  async runAllTests() {
    console.log('🚀 Starting Withdrawal System Integration Tests\n');

    try {
      await connectDB();
      
      // Phase 1: Setup
      await this.setupTestData();
      
      // Phase 2: User Authentication
      await this.testUserAuthentication();
      
      // Phase 3: Investment Package Setup
      await this.setupInvestmentPackages();
      
      // Phase 4: Balance Validation
      await this.testBalanceValidation();
      
      // Phase 5: Withdrawal Request
      await this.testWithdrawalRequest();
      
      // Phase 6: Admin Panel Integration
      await this.testAdminPanelIntegration();
      
      // Phase 7: Withdrawal Approval
      await this.testWithdrawalApproval();
      
      // Phase 8: Real-time Updates
      await this.testRealTimeUpdates();
      
      console.log('\n✅ All withdrawal system tests completed successfully!');
      
    } catch (error) {
      console.error('\n❌ Test failed:', error.message);
      console.error('Stack trace:', error.stack);
    } finally {
      await this.cleanup();
      process.exit(0);
    }
  }

  async setupTestData() {
    console.log('📋 Setting up test data...');
    
    // Import models
    const User = require('../src/models/userModel').default;
    const InvestmentPackage = require('../src/models/investmentPackageModel').default;
    const Wallet = require('../src/models/walletModel').default;
    
    // Clean up existing test data
    await User.deleteMany({ email: { $in: [testData.testUser.email, testData.testAdmin.email] } });
    
    // Create test user
    const testUser = await User.create(testData.testUser);
    this.testUserId = testUser._id;
    
    // Create test admin
    await User.create(testData.testAdmin);
    
    // Create wallet for test user
    await Wallet.create({
      userId: testUser._id,
      assets: [{
        symbol: 'USDT',
        balance: 0,
        interestBalance: 150, // Available for withdrawal
        commissionBalance: 50,
        mode: 'interest',
        network: 'ethereum'
      }],
      totalCommissionEarned: 50,
      totalInterestEarned: 150
    });
    
    console.log('✅ Test data setup completed');
  }

  async testUserAuthentication() {
    console.log('🔐 Testing user authentication...');
    
    // Test user login
    const userLoginResponse = await axios.post(`${API_URL}/auth/login`, {
      email: testData.testUser.email,
      password: testData.testUser.password
    });
    
    this.userToken = userLoginResponse.data.token;
    console.log('✅ User authentication successful');
    
    // Test admin login
    const adminLoginResponse = await axios.post(`${API_URL}/admin/login`, {
      email: testData.testAdmin.email,
      password: testData.testAdmin.password
    });
    
    this.adminToken = adminLoginResponse.data.token;
    console.log('✅ Admin authentication successful');
  }

  async setupInvestmentPackages() {
    console.log('💰 Setting up investment packages...');
    
    const InvestmentPackage = require('../src/models/investmentPackageModel').default;
    
    // Create active investment package with earnings
    await InvestmentPackage.create({
      userId: this.testUserId,
      amount: 1000,
      currency: 'USDT',
      status: 'active',
      totalEarned: 150,
      totalWithdrawn: 0,
      dailyRate: 0.002,
      createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      lastEarningDate: new Date()
    });
    
    console.log('✅ Investment packages setup completed');
  }

  async testBalanceValidation() {
    console.log('⚖️ Testing balance validation...');
    
    // Test investment balance endpoint
    const balanceResponse = await axios.get(`${API_URL}/investments/balances`, {
      headers: { Authorization: `Bearer ${this.userToken}` }
    });
    
    const balances = balanceResponse.data.data;
    const usdtBalance = balances.find(b => b.currency === 'USDT');
    
    if (!usdtBalance || usdtBalance.availableForWithdrawal < 100) {
      throw new Error('Insufficient balance for test withdrawal');
    }
    
    console.log('✅ Balance validation successful');
    
    // Test withdrawal eligibility
    const eligibilityResponse = await axios.post(`${API_URL}/investments/withdrawal-eligibility`, {
      currency: 'USDT',
      amount: 100
    }, {
      headers: { Authorization: `Bearer ${this.userToken}` }
    });
    
    if (!eligibilityResponse.data.data.isEligible) {
      throw new Error('User not eligible for withdrawal');
    }
    
    console.log('✅ Withdrawal eligibility check passed');
  }

  async testWithdrawalRequest() {
    console.log('📤 Testing withdrawal request...');
    
    const withdrawalResponse = await axios.post(`${API_URL}/wallets/withdraw`, testData.testWithdrawal, {
      headers: { Authorization: `Bearer ${this.userToken}` }
    });
    
    if (!withdrawalResponse.data.withdrawal) {
      throw new Error('Withdrawal request failed');
    }
    
    this.testWithdrawalId = withdrawalResponse.data.withdrawal._id;
    console.log('✅ Withdrawal request successful');
  }

  async testAdminPanelIntegration() {
    console.log('👨‍💼 Testing admin panel integration...');
    
    // Test admin withdrawals endpoint
    const adminWithdrawalsResponse = await axios.get(`${API_URL}/admin/withdrawals`, {
      headers: { Authorization: `Bearer ${this.adminToken}` }
    });
    
    const withdrawals = adminWithdrawalsResponse.data.withdrawals;
    const testWithdrawal = withdrawals.find(w => w.id === this.testWithdrawalId);
    
    if (!testWithdrawal) {
      throw new Error('Withdrawal not found in admin panel');
    }
    
    if (testWithdrawal.status !== 'pending') {
      throw new Error('Withdrawal status should be pending');
    }
    
    console.log('✅ Admin panel integration successful');
  }

  async testWithdrawalApproval() {
    console.log('✅ Testing withdrawal approval...');
    
    // Test withdrawal approval
    const approvalResponse = await axios.put(`${API_URL}/admin/withdrawals/${this.testWithdrawalId}/status`, {
      status: 'approved',
      adminNotes: 'Test approval',
      txHash: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef'
    }, {
      headers: { Authorization: `Bearer ${this.adminToken}` }
    });
    
    if (!approvalResponse.data.success) {
      throw new Error('Withdrawal approval failed');
    }
    
    console.log('✅ Withdrawal approval successful');
    
    // Verify status update
    const updatedWithdrawalsResponse = await axios.get(`${API_URL}/admin/withdrawals`, {
      headers: { Authorization: `Bearer ${this.adminToken}` }
    });
    
    const updatedWithdrawal = updatedWithdrawalsResponse.data.withdrawals.find(w => w.id === this.testWithdrawalId);
    
    if (updatedWithdrawal.status !== 'approved') {
      throw new Error('Withdrawal status not updated correctly');
    }
    
    console.log('✅ Withdrawal status update verified');
  }

  async testRealTimeUpdates() {
    console.log('🔄 Testing real-time updates...');
    
    // Test transaction history update
    const transactionHistoryResponse = await axios.get(`${API_URL}/wallets/transactions`, {
      headers: { Authorization: `Bearer ${this.userToken}` }
    });
    
    const transactions = transactionHistoryResponse.data.transactions;
    const withdrawalTransaction = transactions.find(t => t._id === this.testWithdrawalId);
    
    if (!withdrawalTransaction) {
      throw new Error('Withdrawal transaction not found in user history');
    }
    
    console.log('✅ Real-time updates verified');
  }

  async cleanup() {
    console.log('🧹 Cleaning up test data...');
    
    try {
      const User = require('../src/models/userModel').default;
      const InvestmentPackage = require('../src/models/investmentPackageModel').default;
      const Wallet = require('../src/models/walletModel').default;
      const Transaction = require('../src/models/transactionModel').default;
      
      // Clean up test data
      await User.deleteMany({ email: { $in: [testData.testUser.email, testData.testAdmin.email] } });
      await InvestmentPackage.deleteMany({ userId: this.testUserId });
      await Wallet.deleteMany({ userId: this.testUserId });
      await Transaction.deleteMany({ userId: this.testUserId });
      
      console.log('✅ Cleanup completed');
    } catch (error) {
      console.error('⚠️ Cleanup error:', error.message);
    }
    
    await mongoose.disconnect();
  }
}

// Run tests
const testRunner = new WithdrawalSystemTest();
testRunner.runAllTests();
