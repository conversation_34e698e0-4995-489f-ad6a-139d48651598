import React from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  Icon,
  Flex,
} from '@chakra-ui/react';
import { FaExchangeAlt } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import CurrencyConverter from '../components/CurrencyConverter';

const Converter = () => {
  const { t } = useTranslation();
  
  // Colors
  const bgColor = "#0B0E11";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const primaryColor = "#F0B90B";
  
  return (
    <Box bg={bgColor} minH="100vh" py={8}>
      <Container maxW="container.xl">
        <VStack spacing={8} align="stretch">
          <Box>
            <Heading size="lg" color={textColor} mb={2}>
              <Flex align="center">
                <Icon as={FaExchangeAlt} color={primaryColor} mr={3} />
                {t('converter.title', 'Currency Converter')}
              </Flex>
            </Heading>
            <Text color={secondaryTextColor}>
              {t('converter.description', 'Convert between different cryptocurrencies and fiat currencies with real-time exchange rates.')}
            </Text>
          </Box>
          
          <CurrencyConverter 
            initialAmount={1000}
            showChart={true}
            showHistory={true}
          />
        </VStack>
      </Container>
    </Box>
  );
};

export default Converter;
