import React from 'react';
import {
  Box,
  Button,
  Flex,
  HStack,
  Text,
  useColorModeValue
} from '@chakra-ui/react';
import { ChevronLeftIcon, ChevronRightIcon } from '@chakra-ui/icons';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange
}) => {
  const bgColor = useColorModeValue('white', '#1E2329');
  const borderColor = useColorModeValue('gray.200', '#2B3139');
  const textColor = useColorModeValue('gray.800', '#EAECEF');
  const accentColor = '#F0B90B';

  // Generate page numbers to display
  const getPageNumbers = () => {
    const pageNumbers = [];
    const maxPagesToShow = 5;
    
    if (totalPages <= maxPagesToShow) {
      // If total pages is less than max pages to show, display all pages
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // Always include first page
      pageNumbers.push(1);
      
      // Calculate start and end of page range
      let startPage = Math.max(2, currentPage - 1);
      let endPage = Math.min(totalPages - 1, currentPage + 1);
      
      // Adjust if at the beginning
      if (currentPage <= 2) {
        endPage = 4;
      }
      
      // Adjust if at the end
      if (currentPage >= totalPages - 1) {
        startPage = totalPages - 3;
      }
      
      // Add ellipsis after first page if needed
      if (startPage > 2) {
        pageNumbers.push(-1); // -1 represents ellipsis
      }
      
      // Add middle pages
      for (let i = startPage; i <= endPage; i++) {
        pageNumbers.push(i);
      }
      
      // Add ellipsis before last page if needed
      if (endPage < totalPages - 1) {
        pageNumbers.push(-2); // -2 represents ellipsis
      }
      
      // Always include last page
      if (totalPages > 1) {
        pageNumbers.push(totalPages);
      }
    }
    
    return pageNumbers;
  };

  return (
    <Flex justify="center" mt={6}>
      <HStack spacing={1}>
        {/* Previous button */}
        <Button
          size="sm"
          onClick={() => onPageChange(currentPage - 1)}
          isDisabled={currentPage === 1}
          bg={bgColor}
          color={textColor}
          borderColor={borderColor}
          borderWidth="1px"
          _hover={{ bg: useColorModeValue('gray.100', '#2B3139') }}
        >
          <ChevronLeftIcon />
        </Button>
        
        {/* Page numbers */}
        {getPageNumbers().map((page, index) => {
          if (page < 0) {
            // Render ellipsis
            return (
              <Text key={`ellipsis-${index}`} px={2}>
                ...
              </Text>
            );
          }
          
          return (
            <Button
              key={`page-${page}`}
              size="sm"
              onClick={() => onPageChange(page)}
              bg={currentPage === page ? accentColor : bgColor}
              color={currentPage === page ? 'black' : textColor}
              borderColor={borderColor}
              borderWidth="1px"
              _hover={{
                bg: currentPage === page ? accentColor : useColorModeValue('gray.100', '#2B3139')
              }}
            >
              {page}
            </Button>
          );
        })}
        
        {/* Next button */}
        <Button
          size="sm"
          onClick={() => onPageChange(currentPage + 1)}
          isDisabled={currentPage === totalPages || totalPages === 0}
          bg={bgColor}
          color={textColor}
          borderColor={borderColor}
          borderWidth="1px"
          _hover={{ bg: useColorModeValue('gray.100', '#2B3139') }}
        >
          <ChevronRightIcon />
        </Button>
      </HStack>
    </Flex>
  );
};

export default Pagination;
