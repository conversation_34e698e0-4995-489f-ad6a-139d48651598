{"name": "cryptoy<PERSON>hub", "version": "1.0.0", "description": "A Web3 platform for crypto asset management with commission and interest options", "main": "index.js", "scripts": {"start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "start:backend": "cd backend && npm start", "start:frontend": "cd frontend && npm run preview", "dev:backend": "cd backend && npm run dev:docker", "dev:frontend": "cd frontend && npm run dev", "build": "vite build --emptyOutDir", "build:dev": "cd frontend && npm run build:dev", "preview:dev": "cd frontend && npm run preview:dev", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["crypto", "blockchain", "web3", "defi", "yield", "commission"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.9.0", "body-parser": "^2.2.0", "concurrently": "^8.2.2", "cors": "^2.8.5", "cryptoyieldhub": "file:", "express": "^5.1.0", "lightweight-charts": "^5.0.6", "react-tradingview-widget": "^1.3.2", "socket.io": "^4.8.1"}, "directories": {"doc": "docs"}, "repository": {"type": "git", "url": "git+ssh://**************/dewsolt/cryptoyield.git"}, "bugs": {"url": "https://gitlab.com/dewsolt/cryptoyield/issues"}, "homepage": "https://gitlab.com/dewsolt/cryptoyield#readme"}