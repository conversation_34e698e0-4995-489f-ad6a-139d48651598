import { Request, Response, NextFunction } from 'express';
import { logger, logError } from './logger';
import { AppError } from './AppError';

export const catchAsync = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

export const globalErrorHandler = (err: any, req: Request, res: Response, next: NextFunction) => {
  err.statusCode = err.statusCode || 500;
  err.status = err.status || 'error';

  // Log error
  logError(err, {
    path: req.path,
    method: req.method,
    ip: req.ip,
    userId: req.user?._id
  });

  // Operational, trusted error: send message to client
  if (err.isOperational) {
    return res.status(err.statusCode).json({
      status: err.status,
      message: err.message
    });
  }

  // Programming or other unknown error: don't leak error details
  // Send generic message
  return res.status(500).json({
    status: 'error',
    message: process.env.NODE_ENV === 'production'
      ? 'Something went wrong. Please try again later.'
      : err.message
  });
};

// Handle unhandled promise rejections
export const setupUnhandledRejections = () => {
  process.on('unhandledRejection', (err: Error) => {
    logger.error('UNHANDLED REJECTION! 💥 Shutting down...', err);
    logError(err);

    // Give the server time to finish current requests before shutting down
    setTimeout(() => {
      process.exit(1);
    }, 1000);
  });

  process.on('uncaughtException', (err: Error) => {
    logger.error('UNCAUGHT EXCEPTION! 💥 Shutting down...', err);
    logError(err);

    process.exit(1);
  });
};
