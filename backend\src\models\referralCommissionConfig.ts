import mongoose, { Document, Schema } from 'mongoose';

export interface ReferralCommissionConfigDocument extends Document {
  level: number;
  commissionRate: number; // Tỷ lệ hoa hồng (phần trăm)
  minInvestmentAmount: number; // Số tiền đầu tư tối thiểu để nhận hoa hồng
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const referralCommissionConfigSchema = new Schema(
  {
    level: {
      type: Number,
      required: true,
      min: 1,
      unique: true,
    },
    commissionRate: {
      type: Number,
      required: true,
      min: 0,
      max: 100,
      default: 5, // Mặc định 5%
    },
    minInvestmentAmount: {
      type: Number,
      required: true,
      min: 0,
      default: 0,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Đảm bảo rằng level là duy nhất
referralCommissionConfigSchema.index({ level: 1 }, { unique: true });

const ReferralCommissionConfig = mongoose.model<ReferralCommissionConfigDocument>(
  'ReferralCommissionConfig',
  referralCommissionConfigSchema
);

export default ReferralCommissionConfig;
