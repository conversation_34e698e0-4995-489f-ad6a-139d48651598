import { Request, Response, NextFunction, Express } from 'express';
import { AppError } from '../utils/AppError';
import { logger, logError } from '../utils/logger';

/**
 * Global error handler middleware
 */
const globalErrorHandler = (err: Error, req: Request, res: Response, next: NextFunction) => {
  // Log error
  logError(err);

  // Default error
  let statusCode = 500;
  let message = 'Something went wrong';
  let status = 'error';
  let errors: any = {};
  let stack: string | undefined = undefined;

  // If it's our custom AppError
  if (err instanceof AppError) {
    statusCode = err.statusCode;
    message = err.message;
    status = err.status;
  }

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    statusCode = 400;
    message = 'Validation Error';
    status = 'fail';
    
    // Extract validation errors
    const validationError = err as any;
    if (validationError.errors) {
      Object.keys(validationError.errors).forEach(key => {
        errors[key] = validationError.errors[key].message;
      });
    }
  }

  // Mongoose duplicate key error
  if (err.name === 'MongoError' && (err as any).code === 11000) {
    statusCode = 409;
    message = 'Duplicate field value';
    status = 'fail';
    
    // Extract duplicate field
    const duplicateError = err as any;
    if (duplicateError.keyValue) {
      const field = Object.keys(duplicateError.keyValue)[0];
      errors[field] = `${field} already exists`;
    }
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = 'Invalid token';
    status = 'fail';
  }

  if (err.name === 'TokenExpiredError') {
    statusCode = 401;
    message = 'Token expired';
    status = 'fail';
  }

  // Include stack trace in development
  if (process.env.NODE_ENV === 'development') {
    stack = err.stack;
  }

  // Send response
  res.status(statusCode).json({
    status,
    message,
    ...(Object.keys(errors).length > 0 && { errors }),
    ...(stack && { stack })
  });
};

/**
 * Handle 404 errors for undefined routes
 */
const notFoundHandler = (req: Request, res: Response, next: NextFunction) => {
  const err = new AppError(`Cannot find ${req.originalUrl} on this server`, 404);
  next(err);
};

/**
 * Apply error handling middleware to Express app
 * @param app Express application
 */
export const errorHandlerMiddleware = (app: Express): void => {
  // 404 handler for undefined routes
  app.use(notFoundHandler);
  
  // Global error handler
  app.use(globalErrorHandler);
};
