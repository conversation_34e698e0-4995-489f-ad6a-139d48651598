import React, { useState, useEffect } from 'react';
import {
  Box,
  Heading,
  Text,
  VStack,
  HStack,
  Icon,
  Flex,
  Grid,
  GridItem,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Select,
  Button,
  Tooltip,
  Divider,
  Badge,
  useColorModeValue
} from '@chakra-ui/react';
import ChartWrapper from './ChartWrapper';
import { InfoOutlineIcon } from '@chakra-ui/icons';
import {
  FaChartLine,
  FaChartBar,
  FaChartPie,
  FaCalendarAlt,
  FaMoneyBillWave,
  FaPercentage,
  FaHistory,
  FaExchangeAlt,
  FaUserFriends
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';

// Mock data for charts
const dailyReturnsData = [
  { date: '05/01', return: 1.0 },
  { date: '05/02', return: 1.1 },
  { date: '05/03', return: 0.9 },
  { date: '05/04', return: 1.2 },
  { date: '05/05', return: 1.0 },
  { date: '05/06', return: 1.1 },
  { date: '05/07', return: 1.3 },
  { date: '05/08', return: 1.0 },
  { date: '05/09', return: 0.8 },
  { date: '05/10', return: 1.1 },
  { date: '05/11', return: 1.2 },
  { date: '05/12', return: 1.0 },
  { date: '05/13', return: 0.9 },
  { date: '05/14', return: 1.1 }
];

const monthlyReturnsData = [
  { month: 'Jan', return: 31.0 },
  { month: 'Feb', return: 28.0 },
  { month: 'Mar', return: 31.0 },
  { month: 'Apr', return: 30.0 },
  { month: 'May', return: 15.5 }
];

const portfolioDistributionData = [
  { name: 'Active Investments', value: 10000 },
  { name: 'Available Balance', value: 2458.32 },
  { name: 'Pending Deposits', value: 500 },
  { name: 'Earned Commissions', value: 1245.83 }
];

const referralPerformanceData = [
  { month: 'Jan', referrals: 2, earnings: 150 },
  { month: 'Feb', referrals: 3, earnings: 225 },
  { month: 'Mar', referrals: 1, earnings: 75 },
  { month: 'Apr', referrals: 4, earnings: 300 },
  { month: 'May', referrals: 2, earnings: 150 }
];

interface AnalyticsDashboardProps {
  investmentAmount?: number;
  commissionEarnings?: number;
  referralCount?: number;
}

const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({
  investmentAmount = 10000,
  commissionEarnings = 1245.83,
  referralCount = 12
}) => {
  const { t } = useTranslation();
  const [timeRange, setTimeRange] = useState('week');
  const [chartData, setChartData] = useState(dailyReturnsData.slice(-7));

  // Colors
  const bgColor = "#1E2329";
  const cardBgColor = "#0B0E11";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const primaryColor = "#F0B90B";

  // Colors for pie chart
  const COLORS = ['#F0B90B', '#0ECB81', '#F6465D', '#3375BB'];

  // Calculate ROI
  const calculateROI = () => {
    const dailyRate = 0.01; // 1%

    const dailyReturn = investmentAmount * dailyRate;
    const weeklyReturn = dailyReturn * 7;
    const monthlyReturn = dailyReturn * 30;
    const yearlyReturn = dailyReturn * 365;

    const dailyROI = dailyRate * 100;
    const weeklyROI = dailyROI * 7;
    const monthlyROI = dailyROI * 30;
    const yearlyROI = dailyROI * 365;

    return {
      daily: { amount: dailyReturn, percentage: dailyROI },
      weekly: { amount: weeklyReturn, percentage: weeklyROI },
      monthly: { amount: monthlyReturn, percentage: monthlyROI },
      yearly: { amount: yearlyReturn, percentage: yearlyROI }
    };
  };

  const roi = calculateROI();

  // Update chart data based on time range
  useEffect(() => {
    switch (timeRange) {
      case 'week':
        setChartData(dailyReturnsData.slice(-7));
        break;
      case 'month':
        setChartData(dailyReturnsData);
        break;
      case 'year':
        setChartData(monthlyReturnsData);
        break;
      default:
        setChartData(dailyReturnsData.slice(-7));
    }
  }, [timeRange]);

  return (
    <Box bg={bgColor} borderRadius="md" borderWidth="1px" borderColor={borderColor} p={6}>
      <Flex align="center" mb={6}>
        <Icon as={FaChartLine} color={primaryColor} boxSize={5} mr={2} />
        <Heading size="md" color={textColor}>{t('analytics.title', 'Investment Analytics')}</Heading>
      </Flex>

      <Grid templateColumns={{ base: "1fr", lg: "2fr 1fr" }} gap={6}>
        {/* Left Column - Charts */}
        <GridItem>
          <VStack spacing={6} align="stretch">
            {/* Returns Chart */}
            <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
              <Flex justify="space-between" align="center" mb={4}>
                <Heading size="sm" color={textColor}>{t('analytics.returnsChart.title', 'Investment Returns')}</Heading>
                <HStack>
                  <Select
                    size="sm"
                    value={timeRange}
                    onChange={(e) => setTimeRange(e.target.value)}
                    bg={cardBgColor}
                    borderColor={borderColor}
                    color={textColor}
                    _hover={{ borderColor: primaryColor }}
                    _focus={{ borderColor: primaryColor, boxShadow: "none" }}
                    w="120px"
                  >
                    <option value="week">{t('analytics.timeRange.week', 'Week')}</option>
                    <option value="month">{t('analytics.timeRange.month', 'Month')}</option>
                    <option value="year">{t('analytics.timeRange.year', 'Year')}</option>
                  </Select>
                </HStack>
              </Flex>

              <Box h="300px">
                {/* Wrap chart with error boundary */}
                <ChartWrapper height="300px">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={chartData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke={borderColor} />
                      <XAxis
                        dataKey={timeRange === 'year' ? 'month' : 'date'}
                        stroke={secondaryTextColor}
                      />
                      <YAxis stroke={secondaryTextColor} />
                      <RechartsTooltip
                        contentStyle={{
                          backgroundColor: cardBgColor,
                          borderColor: borderColor,
                          color: textColor
                        }}
                      />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="return"
                        name={t('analytics.returnsChart.dailyReturn', 'Daily Return (%)')}
                        stroke={primaryColor}
                        activeDot={{ r: 8 }}
                        strokeWidth={2}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </ChartWrapper>
              </Box>
            </Box>

            {/* Portfolio Distribution */}
            <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
              <Heading size="sm" color={textColor} mb={4}>{t('analytics.portfolioDistribution.title', 'Portfolio Distribution')}</Heading>

              <Grid templateColumns={{ base: "1fr", md: "1fr 1fr" }} gap={4}>
                <Box h="250px">
                  <ChartWrapper height="250px">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={portfolioDistributionData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        >
                          {portfolioDistributionData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <RechartsTooltip
                          contentStyle={{
                            backgroundColor: cardBgColor,
                            borderColor: borderColor,
                            color: textColor
                          }}
                        />
                      </PieChart>
                    </ResponsiveContainer>
                  </ChartWrapper>
                </Box>

                <Box>
                  <VStack spacing={4} align="stretch">
                    {portfolioDistributionData.map((item, index) => (
                      <HStack key={index} justify="space-between">
                        <HStack>
                          <Box w="12px" h="12px" borderRadius="sm" bg={COLORS[index % COLORS.length]} />
                          <Text color={textColor} fontSize="sm">{t(`analytics.portfolioDistribution.${item.name.toLowerCase().replace(/\s+/g, '')}`, item.name)}</Text>
                        </HStack>
                        <Text color={textColor} fontWeight="bold">${item.value.toLocaleString()}</Text>
                      </HStack>
                    ))}
                  </VStack>
                </Box>
              </Grid>
            </Box>

            {/* Referral Performance */}
            <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
              <Heading size="sm" color={textColor} mb={4}>{t('analytics.referralPerformance.title', 'Referral Performance')}</Heading>

              <Box h="250px">
                <ChartWrapper height="250px">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={referralPerformanceData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke={borderColor} />
                      <XAxis dataKey="month" stroke={secondaryTextColor} />
                      <YAxis yAxisId="left" stroke={secondaryTextColor} />
                      <YAxis yAxisId="right" orientation="right" stroke={secondaryTextColor} />
                      <RechartsTooltip
                        contentStyle={{
                          backgroundColor: cardBgColor,
                          borderColor: borderColor,
                          color: textColor
                        }}
                      />
                      <Legend />
                      <Bar
                        yAxisId="left"
                        dataKey="referrals"
                        name={t('analytics.referralPerformance.referrals', 'Referrals')}
                        fill="#3375BB"
                      />
                      <Bar
                        yAxisId="right"
                        dataKey="earnings"
                        name={t('analytics.referralPerformance.earnings', 'Earnings ($)')}
                        fill="#F0B90B"
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartWrapper>
              </Box>
            </Box>
          </VStack>
        </GridItem>

        {/* Right Column - Stats */}
        <GridItem>
          <VStack spacing={6} align="stretch">
            {/* ROI Calculator */}
            <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
              <Flex align="center" mb={4}>
                <Icon as={FaPercentage} color={primaryColor} mr={2} />
                <Heading size="sm" color={textColor}>{t('analytics.roiCalculator.title', 'ROI Calculator')}</Heading>
                <Tooltip
                  label={t('analytics.roiCalculator.tooltip', 'Return on Investment based on 1% daily returns')}
                  placement="top"
                >
                  <InfoOutlineIcon ml={2} color={secondaryTextColor} />
                </Tooltip>
              </Flex>

              <VStack spacing={4} align="stretch">
                <HStack justify="space-between" p={3} bg={bgColor} borderRadius="md">
                  <VStack align="start" spacing={0}>
                    <Text color={secondaryTextColor} fontSize="sm">{t('analytics.roiCalculator.investmentAmount', 'Investment Amount')}</Text>
                    <Text color={textColor} fontWeight="bold">${investmentAmount.toLocaleString()}</Text>
                  </VStack>
                  <Icon as={FaMoneyBillWave} color={primaryColor} boxSize={5} />
                </HStack>

                <Divider borderColor={borderColor} />

                <Box>
                  <Text color={secondaryTextColor} fontSize="sm" mb={2}>{t('analytics.roiCalculator.projectedReturns', 'Projected Returns')}</Text>

                  <Grid templateColumns="repeat(2, 1fr)" gap={3}>
                    <GridItem bg={bgColor} p={3} borderRadius="md">
                      <Text color={secondaryTextColor} fontSize="xs">{t('analytics.roiCalculator.daily', 'Daily')}</Text>
                      <Text color={textColor} fontWeight="bold">${roi.daily.amount.toFixed(2)}</Text>
                      <Badge colorScheme="green" variant="subtle" mt={1}>
                        {roi.daily.percentage.toFixed(1)}%
                      </Badge>
                    </GridItem>

                    <GridItem bg={bgColor} p={3} borderRadius="md">
                      <Text color={secondaryTextColor} fontSize="xs">{t('analytics.roiCalculator.weekly', 'Weekly')}</Text>
                      <Text color={textColor} fontWeight="bold">${roi.weekly.amount.toFixed(2)}</Text>
                      <Badge colorScheme="green" variant="subtle" mt={1}>
                        {roi.weekly.percentage.toFixed(1)}%
                      </Badge>
                    </GridItem>

                    <GridItem bg={bgColor} p={3} borderRadius="md">
                      <Text color={secondaryTextColor} fontSize="xs">{t('analytics.roiCalculator.monthly', 'Monthly')}</Text>
                      <Text color={textColor} fontWeight="bold">${roi.monthly.amount.toFixed(2)}</Text>
                      <Badge colorScheme="green" variant="subtle" mt={1}>
                        {roi.monthly.percentage.toFixed(1)}%
                      </Badge>
                    </GridItem>

                    <GridItem bg={bgColor} p={3} borderRadius="md">
                      <Text color={secondaryTextColor} fontSize="xs">{t('analytics.roiCalculator.yearly', 'Yearly')}</Text>
                      <Text color={textColor} fontWeight="bold">${roi.yearly.amount.toFixed(2)}</Text>
                      <Badge colorScheme="green" variant="subtle" mt={1}>
                        {roi.yearly.percentage.toFixed(1)}%
                      </Badge>
                    </GridItem>
                  </Grid>
                </Box>

                <Button colorScheme="yellow" size="sm" w="full">
                  {t('analytics.roiCalculator.investNow', 'Invest Now')}
                </Button>
              </VStack>
            </Box>

            {/* Referral Stats */}
            <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
              <Flex align="center" mb={4}>
                <Icon as={FaUserFriends} color={primaryColor} mr={2} />
                <Heading size="sm" color={textColor}>{t('analytics.referralStats.title', 'Referral Statistics')}</Heading>
              </Flex>

              <VStack spacing={4} align="stretch">
                <Stat>
                  <StatLabel color={secondaryTextColor}>{t('analytics.referralStats.totalReferrals', 'Total Referrals')}</StatLabel>
                  <StatNumber color={textColor}>{referralCount}</StatNumber>
                  <StatHelpText color="green.400">
                    <StatArrow type="increase" />
                    {t('analytics.referralStats.increase', '25% increase from last month')}
                  </StatHelpText>
                </Stat>

                <Stat>
                  <StatLabel color={secondaryTextColor}>{t('analytics.referralStats.totalEarnings', 'Total Earnings')}</StatLabel>
                  <StatNumber color={textColor}>${commissionEarnings.toFixed(2)}</StatNumber>
                  <StatHelpText color="green.400">
                    <StatArrow type="increase" />
                    {t('analytics.referralStats.newEarnings', '$150.00 new earnings this month')}
                  </StatHelpText>
                </Stat>

                <Divider borderColor={borderColor} />

                <HStack justify="space-between">
                  <Text color={secondaryTextColor}>{t('analytics.referralStats.commissionRate', 'Commission Rate')}</Text>
                  <Badge colorScheme="yellow" variant="solid" px={2} py={1}>
                    3%
                  </Badge>
                </HStack>

                <Button colorScheme="yellow" variant="outline" size="sm" w="full" leftIcon={<FaUserFriends />}>
                  {t('analytics.referralStats.inviteFriends', 'Invite Friends')}
                </Button>
              </VStack>
            </Box>

            {/* Transaction History Summary */}
            <Box bg={cardBgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
              <Flex align="center" mb={4}>
                <Icon as={FaHistory} color={primaryColor} mr={2} />
                <Heading size="sm" color={textColor}>{t('analytics.transactionSummary.title', 'Transaction Summary')}</Heading>
              </Flex>

              <VStack spacing={3} align="stretch">
                <HStack justify="space-between">
                  <HStack>
                    <Icon as={FaExchangeAlt} color="green.400" />
                    <Text color={textColor}>{t('analytics.transactionSummary.deposits', 'Deposits')}</Text>
                  </HStack>
                  <Text color={textColor} fontWeight="bold">$12,500.00</Text>
                </HStack>

                <HStack justify="space-between">
                  <HStack>
                    <Icon as={FaExchangeAlt} color="red.400" transform="rotate(180deg)" />
                    <Text color={textColor}>{t('analytics.transactionSummary.withdrawals', 'Withdrawals')}</Text>
                  </HStack>
                  <Text color={textColor} fontWeight="bold">$1,250.00</Text>
                </HStack>

                <HStack justify="space-between">
                  <HStack>
                    <Icon as={FaChartLine} color="blue.400" />
                    <Text color={textColor}>{t('analytics.transactionSummary.earnings', 'Earnings')}</Text>
                  </HStack>
                  <Text color={textColor} fontWeight="bold">$2,458.32</Text>
                </HStack>

                <Divider borderColor={borderColor} />

                <HStack justify="space-between">
                  <Text color={textColor} fontWeight="bold">{t('analytics.transactionSummary.netBalance', 'Net Balance')}</Text>
                  <Text color="green.400" fontWeight="bold">$13,708.32</Text>
                </HStack>

                <Button size="sm" variant="ghost" colorScheme="yellow" rightIcon={<FaHistory />}>
                  {t('analytics.transactionSummary.viewAll', 'View All Transactions')}
                </Button>
              </VStack>
            </Box>
          </VStack>
        </GridItem>
      </Grid>
    </Box>
  );
};

export default AnalyticsDashboard;
