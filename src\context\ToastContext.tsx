import React, { createContext, useContext, useState, ReactNode } from 'react';

// Toast types
export type ToastType = 'info' | 'success' | 'warning' | 'error';

// Toast interface
export interface Toast {
  id: number;
  message: string;
  type: ToastType;
  duration: number;
}

// Toast context interface
interface ToastContextType {
  toasts: Toast[];
  addToast: (message: string, type?: ToastType, duration?: number) => void;
  removeToast: (id: number) => void;
}

// Create context with default values
const ToastContext = createContext<ToastContextType>({
  toasts: [],
  addToast: () => {},
  removeToast: () => {}
});

// Custom hook to use toast context
export const useToast = () => useContext(ToastContext);

// Props for ToastProvider
interface ToastProviderProps {
  children: ReactNode;
}

// Toast provider component
export const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
  const [toasts, setToasts] = useState<Toast[]>([]);
  
  // Add a new toast
  const addToast = (message: string, type: ToastType = 'info', duration: number = 3000) => {
    const id = Date.now();
    const newToast: Toast = { id, message, type, duration };
    
    setToasts(prevToasts => [...prevToasts, newToast]);
    
    // Auto remove toast after duration
    setTimeout(() => {
      removeToast(id);
    }, duration);
  };
  
  // Remove a toast by id
  const removeToast = (id: number) => {
    setToasts(prevToasts => prevToasts.filter(toast => toast.id !== id));
  };
  
  return (
    <ToastContext.Provider value={{ toasts, addToast, removeToast }}>
      {children}
    </ToastContext.Provider>
  );
};
