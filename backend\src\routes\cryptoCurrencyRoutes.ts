import express from 'express';
import { cryptoCurrencyController } from '../controllers/cryptoCurrencyController';
import { protect, admin } from '../middleware/authMiddleware';

const router = express.Router();

// Routes công khai
router.get('/', cryptoCurrencyController.getAllCurrencies);
router.get('/:symbol', cryptoCurrencyController.getCurrencyBySymbol);
router.get('/:symbol/networks', cryptoCurrencyController.getNetworksByCurrency);
router.get('/:symbol/networks/:networkId', cryptoCurrencyController.getNetworkById);

// Routes yêu cầu đăng nhập
router.get(
  '/:symbol/networks/:networkId/next-address',
  protect,
  cryptoCurrencyController.getNextAddress
);

// Routes yêu cầu quyền admin
router.post(
  '/:symbol/networks/:networkId/addresses',
  protect,
  admin,
  cryptoCurrencyController.addAddress
);
router.put(
  '/:symbol/status',
  protect,
  admin,
  cryptoCurrencyController.updateCurrencyStatus
);
router.put(
  '/:symbol/networks/:networkId/status',
  protect,
  admin,
  cryptoCurrencyController.updateNetworkStatus
);

export default router;
