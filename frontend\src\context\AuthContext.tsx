import { createContext, useState, useEffect, ReactNode, useCallback, useMemo } from 'react';
import axios from 'axios';
import { socketService } from '../services/socket-service';

interface User {
  _id: string;
  email: string;
  firstName: string;
  lastName: string;
  walletAddress?: string;
  phoneNumber?: string;
  country?: string;
  city?: string;
  kycVerified: boolean;
  twoFactorEnabled: boolean;
  token: string;
  referralCode?: string;
  referralCount?: number;
  referralEarnings?: number;
  marketingConsent?: boolean;
  isAdmin?: boolean;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  adminLogin: (email: string, password: string) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => void;
  updateProfile: (userData: Partial<User>) => Promise<void>;
  checkAdminStatus: () => Promise<boolean>;
}

interface AuthProviderProps {
  children: ReactNode;
}

interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  username?: string;
  birthDate?: string;
  phoneNumber?: string;
  country?: string;
  city?: string;
  referralCode?: string;
  marketingConsent?: boolean;
}

// Create context
export const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: false,
  error: null,
  login: async () => {},
  adminLogin: async () => {},
  register: async () => {},
  logout: () => {},
  updateProfile: async () => {},
  checkAdminStatus: async () => false,
});

// API base URL - Use Vite's environment variables or default value
const API_URL = import.meta.env.VITE_API_URL || 'https://api.shpnfinance.com/api';

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize auth state from localStorage and set up cookie-based auth
  useEffect(() => {
    // Set axios to include credentials in all requests
    axios.defaults.withCredentials = true;
    console.log('Axios configured to include credentials in all requests');

    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        const userData = JSON.parse(storedUser);

        // Check if we're on the login page and prevent auto-login
        const isLoginPage = window.location.pathname === '/login' ||
                           window.location.pathname === '/admin/login';

        if (isLoginPage) {
          console.log('On login page, not auto-loading user from localStorage');
          // Clear localStorage to prevent auto-login on refresh
          localStorage.removeItem('user');
        } else {
          setUser(userData);
          console.log('User loaded from localStorage');
        }
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
        localStorage.removeItem('user');
      }
    }

    setLoading(false);
  }, []);

  // Set up axios interceptor for cookie-based authentication
  useEffect(() => {
    const interceptor = axios.interceptors.request.use(
      (config) => {
        // Ensure withCredentials is set for all requests
        config.withCredentials = true;

        // No need to set Authorization header with cookie-based auth
        // The cookie will be sent automatically

        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    return () => {
      axios.interceptors.request.eject(interceptor);
    };
  }, []);

  // Login user - optimized with useCallback
  const login = useCallback(async (email: string, password: string) => {
    try {
      setLoading(true);
      setError(null);

      // Clear any existing user data to prevent auto-login issues
      localStorage.removeItem('user');
      localStorage.removeItem('adminToken');

      // Call the real API
      const response = await axios.post(`${API_URL}/users/login`, {
        email,
        password,
      }, {
        withCredentials: true // Important for cookies
      });

      const userData = response.data.data || response.data;

      // User data from API response
      const userDataWithToken = {
        ...userData
      };

      setUser(userDataWithToken);

      // Store user data in localStorage
      localStorage.setItem('user', JSON.stringify(userDataWithToken));

      console.log('User logged in successfully with cookie authentication');

      // Set axios to include credentials in all requests
      axios.defaults.withCredentials = true;

      // Connect to WebSocket
      try {
        socketService.connect().catch(error => {
          console.error('Failed to establish WebSocket connection:', error);
        });
        console.log('WebSocket connection established after login');
      } catch (wsError) {
        console.error('Failed to establish WebSocket connection:', wsError);
        // Continue anyway, WebSocket is not critical for basic functionality
      }
    } catch (err: any) {
      console.error("Login error:", err);

      // Handle API error properly
      const errorMessage = err.response?.data?.message || err.message || 'Email hoặc mật khẩu không chính xác';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [API_URL]);

  // Admin Login - optimized with useCallback
  const adminLogin = useCallback(async (email: string, password: string) => {
    try {
      setLoading(true);
      setError(null);

      // Clear any existing user data to prevent auto-login issues
      localStorage.removeItem('user');
      localStorage.removeItem('adminToken');
      localStorage.removeItem('adminUser');

      // Call the admin login API
      try {
        console.log('Attempting admin login...');

        // Ensure axios is configured to include credentials
        axios.defaults.withCredentials = true;

        const response = await axios.post(`${API_URL}/admin/login`, {
          email,
          password,
        }, {
          withCredentials: true // Important for cookies
        });

        const userData = response.data.data || response.data;

        // Ensure isAdmin flag is set
        const adminUserData = {
          ...userData,
          isAdmin: true
        };

        // Connect to WebSocket and subscribe to admin updates
        try {
          socketService.connect().then(() => {
            console.log('WebSocket connection established after admin login');

            // Subscribe to admin updates
            setTimeout(() => {
              socketService.send('subscribe_admin_updates', {});
              console.log('Subscribed to admin updates via WebSocket');
            }, 1000); // Small delay to ensure connection is established
          }).catch(error => {
            console.error('Failed to establish WebSocket connection for admin:', error);
          });
        } catch (wsError) {
          console.error('Failed to establish WebSocket connection for admin:', wsError);
          // Continue anyway, WebSocket is not critical for basic functionality
        }

        console.log('Admin login successful:', adminUserData.email);

        // Update user state
        setUser(adminUserData);

        // Store admin user data in localStorage
        localStorage.setItem('user', JSON.stringify(adminUserData));
        localStorage.setItem('adminUser', JSON.stringify(adminUserData));
        localStorage.setItem('adminToken', 'true');

        // Verify admin status immediately after login to ensure server recognizes the cookie
        try {
          console.log('Verifying admin status immediately after login...');

          // Add a small delay to ensure cookies are properly set
          await new Promise(resolve => setTimeout(resolve, 100));

          const verifyResponse = await axios.get(`${API_URL}/admin/check`, {
            withCredentials: true,
            headers: {
              'Cache-Control': 'no-cache',
              'X-Admin-Verification': 'post-login'
            }
          });

          console.log('Admin verification after login:', verifyResponse.status, verifyResponse.data);

          // Check if the adminToken cookie is set
          const hasAdminCookie = document.cookie.includes('adminToken=true');
          console.log('Admin cookie present:', hasAdminCookie);

          if (!hasAdminCookie) {
            console.warn('Admin cookie not found after login, manually setting it');
            document.cookie = `adminToken=true; path=/; max-age=${30 * 24 * 60 * 60}`;
          }

          // Connect to WebSocket
          try {
            socketService.connect().catch(error => {
              console.error('Failed to establish WebSocket connection:', error);
            });
            console.log('WebSocket connection established after admin login');
          } catch (wsError) {
            console.error('Failed to establish WebSocket connection:', wsError);
            // Continue anyway, WebSocket is not critical for basic functionality
          }
        } catch (verifyError: any) {
          console.warn('Admin verification after login failed:', verifyError.response?.status, verifyError.response?.data);
          // Continue anyway since we just logged in successfully
        }
      } catch (apiError: any) {
        console.error("API admin login error:", apiError.response?.status, apiError.response?.data);

        // Handle API error properly
        const errorMessage = apiError.response?.data?.message || 'Tài khoản hoặc mật khẩu không chính xác';
        setError(errorMessage);
        throw new Error(errorMessage);
      }
    } catch (err: any) {
      setError(err.response?.data?.message || err.message || 'Tài khoản hoặc mật khẩu không chính xác');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [API_URL]);

  // Register user - optimized with useCallback
  const register = useCallback(async (userData: RegisterData) => {
    try {
      setLoading(true);
      setError(null);

      // Call the real API
      const response = await axios.post(`${API_URL}/users/register`, userData, {
        withCredentials: true // Important for cookies
      });

      const newUser = response.data.data || response.data;

      // User data from API response
      const newUserWithToken = {
        ...newUser
      };

      setUser(newUserWithToken);

      // Store user data in localStorage
      localStorage.setItem('user', JSON.stringify(newUserWithToken));

      console.log('User registered successfully with cookie authentication');

      // Set axios to include credentials in all requests
      axios.defaults.withCredentials = true;
    } catch (err: any) {
      console.error("Registration error:", err);

      // Extract detailed error message from backend response
      const errorResponse = err.response?.data;
      let errorMessage = 'Kayıt işlemi başarısız oldu';

      if (errorResponse) {
        if (errorResponse.message) {
          errorMessage = errorResponse.message;
        }

        // If there are field-specific validation errors
        if (errorResponse.errors && typeof errorResponse.errors === 'object') {
          const fieldErrors = Object.values(errorResponse.errors).filter(Boolean);
          if (fieldErrors.length > 0) {
            errorMessage = fieldErrors.join('. ');
          }
        }
      }

      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [API_URL]);

  // Logout user - optimized with useCallback
  const logout = useCallback(async () => {
    try {
      // Call the logout API endpoint to clear cookies
      await axios.post(`${API_URL}/users/logout`, {}, {
        withCredentials: true // Important for cookies
      });
      console.log('Logout API called successfully');
    } catch (error) {
      console.error('Error during logout API call:', error);
    } finally {
      // Clear local state regardless of API success
      setUser(null);
      localStorage.removeItem('user');
      localStorage.removeItem('token');
      localStorage.removeItem('adminUser');
      localStorage.removeItem('adminToken');
      localStorage.removeItem('admin_welcome_shown'); // Remove welcome toast flag

      // Remove Authorization header from axios defaults
      delete axios.defaults.headers.common['Authorization'];

      // Close WebSocket connection
      try {
        socketService.disconnect();
        console.log('WebSocket connection closed after logout');
      } catch (wsError) {
        console.error('Error closing WebSocket connection:', wsError);
      }

      console.log('User logged out, token removed from localStorage and axios headers');
    }
  }, [API_URL]);

  // Update user profile - optimized with useCallback
  const updateProfile = useCallback(async (userData: Partial<User>) => {
    try {
      setLoading(true);
      setError(null);

      // Call the real API
      const response = await axios.put(`${API_URL}/users/profile`, userData, {
        withCredentials: true // Important for cookies
      });

      const updatedUser = response.data.data || response.data;

      // User data from API response
      const updatedUserWithToken = {
        ...updatedUser
      };

      setUser(updatedUserWithToken);
      localStorage.setItem('user', JSON.stringify(updatedUserWithToken));
    } catch (err: any) {
      console.error("Profile update error:", err);
      setError(err.response?.data?.message || 'Profile update failed');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [API_URL, user?.token]);

  // Check admin status - optimized with useCallback
  const checkAdminStatus = useCallback(async (forceCheck = false): Promise<boolean> => {
    try {
      console.log('Checking admin status...');

      // If no user is logged in, return false
      if (!user) {
        console.log('No user logged in, cannot be admin');
        return false;
      }

      // Check for admin cookie first (most reliable indicator)
      const hasAdminCookie = document.cookie.includes('adminToken=true');
      if (hasAdminCookie && !forceCheck) {
        console.log('Admin cookie found, user is admin');

        // Update the user object with admin status if needed
        if (!user.isAdmin) {
          const updatedUser = { ...user, isAdmin: true };
          setUser(updatedUser);
          localStorage.setItem('user', JSON.stringify(updatedUser));
          localStorage.setItem('adminToken', 'true');
        }

        return true;
      }

      // If we already know the user is an admin from local storage and not forcing a check, return true
      if (user.isAdmin && !forceCheck) {
        console.log('User is already marked as admin in context');
        return true;
      }

      // Check if admin token exists in localStorage
      const adminToken = localStorage.getItem('adminToken');
      if (adminToken === 'true' && !forceCheck) {
        console.log('Admin token found in localStorage');
        // Update the user object with admin status
        const updatedUser = { ...user, isAdmin: true };
        setUser(updatedUser);
        localStorage.setItem('user', JSON.stringify(updatedUser));
        return true;
      }

      // Otherwise, verify with the server
      try {
        console.log('Verifying admin status with server...');

        // Ensure axios is configured to include credentials
        axios.defaults.withCredentials = true;

        const response = await axios.get(`${API_URL}/admin/check`, {
          withCredentials: true,
          headers: {
            'Cache-Control': 'no-cache',
            'X-Admin-Check': 'true', // Add custom header for debugging
            'X-Request-Time': new Date().toISOString() // Add timestamp to prevent caching
          }
        });

        console.log('Admin check response:', response.status, response.data);

        // If we get a successful response, the user is an admin
        if (response.status === 200) {
          console.log('Server confirmed user is admin');

          // Update the user object with admin status
          const updatedUser = { ...user, isAdmin: true };
          setUser(updatedUser);
          localStorage.setItem('user', JSON.stringify(updatedUser));
          localStorage.setItem('adminToken', 'true');

          // Check if the adminToken cookie is set
          if (!hasAdminCookie) {
            console.warn('Admin cookie not found after verification, manually setting it');
            document.cookie = `adminToken=true; path=/; max-age=${30 * 24 * 60 * 60}`;
          }

          return true;
        }

        console.log('Server did not confirm admin status');
        return false;
      } catch (error: any) {
        console.error("Admin check failed:", error.response?.status, error.response?.data);

        // If the error is 401 or 403, the user is definitely not an admin
        if (error.response?.status === 401 || error.response?.status === 403) {
          console.log('Server explicitly denied admin access');
          // Update user to explicitly mark as non-admin
          const updatedUser = { ...user, isAdmin: false };
          setUser(updatedUser);
          localStorage.setItem('user', JSON.stringify(updatedUser));
          // Remove any admin tokens
          localStorage.removeItem('adminToken');
          // Clear admin cookie if it exists
          if (hasAdminCookie) {
            document.cookie = 'adminToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
          }
        }

        return false;
      }
    } catch (error) {
      console.error("Admin status check error:", error);
      return false;
    }
  }, [user, API_URL]);

  // Optimize the context value with useMemo
  const contextValue = useMemo(() => ({
    user,
    loading,
    error,
    login,
    adminLogin,
    register,
    logout,
    updateProfile,
    checkAdminStatus,
  }), [user, loading, error, login, adminLogin, register, logout, updateProfile, checkAdminStatus]);

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
