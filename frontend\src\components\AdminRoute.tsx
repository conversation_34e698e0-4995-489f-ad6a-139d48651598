import { ReactNode, useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import useAuth from '../hooks/useAuth';
import useAdminAuth from '../hooks/useAdminAuth';
import { Spinner, Center, Text, VStack, useToast } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import axios from 'axios';

interface AdminRouteProps {
  children: ReactNode;
  redirectPath?: string;
  showFeedback?: boolean;
}

const AdminRoute = ({
  children,
  redirectPath = '/admin/login',
  showFeedback = true
}: AdminRouteProps) => {
  const { user, loading: authLoading } = useAuth();
  const { isAdmin, isVerifying, verifyAdminStatus } = useAdminAuth();
  const location = useLocation();
  const { t } = useTranslation();
  const toast = useToast();
  const [isVerified, setIsVerified] = useState(false);

  // Force admin check when component mounts or when URL changes
  useEffect(() => {
    const checkAdminStatus = async () => {
      try {
        console.log('AdminRoute: Performing explicit admin check for path:', location.pathname);

        // Ensure axios is configured to include credentials
        axios.defaults.withCredentials = true;

        // Verify admin status
        const isAdminUser = await verifyAdminStatus();
        setIsVerified(true);

        if (!isAdminUser) {
          console.warn('AdminRoute: Admin check failed despite user being logged in');
          if (showFeedback) {
            toast({
              title: t('admin.verificationFailed', 'Admin Verification Failed'),
              description: t('admin.pleaseLoginAgain', 'Please login again as admin'),
              status: 'error',
              duration: 5000,
              isClosable: true,
              id: 'admin-verification-failed-toast' // Add an ID to prevent duplicate toasts
            });
          }
        } else {
          console.log('AdminRoute: Admin status confirmed for user:', user?.email);
          // We don't show welcome toast here anymore to prevent duplicate toasts
          // The welcome toast is now handled in AdminLayout.tsx
        }
      } catch (error) {
        console.error('AdminRoute: Error checking admin status:', error);
        setIsVerified(true); // Set to true even on error to avoid infinite loading
      }
    };

    // Only check if we have a user and we're not already verifying
    if (user && !isVerifying && !isVerified) {
      checkAdminStatus();
    }
  }, [user, verifyAdminStatus, toast, t, isVerifying, location.pathname, isVerified, showFeedback]);

  // Show loading spinner while verifying or during auth loading
  if (isVerifying || authLoading || (user && !isVerified)) {
    return (
      <Center h="100vh" bg="#0B0E11">
        <VStack spacing={4}>
          <Spinner size="xl" color="#F0B90B" thickness="4px" />
          <Text color="white">{t('common.verifyingAdminStatus', 'Verifying admin status...')}</Text>
        </VStack>
      </Center>
    );
  }

  // Redirect to login if not logged in
  if (!user) {
    console.log('No user found, redirecting to admin login');
    // Save the current location for redirect after login
    return <Navigate to={redirectPath} state={{ from: location }} replace />;
  }

  // Redirect to unauthorized page if not an admin
  if (!isAdmin) {
    console.log('User is not an admin, redirecting to unauthorized page');
    if (showFeedback) {
      toast({
        title: t('admin.accessDenied', 'Access Denied'),
        description: t('admin.notAuthorized', 'You are not authorized to access admin area'),
        status: 'error',
        duration: 5000,
        isClosable: true,
        id: 'admin-access-denied-toast' // Add an ID to prevent duplicate toasts
      });
    }
    return <Navigate to="/unauthorized" state={{ from: location }} replace />;
  }

  // If they are an admin, render the children
  console.log('User is admin, rendering admin content for path:', location.pathname);
  return <>{children}</>;
};

export default AdminRoute;
