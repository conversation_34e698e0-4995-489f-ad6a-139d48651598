import React, { useEffect } from 'react';
import { Routes, Route, Navigate, useLocation } from 'react-router-dom';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorBoundary from '../components/ErrorBoundary';
import useAuth from '../hooks/useAuth';
import AdminLayout from '../layouts/AdminLayout';
import { withSuspense } from './LazyRoutes';
import AuthRedirectRoute from '../components/AuthRedirectRoute';

// Import all lazy-loaded components
import {
  LazyHome,
  LazyLogin,
  LazyRegister,
  LazyProfile,
  LazyFAQ,
  LazyAbout,
  LazyContact,
  LazyNotFound,
  LazyAdminDashboard,
  LazyAdminUsers,
  LazyAdminUserDetail,
  LazyAdminTransactions,
  LazyAdminSettings,
  LazySystemManagement,
  LazyTransactionDetail
} from './LazyRoutes';

// Route types for better organization
enum RouteType {
  PUBLIC = 'public',
  PROTECTED = 'protected',
  ADMIN = 'admin'
}

// Route definition interface
interface RouteDefinition {
  path: string;
  component: React.ComponentType<any>;
  type: RouteType;
  title: string;
  description?: string;
  exact?: boolean;
  children?: RouteDefinition[];
}

// Apply Suspense to all components
const Home = withSuspense(LazyHome);
const Login = withSuspense(LazyLogin);
const Register = withSuspense(LazyRegister);
const Profile = withSuspense(LazyProfile);
const About = withSuspense(LazyAbout);
const Contact = withSuspense(LazyContact);
const FAQ = withSuspense(LazyFAQ);
const NotFoundPage = withSuspense(LazyNotFound);

// Admin components with Suspense
const AdminDashboard = withSuspense(LazyAdminDashboard);
const AdminUsers = withSuspense(LazyAdminUsers);
const AdminTransactions = withSuspense(LazyAdminTransactions);
const AdminSettings = withSuspense(LazyAdminSettings);
const SystemManagement = withSuspense(LazySystemManagement);
const TransactionDetail = withSuspense(LazyTransactionDetail);

// Route definitions
const routes: RouteDefinition[] = [
  // Public routes
  {
    path: '/',
    component: Home,
    type: RouteType.PUBLIC,
    title: 'Home | Shipping Finance',
    description: 'Welcome to Shipping Finance - Your trusted crypto investment platform'
  },
  {
    path: '/login',
    component: Login,
    type: RouteType.PUBLIC,
    title: 'Login | Shipping Finance',
    description: 'Login to your Shipping Finance account'
  },
  {
    path: '/register',
    component: Register,
    type: RouteType.PUBLIC,
    title: 'Register | Shipping Finance',
    description: 'Create a new Shipping Finance account'
  },
  {
    path: '/about',
    component: About,
    type: RouteType.PUBLIC,
    title: 'About Us | Shipping Finance',
    description: 'Learn more about Shipping Finance and our mission'
  },
  {
    path: '/contact',
    component: Contact,
    type: RouteType.PUBLIC,
    title: 'Contact Us | Shipping Finance',
    description: 'Get in touch with the Shipping Finance team'
  },
  {
    path: '/faq',
    component: FAQ,
    type: RouteType.PUBLIC,
    title: 'FAQ | Shipping Finance',
    description: 'Frequently asked questions about Shipping Finance'
  },

  // Protected routes
  {
    path: '/profile',
    component: Profile,
    type: RouteType.PROTECTED,
    title: 'Profile | Shipping Finance',
    description: 'Manage your Shipping Finance profile'
  },

  // Admin login route
  {
    path: '/admin/login',
    component: Login, // Using the same Login component for now
    type: RouteType.PUBLIC,
    title: 'Admin Login | Shipping Finance',
    description: 'Login to your Shipping Finance admin account'
  },

  // Admin routes
  {
    path: '/admin',
    component: AdminDashboard,
    type: RouteType.ADMIN,
    title: 'Admin Dashboard | Shipping Finance',
    description: 'Shipping Finance administration dashboard',
    exact: true
  },
  {
    path: '/admin/users',
    component: AdminUsers,
    type: RouteType.ADMIN,
    title: 'User Management | Admin | Shipping Finance',
    description: 'Manage users on Shipping Finance'
  },
  {
    path: '/admin/transactions',
    component: AdminTransactions,
    type: RouteType.ADMIN,
    title: 'Transactions | Admin | Shipping Finance',
    description: 'Manage transactions on Shipping Finance'
  },
  {
    path: '/admin/settings',
    component: AdminSettings,
    type: RouteType.ADMIN,
    title: 'Settings | Admin | Shipping Finance',
    description: 'Manage settings on Shipping Finance'
  },
  {
    path: '/admin/transaction/:id',
    component: TransactionDetail,
    type: RouteType.ADMIN,
    title: 'Transaction Detail | Admin | Shipping Finance',
    description: 'View transaction details on Shipping Finance'
  },
  {
    path: '/admin/system-management',
    component: SystemManagement,
    type: RouteType.ADMIN,
    title: 'System Management | Admin | Shipping Finance',
    description: 'Manage system settings on Shipping Finance'
  }
];

// Protected route wrapper component
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, loading } = useAuth();
  const location = useLocation();

  if (loading) {
    return <LoadingSpinner />;
  }

  if (!user) {
    // Save the location they were trying to access for redirect after login
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return <>{children}</>;
};

// Admin route wrapper component
const AdminRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, loading } = useAuth();
  const location = useLocation();

  if (loading) {
    return <LoadingSpinner />;
  }

  if (!user) {
    // Save the location they were trying to access for redirect after login
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (!user.isAdmin) {
    return <Navigate to="/" replace />;
  }

  return <>{children}</>;
};

// Route controller component
const OptimizedRouteController = () => {
  const location = useLocation();

  // Update document title and meta description based on current route
  const updateDocumentMeta = () => {
    const currentRoute = routes.find(route => {
      if (route.exact) {
        return route.path === location.pathname;
      }
      return location.pathname.startsWith(route.path);
    });

    if (currentRoute) {
      document.title = currentRoute.title;

      // Update meta description
      const metaDescription = document.querySelector('meta[name="description"]');
      if (metaDescription && currentRoute.description) {
        metaDescription.setAttribute('content', currentRoute.description);
      }
    } else {
      document.title = 'Shipping Finance';
    }
  };

  // Update meta tags when location changes
  useEffect(() => {
    updateDocumentMeta();
  }, [location]);

  return (
    <Routes>
      {/* Public Routes */}
      {routes
        .filter(route => route.type === RouteType.PUBLIC)
        .map(route => {
          // Special handling for login and register routes
          if (route.path === '/login' || route.path === '/register' || route.path === '/admin/login') {
            return (
              <Route
                key={route.path}
                path={route.path}
                element={
                  <AuthRedirectRoute>
                    <ErrorBoundary>
                      <route.component />
                    </ErrorBoundary>
                  </AuthRedirectRoute>
                }
              />
            );
          }

          // Regular public routes
          return (
            <Route
              key={route.path}
              path={route.path}
              element={
                <ErrorBoundary>
                  <route.component />
                </ErrorBoundary>
              }
            />
          );
        })}

      {/* Protected Routes */}
      {routes
        .filter(route => route.type === RouteType.PROTECTED)
        .map(route => (
          <Route
            key={route.path}
            path={route.path}
            element={
              <ProtectedRoute>
                <ErrorBoundary>
                  <route.component />
                </ErrorBoundary>
              </ProtectedRoute>
            }
          />
        ))}

      {/* Admin Routes */}
      <Route
        path="/admin"
        element={
          <AdminRoute>
            <ErrorBoundary>
              <AdminLayout />
            </ErrorBoundary>
          </AdminRoute>
        }
      >
        {/* Index route for /admin */}
        <Route index element={withSuspense(LazyAdminDashboard)} />

        {/* Nested admin routes */}
        <Route path="users" element={withSuspense(LazyAdminUsers)} />
        <Route path="users/:id" element={withSuspense(LazyAdminUserDetail)} />
        <Route path="transactions" element={withSuspense(LazyAdminTransactions)} />
        <Route path="settings" element={withSuspense(LazyAdminSettings)} />
        <Route path="transaction/:id" element={withSuspense(LazyTransactionDetail)} />
        <Route path="system-management" element={withSuspense(LazySystemManagement)} />
      </Route>

      {/* 404 Route - Catch all unmatched routes */}
      <Route path="*" element={<NotFoundPage />} />
    </Routes>
  );
};

export default OptimizedRouteController;
export { RouteType, routes };
