groups:
  - name: system_alerts
    rules:
      - alert: HighCPUUsage
        expr: rate(process_cpu_seconds_total[5m]) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: High CPU usage detected
          description: CPU usage is above 80% for 5 minutes

      - alert: HighMemoryUsage
        expr: (process_resident_memory_bytes / node_memory_MemTotal_bytes) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: High memory usage detected
          description: Memory usage is above 85% for 5 minutes

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le)) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: High API response time
          description: 95th percentile of API response time is above 1 second

  - name: business_alerts
    rules:
      - alert: TransactionFailureRate
        expr: sum(rate(transactions_total{status="failed"}[5m])) / sum(rate(transactions_total[5m])) * 100 > 5
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: High transaction failure rate
          description: Transaction failure rate is above 5%

      - alert: LowActiveUsers
        expr: active_users < 10
        for: 30m
        labels:
          severity: warning
        annotations:
          summary: Low active user count
          description: Number of active users has been below 10 for 30 minutes

      - alert: UnusualTransactionVolume
        expr: sum(rate(transactions_total[5m])) > historic_avg_transaction_rate * 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: Unusual transaction volume detected
          description: Transaction rate is significantly above historical average

  - name: security_alerts
    rules:
      - alert: HighErrorRate
        expr: sum(rate(http_request_duration_seconds_count{status_code=~"5.."}[5m])) / sum(rate(http_request_duration_seconds_count[5m])) * 100 > 5
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: High error rate detected
          description: Error rate is above 5% for 5 minutes

      - alert: APIRateLimitExceeded
        expr: rate(rate_limit_exceeded_total[5m]) > 0
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: Rate limit exceeded frequently
          description: API rate limiting is being triggered frequently

      - alert: DatabaseConnectionIssues
        expr: mongodb_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: Database connection lost
          description: MongoDB connection is down

  - name: CryptoYield
    rules:
      # High Error Rate Alert
      - alert: HighErrorRate
        expr: rate(error_rate_total[5m]) > 0.05
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Yüksek hata oranı tespit edildi"
          description: "Son 5 dakikada %5'ten fazla hata oranı tespit edildi"

      # Slow Response Time Alert
      - alert: SlowResponseTime
        expr: rate(http_request_duration_seconds_sum[5m]) / rate(http_request_duration_seconds_count[5m]) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Yavaş yanıt süreleri"
          description: "Ortalama yanıt süresi 1 saniyenin üzerinde"

      # High CPU Usage
      - alert: HighCPUUsage
        expr: avg(rate(process_cpu_seconds_total[5m])) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Yüksek CPU kullanımı"
          description: "CPU kullanımı %80'in üzerinde"

      # Memory Usage
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Yüksek bellek kullanımı"
          description: "Bellek kullanımı %85'in üzerinde"

      # Database Connection Issues
      - alert: DatabaseConnectionIssues
        expr: mongodb_connections{state="available"} < 5
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Veritabanı bağlantı sorunu"
          description: "Kullanılabilir MongoDB bağlantı sayısı kritik seviyede"

      # API Endpoint Errors
      - alert: APIEndpointErrors
        expr: sum(rate(http_requests_total{status_code=~"5.."}[5m])) by (route) > 0
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "API endpoint hataları"
          description: "{{ $labels.route }} endpoint'inde 5xx hataları tespit edildi"

      # Wallet Operation Failures
      - alert: WalletOperationFailures
        expr: rate(wallet_operations_total{status="failed"}[5m]) > 0
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Başarısız cüzdan işlemleri"
          description: "{{ $labels.operation_type }} türünde başarısız cüzdan işlemleri tespit edildi"

      # Transaction Volume Anomaly
      - alert: TransactionVolumeAnomaly
        expr: abs(rate(transaction_volume_total[1h]) - rate(transaction_volume_total[1h] offset 1h)) / rate(transaction_volume_total[1h] offset 1h) > 0.3
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "İşlem hacminde anormal değişim"
          description: "Son bir saatte işlem hacminde %30'dan fazla değişim tespit edildi"

      # Active User Count Drop
      - alert: ActiveUsersDrop
        expr: active_users < 10
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Aktif kullanıcı sayısında düşüş"
          description: "Aktif kullanıcı sayısı kritik seviyenin altında"

      # High Rate Limiting
      - alert: HighRateLimiting
        expr: rate(http_requests_total{status_code="429"}[5m]) > 0
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Yüksek oranda rate limiting"
          description: "Rate limiting nedeniyle reddedilen istekler tespit edildi"