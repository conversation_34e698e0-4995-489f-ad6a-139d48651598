import { useState, useEffect, useCallback } from 'react';

interface MaintenanceStatus {
  maintenance: boolean;
  message?: string;
  siteName?: string;
  estimatedTime?: string;
}

interface UseMaintenanceModeReturn {
  isMaintenanceMode: boolean;
  maintenanceData: MaintenanceStatus | null;
  isLoading: boolean;
  error: string | null;
  checkMaintenanceStatus: () => Promise<void>;
}

/**
 * Hook to check and monitor maintenance mode status
 */
export const useMaintenanceMode = (checkInterval: number = 30000): UseMaintenanceModeReturn => {
  const [isMaintenanceMode, setIsMaintenanceMode] = useState<boolean>(false);
  const [maintenanceData, setMaintenanceData] = useState<MaintenanceStatus | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const checkMaintenanceStatus = useCallback(async () => {
    try {
      setError(null);
      
      const response = await fetch('/api/system/maintenance-status', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        // Don't include credentials for this public endpoint
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        setMaintenanceData(data.data);
        setIsMaintenanceMode(data.data.maintenance);
      } else {
        throw new Error(data.message || 'Failed to get maintenance status');
      }
    } catch (err: any) {
      console.error('Error checking maintenance status:', err);
      setError(err.message || 'Failed to check maintenance status');
      
      // If we can't check maintenance status, assume system is available
      // This prevents the app from being stuck in maintenance mode due to network issues
      setIsMaintenanceMode(false);
      setMaintenanceData(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    // Initial check
    checkMaintenanceStatus();

    // Set up periodic checking if interval is provided
    let interval: NodeJS.Timeout | null = null;
    
    if (checkInterval > 0) {
      interval = setInterval(checkMaintenanceStatus, checkInterval);
    }

    // Cleanup
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [checkMaintenanceStatus, checkInterval]);

  return {
    isMaintenanceMode,
    maintenanceData,
    isLoading,
    error,
    checkMaintenanceStatus,
  };
};
