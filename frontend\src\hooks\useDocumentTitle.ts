import { useEffect, useRef } from 'react';

/**
 * Custom hook for managing document title
 * 
 * Features:
 * - Sets document title on component mount
 * - Restores previous title on component unmount
 * - Supports dynamic title updates
 * - Prevents unnecessary updates
 * 
 * @param title - The title to set for the document
 * @param restoreOnUnmount - Whether to restore the previous title on unmount (default: true)
 */
const useDocumentTitle = (title: string, restoreOnUnmount = true): void => {
  const previousTitle = useRef(document.title);
  
  useEffect(() => {
    // Skip update if title hasn't changed
    if (document.title !== title) {
      // Save the current title before updating
      previousTitle.current = document.title;
      
      // Update the document title
      document.title = title;
    }
    
    // Restore the previous title on unmount if enabled
    return () => {
      if (restoreOnUnmount) {
        document.title = previousTitle.current;
      }
    };
  }, [title, restoreOnUnmount]);
};

export default useDocumentTitle;
