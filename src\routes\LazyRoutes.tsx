import React, { lazy, Suspense } from 'react';
import { Box, Spinner, Center } from '@chakra-ui/react';

// Fallback loading component
const LoadingFallback = () => (
  <Center h="100vh" w="100%">
    <Spinner
      thickness="4px"
      speed="0.65s"
      emptyColor="gray.200"
      color="yellow.500"
      size="xl"
    />
  </Center>
);

// Lazy load all pages
export const LazyHome = lazy(() => import('../pages/Home'));
export const LazyProfile = lazy(() => import('../pages/Profile'));
export const LazyLogin = lazy(() => import('../pages/Login'));
export const LazyRegister = lazy(() => import('../pages/Register'));
export const LazyFAQ = lazy(() => import('../pages/FAQ'));
export const LazyAbout = lazy(() => import('../pages/About'));
export const LazyContact = lazy(() => import('../pages/Contact'));
export const LazyTerms = lazy(() => import('../pages/Terms'));
export const LazyPrivacy = lazy(() => import('../pages/Privacy'));
export const LazyNotFound = lazy(() => import('../pages/NotFound'));

// Admin pages
export const LazyAdminDashboard = lazy(() => import('../pages/admin/AdminDashboard'));
export const LazyAdminUsers = lazy(() => import('../pages/admin/AdminUsers'));
export const LazyAdminUserDetail = lazy(() => import('../pages/admin/AdminUserDetail'));
export const LazyAdminTransactions = lazy(() => import('../pages/admin/AdminTransactionsFixed'));
export const LazyAdminSettings = lazy(() => import('../pages/admin/AdminSettings'));
export const LazySystemManagement = lazy(() => import('../pages/admin/SystemManagement'));
export const LazyTransactionDetail = lazy(() => import('../pages/admin/TransactionDetail'));

// Wrapper component to add Suspense
export const withSuspense = (Component: React.ComponentType<any>) => (props: any) => (
  <Suspense fallback={<LoadingFallback />}>
    <Component {...props} />
  </Suspense>
);
