import { useState, useEffect, useMemo } from 'react';
import { ethers } from 'ethers';
import { useWeb3 } from './useWeb3';

// Import contract ABI
// Note: You'll need to create this file with the actual ABI
import CryptoYieldHubABI from '../contracts/CryptoYieldHub.json';

/**
 * Custom hook for interacting with the CryptoYieldHub contract
 */
export const useContract = () => {
  const { signer, provider, account, chainId } = useWeb3();
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  
  // Initialize contract instance
  const contract = useMemo(() => {
    if (!signer) return null;
    
    try {
      const contractAddress = import.meta.env.VITE_CONTRACT_ADDRESS;
      return new ethers.Contract(contractAddress, CryptoYieldHubABI, signer);
    } catch (error) {
      console.error("Contract initialization error:", error);
      setError(error as Error);
      return null;
    }
  }, [signer]);
  
  /**
   * Deposit tokens into the contract
   */
  const deposit = async (token: string, amount: number, mode: 'COMMISSION' | 'INTEREST') => {
    if (!contract || !account) {
      throw new Error('Contract or account not available');
    }
    
    setLoading(true);
    setError(null);
    
    try {
      // Convert amount to wei (assuming 18 decimals)
      const amountInWei = ethers.utils.parseUnits(amount.toString(), 18);
      
      // Call deposit function
      const tx = await contract.deposit(token, amountInWei, {
        gasLimit: 300000 // Set appropriate gas limit
      });
      
      // Wait for transaction to be mined
      const receipt = await tx.wait();
      return receipt;
    } catch (error) {
      console.error("Deposit error:", error);
      setError(error as Error);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  /**
   * Withdraw tokens from the contract
   */
  const withdraw = async (token: string, amount: number) => {
    if (!contract || !account) {
      throw new Error('Contract or account not available');
    }
    
    setLoading(true);
    setError(null);
    
    try {
      // Convert amount to wei (assuming 18 decimals)
      const amountInWei = ethers.utils.parseUnits(amount.toString(), 18);
      
      // Call withdraw function
      const tx = await contract.withdraw(token, amountInWei, {
        gasLimit: 300000 // Set appropriate gas limit
      });
      
      // Wait for transaction to be mined
      const receipt = await tx.wait();
      return receipt;
    } catch (error) {
      console.error("Withdrawal error:", error);
      setError(error as Error);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  /**
   * Toggle between commission and interest mode
   */
  const toggleMode = async (token: string) => {
    if (!contract || !account) {
      throw new Error('Contract or account not available');
    }
    
    setLoading(true);
    setError(null);
    
    try {
      // Call toggleMode function
      const tx = await contract.toggleMode(token, {
        gasLimit: 200000 // Set appropriate gas limit
      });
      
      // Wait for transaction to be mined
      const receipt = await tx.wait();
      return receipt;
    } catch (error) {
      console.error("Toggle mode error:", error);
      setError(error as Error);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  /**
   * Get user asset details
   */
  const getUserAsset = async (token: string) => {
    if (!contract || !account) {
      throw new Error('Contract or account not available');
    }
    
    setLoading(true);
    setError(null);
    
    try {
      // Call getUserAsset function
      const asset = await contract.getUserAsset(account, token);
      
      // Format the response
      return {
        balance: ethers.utils.formatUnits(asset.balance, 18),
        commissionBalance: ethers.utils.formatUnits(asset.commissionBalance, 18),
        interestBalance: ethers.utils.formatUnits(asset.interestBalance, 18),
        mode: asset.mode === 0 ? 'COMMISSION' : 'INTEREST',
        pendingInterest: ethers.utils.formatUnits(asset.pendingInterest, 18)
      };
    } catch (error) {
      console.error("Get user asset error:", error);
      setError(error as Error);
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  return {
    contract,
    loading,
    error,
    deposit,
    withdraw,
    toggleMode,
    getUserAsset
  };
};
