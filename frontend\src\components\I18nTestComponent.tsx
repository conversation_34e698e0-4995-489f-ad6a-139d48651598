import React from 'react';
import {
  Box,
  VStack,
  HStack,
  Heading,
  Text,
  Button,
  Card,
  CardBody,
  Badge,
  Divider,
  Grid,
  GridItem,
  useColorModeValue,
} from '@chakra-ui/react';
import { useI18n, useCommonTranslation, useAuthTranslation, useDashboardTranslation, useWalletTranslation, useInvestmentTranslation } from '../hooks/useI18n';
import LanguageSwitcher from './LanguageSwitcher';

/**
 * I18nTestComponent
 *
 * This component demonstrates and tests all i18n functionality
 * including translations, formatting, and language switching.
 */
const I18nTestComponent: React.FC = () => {
  const {
    t,
    currentLanguage,
    languageInfo,
    formatCurrency,
    formatNumber,
    formatDate,
    formatDateTime,
    formatRelativeTime,
    direction,
    isRTL
  } = useI18n();

  const { t: tCommon } = useCommonTranslation();
  const { t: tAuth } = useAuthTranslation();
  const { t: tDashboard } = useDashboardTranslation();
  const { t: tWallet } = useWalletTranslation();
  const { t: tInvestment } = useInvestmentTranslation();

  // Theme colors
  const bgColor = useColorModeValue('white', '#1E2026');
  const borderColor = useColorModeValue('gray.200', '#2D3748');
  const textColor = useColorModeValue('gray.800', 'white');

  // Sample data for testing
  const sampleAmount = 1234.56;
  const sampleDate = new Date();
  const samplePastDate = new Date(Date.now() - 2 * 60 * 60 * 1000); // 2 hours ago

  return (
    <Box p={6} bg={bgColor} borderRadius="lg" borderWidth="1px" borderColor={borderColor}>
      <VStack spacing={6} align="stretch">
        {/* Header */}
        <HStack justify="space-between" align="center">
          <Heading size="lg" color={textColor}>
            🌐 i18n Test Component
          </Heading>
          <LanguageSwitcher variant="menu" size="md" />
        </HStack>

        <Divider />

        {/* Language Information */}
        <Card>
          <CardBody>
            <VStack spacing={3} align="start">
              <Heading size="md" color={textColor}>
                {t('common.navigation.settings')} - Language Information
              </Heading>
              <Grid templateColumns="repeat(2, 1fr)" gap={4} w="full">
                <GridItem>
                  <Text><strong>Current Language:</strong> {languageInfo.name}</Text>
                </GridItem>
                <GridItem>
                  <Text><strong>Language Code:</strong> {languageInfo.code}</Text>
                </GridItem>
                <GridItem>
                  <Text><strong>Native Name:</strong> {languageInfo.nativeName}</Text>
                </GridItem>
                <GridItem>
                  <Text><strong>Flag:</strong> {languageInfo.flag}</Text>
                </GridItem>
                <GridItem>
                  <Text><strong>Direction:</strong> {direction}</Text>
                </GridItem>
                <GridItem>
                  <Text><strong>Is RTL:</strong> {isRTL ? 'Yes' : 'No'}</Text>
                </GridItem>
              </Grid>
            </VStack>
          </CardBody>
        </Card>

        {/* Common Translations */}
        <Card>
          <CardBody>
            <VStack spacing={3} align="start">
              <Heading size="md" color={textColor}>
                Common Translations
              </Heading>
              <Grid templateColumns="repeat(3, 1fr)" gap={4} w="full">
                <GridItem>
                  <VStack align="start" spacing={2}>
                    <Text fontWeight="bold">Buttons:</Text>
                    <Button size="sm" variant="outline">{tCommon('buttons.login')}</Button>
                    <Button size="sm" variant="outline">{tCommon('buttons.register')}</Button>
                    <Button size="sm" variant="outline">{tCommon('buttons.save')}</Button>
                  </VStack>
                </GridItem>
                <GridItem>
                  <VStack align="start" spacing={2}>
                    <Text fontWeight="bold">Navigation:</Text>
                    <Text>{tCommon('navigation.home')}</Text>
                    <Text>{tCommon('navigation.dashboard')}</Text>
                    <Text>{tCommon('navigation.profile')}</Text>
                  </VStack>
                </GridItem>
                <GridItem>
                  <VStack align="start" spacing={2}>
                    <Text fontWeight="bold">Status:</Text>
                    <Badge colorScheme="green">{tCommon('status.active')}</Badge>
                    <Badge colorScheme="yellow">{tCommon('status.pending')}</Badge>
                    <Badge colorScheme="red">{tCommon('status.failed')}</Badge>
                  </VStack>
                </GridItem>
              </Grid>
            </VStack>
          </CardBody>
        </Card>

        {/* Auth Translations */}
        <Card>
          <CardBody>
            <VStack spacing={3} align="start">
              <Heading size="md" color={textColor}>
                Auth Translations
              </Heading>
              <Grid templateColumns="repeat(2, 1fr)" gap={4} w="full">
                <GridItem>
                  <VStack align="start" spacing={2}>
                    <Text fontWeight="bold">Login:</Text>
                    <Text>{tAuth('login.title')}</Text>
                    <Text>{tAuth('login.email')}</Text>
                    <Text>{tAuth('login.password')}</Text>
                  </VStack>
                </GridItem>
                <GridItem>
                  <VStack align="start" spacing={2}>
                    <Text fontWeight="bold">Register:</Text>
                    <Text>{tAuth('register.title')}</Text>
                    <Text>{tAuth('register.firstName')}</Text>
                    <Text>{tAuth('register.lastName')}</Text>
                  </VStack>
                </GridItem>
              </Grid>
            </VStack>
          </CardBody>
        </Card>

        {/* Dashboard Translations */}
        <Card>
          <CardBody>
            <VStack spacing={3} align="start">
              <Heading size="md" color={textColor}>
                Dashboard Translations
              </Heading>
              <Grid templateColumns="repeat(2, 1fr)" gap={4} w="full">
                <GridItem>
                  <VStack align="start" spacing={2}>
                    <Text fontWeight="bold">Overview:</Text>
                    <Text>{tDashboard('overview.totalBalance')}</Text>
                    <Text>{tDashboard('overview.totalInvestment')}</Text>
                    <Text>{tDashboard('overview.totalEarnings')}</Text>
                  </VStack>
                </GridItem>
                <GridItem>
                  <VStack align="start" spacing={2}>
                    <Text fontWeight="bold">Quick Actions:</Text>
                    <Text>{tDashboard('quickActions.newInvestment')}</Text>
                    <Text>{tDashboard('quickActions.withdraw')}</Text>
                    <Text>{tDashboard('quickActions.deposit')}</Text>
                  </VStack>
                </GridItem>
              </Grid>
            </VStack>
          </CardBody>
        </Card>

        {/* Formatting Tests */}
        <Card>
          <CardBody>
            <VStack spacing={3} align="start">
              <Heading size="md" color={textColor}>
                Formatting Tests
              </Heading>
              <Grid templateColumns="repeat(2, 1fr)" gap={4} w="full">
                <GridItem>
                  <VStack align="start" spacing={2}>
                    <Text fontWeight="bold">Currency Formatting:</Text>
                    <Text>USD: {formatCurrency(sampleAmount, 'USD')}</Text>
                    <Text>TRY: {formatCurrency(sampleAmount, 'TRY')}</Text>
                    <Text>Default: {formatCurrency(sampleAmount)}</Text>
                  </VStack>
                </GridItem>
                <GridItem>
                  <VStack align="start" spacing={2}>
                    <Text fontWeight="bold">Number Formatting:</Text>
                    <Text>Number: {formatNumber(sampleAmount)}</Text>
                    <Text>Percentage: {formatNumber(0.1234, { style: 'percent' })}</Text>
                    <Text>Decimal: {formatNumber(sampleAmount, { minimumFractionDigits: 4 })}</Text>
                  </VStack>
                </GridItem>
                <GridItem>
                  <VStack align="start" spacing={2}>
                    <Text fontWeight="bold">Date Formatting:</Text>
                    <Text>Date: {formatDate(sampleDate)}</Text>
                    <Text>DateTime: {formatDateTime(sampleDate)}</Text>
                    <Text>Relative: {formatRelativeTime(samplePastDate)}</Text>
                  </VStack>
                </GridItem>
                <GridItem>
                  <VStack align="start" spacing={2}>
                    <Text fontWeight="bold">Interpolation:</Text>
                    <Text>{t('dashboard.welcome', { name: 'John Doe' })}</Text>
                    <Text>{t('common.validation.minLength', { min: 8 })}</Text>
                    <Text>{t('common.validation.maxLength', { max: 50 })}</Text>
                  </VStack>
                </GridItem>
              </Grid>
            </VStack>
          </CardBody>
        </Card>

        {/* Validation Messages */}
        <Card>
          <CardBody>
            <VStack spacing={3} align="start">
              <Heading size="md" color={textColor}>
                Validation Messages
              </Heading>
              <VStack align="start" spacing={2}>
                <Text color="red.500">{tCommon('validation.required')}</Text>
                <Text color="red.500">{tCommon('validation.email')}</Text>
                <Text color="red.500">{tCommon('validation.password')}</Text>
                <Text color="red.500">{tCommon('validation.passwordMatch')}</Text>
              </VStack>
            </VStack>
          </CardBody>
        </Card>

        {/* Investment Translations */}
        <Card>
          <CardBody>
            <VStack spacing={3} align="start">
              <Heading size="md" color={textColor}>
                Investment Translations
              </Heading>
              <Grid templateColumns="repeat(2, 1fr)" gap={4} w="full">
                <GridItem>
                  <VStack align="start" spacing={2}>
                    <Text fontWeight="bold">Packages:</Text>
                    <Text>{tInvestment('packages.title')}</Text>
                    <Text>{tInvestment('packages.noPackages')}</Text>
                    <Text>{tInvestment('packages.loading')}</Text>
                  </VStack>
                </GridItem>
                <GridItem>
                  <VStack align="start" spacing={2}>
                    <Text fontWeight="bold">My Investments:</Text>
                    <Text>{tInvestment('myInvestments.title')}</Text>
                    <Text>{tInvestment('myInvestments.active')}</Text>
                    <Text>{tInvestment('myInvestments.completed')}</Text>
                  </VStack>
                </GridItem>
              </Grid>
            </VStack>
          </CardBody>
        </Card>

        {/* Wallet Translations */}
        <Card>
          <CardBody>
            <VStack spacing={3} align="start">
              <Heading size="md" color={textColor}>
                Wallet Translations
              </Heading>
              <Grid templateColumns="repeat(2, 1fr)" gap={4} w="full">
                <GridItem>
                  <VStack align="start" spacing={2}>
                    <Text fontWeight="bold">Overview:</Text>
                    <Text>{tWallet('overview.totalBalance')}</Text>
                    <Text>{tWallet('overview.availableBalance')}</Text>
                    <Text>{tWallet('overview.lockedBalance')}</Text>
                  </VStack>
                </GridItem>
                <GridItem>
                  <VStack align="start" spacing={2}>
                    <Text fontWeight="bold">Actions:</Text>
                    <Text>{tWallet('deposit.title')}</Text>
                    <Text>{tWallet('withdrawal.title')}</Text>
                    <Text>{tWallet('transfer.title')}</Text>
                  </VStack>
                </GridItem>
              </Grid>
            </VStack>
          </CardBody>
        </Card>

        {/* Success Messages */}
        <Card>
          <CardBody>
            <VStack spacing={3} align="start">
              <Heading size="md" color={textColor}>
                Success Messages
              </Heading>
              <VStack align="start" spacing={2}>
                <Text color="green.500">{tCommon('messages.saved')}</Text>
                <Text color="green.500">{tCommon('messages.updated')}</Text>
                <Text color="green.500">{tCommon('messages.created')}</Text>
                <Text color="green.500">{tAuth('login.loginSuccess')}</Text>
              </VStack>
            </VStack>
          </CardBody>
        </Card>
      </VStack>
    </Box>
  );
};

export default I18nTestComponent;
