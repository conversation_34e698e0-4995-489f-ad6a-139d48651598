# CryptoYield Deployment Guide

This guide explains how to deploy the CryptoYield platform using Docker.

## Prerequisites

- Docker Engine 24.0+
- Docker Compose 2.0+
- Git
- Node.js 20+ (for local development only)

## Deployment Steps

1. Clone the repository and navigate to the project directory:
```bash
git clone https://github.com/yourusername/cryptoyield.git
cd cryptoyield
```

2. Set up environment variables:
```bash
cp .env.example .env
```
Edit the `.env` file and update all configuration values with secure credentials.

3. Build and start the containers:
```bash
docker compose up -d --build
```

4. Verify the deployment:
- Frontend: http://localhost
- Backend health check: http://localhost/api/health
- MongoDB status: Use `docker compose logs mongodb`

## Container Architecture

The deployment consists of three main services:

1. **Frontend (Nginx + React)**
   - Serves the React application
   - Handles static file caching
   - Routes API requests to backend
   - Implements security headers
   - Port: 80

2. **Backend (Node.js + Express)**
   - Handles API requests
   - Connects to MongoDB
   - Manages authentication
   - Port: 5000 (internal)

3. **MongoDB**
   - Stores application data
   - Port: 27017 (internal)
   - Persisted via Docker volume

## Health Checks

All services implement health checks:
- MongoDB: Ping command every 10s
- Backend: HTTP request to /health endpoint every 10s
- Frontend: Depends on backend health

## Backup and Recovery

### Database Backup
```bash
docker compose exec mongodb mongodump --uri="mongodb://${MONGO_USER}:${MONGO_PASSWORD}@localhost:27017/cryptoyield?authSource=admin" --out=/data/backup/
```

### Database Restore
```bash
docker compose exec mongodb mongorestore --uri="mongodb://${MONGO_USER}:${MONGO_PASSWORD}@localhost:27017/cryptoyield?authSource=admin" /data/backup/
```

## Monitoring

1. View container logs:
```bash
docker compose logs -f [service_name]
```

2. Check container status:
```bash
docker compose ps
```

3. Monitor resource usage:
```bash
docker stats
```

## Troubleshooting

1. If containers fail to start:
```bash
docker compose logs [service_name]
```

2. To restart a service:
```bash
docker compose restart [service_name]
```

3. To rebuild a specific service:
```bash
docker compose up -d --build [service_name]
```

4. Common issues:
   - MongoDB connection failures: Check credentials in .env
   - Frontend can't reach backend: Verify network configuration
   - Backend startup issues: Check logs for database connection

## Security Considerations

1. **Environment Variables**
   - Never commit .env files
   - Use strong passwords
   - Rotate JWT secrets regularly

2. **Network Security**
   - Only port 80 is exposed publicly
   - Internal services use Docker network
   - MongoDB is not accessible externally

3. **SSL/TLS**
   - Configure SSL in production
   - Update Nginx configuration accordingly
   - Use valid certificates

## Production Optimizations

1. **Scaling**
   - Backend can be scaled horizontally:
     ```bash
     docker compose up -d --scale backend=3
     ```
   - Use load balancer for multiple frontend instances

2. **Performance**
   - MongoDB indexing is automatic
   - Nginx caches static assets
   - Backend implements rate limiting

## Updates and Maintenance

1. To update the application:
```bash
git pull
docker compose down
docker compose up -d --build
```

2. To update base images:
```bash
docker compose pull
docker compose up -d
```

3. Maintenance mode:
```bash
docker compose stop frontend
# Perform maintenance
docker compose start frontend
```

## Monitoring and Logging

1. Set up log aggregation:
   - Backend logs to winston
   - All containers log to Docker
   - Consider ELK stack for production

2. Metrics collection:
   - API response times
   - Database performance
   - System resources

## Support

For issues and support:
- Create GitHub issues
- Check container logs
- Review application logs in /logs directory
- Contact support team