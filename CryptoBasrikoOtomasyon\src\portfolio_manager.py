import json
import os
import logging
from datetime import datetime

class PortfolioManager:
    """Kullanıcı portföylerini yöneten sınıf"""
    
    def __init__(self, crypto_api):
        self.crypto_api = crypto_api
        self.portfolios_file = 'data/portfolios.json'
        self.logger = logging.getLogger('PortfolioManager')
        self.portfolios = self._load_portfolios()
        self.logger.info("PortfolioManager başlatıldı")
    
    def _load_portfolios(self):
        """Portföy verilerini yükle"""
        try:
            if os.path.exists(self.portfolios_file):
                with open(self.portfolios_file, 'r') as f:
                    return json.load(f)
            else:
                # <PERSON><PERSON>a yoksa boş bir portföy sözlüğü oluştur
                return {}
        except Exception as e:
            self.logger.error(f"Portföy yüklenirken hata: {str(e)}")
            return {}
    
    def _save_portfolios(self):
        """Portföy verilerini kaydet"""
        try:
            # <PERSON><PERSON><PERSON>r yoksa oluştur
            os.makedirs(os.path.dirname(self.portfolios_file), exist_ok=True)
            
            with open(self.portfolios_file, 'w') as f:
                json.dump(self.portfolios, f, indent=4)
            return True
        except Exception as e:
            self.logger.error(f"Portföy kaydedilirken hata: {str(e)}")
            return False
    
    def get_user_portfolio(self, user_id):
        """Kullanıcının portföyünü getir"""
        if user_id not in self.portfolios:
            # Kullanıcı için yeni portföy oluştur
            self.portfolios[user_id] = {
                "assets": [],
                "total_commission_earned": 0,
                "total_interest_earned": 0,
                "last_updated": datetime.now().isoformat()
            }
            self._save_portfolios()
        
        # Güncel fiyatlarla portföy değerini hesapla
        portfolio = self.portfolios[user_id]
        current_prices = self.crypto_api.get_current_prices()
        
        total_value = 0
        for asset in portfolio.get("assets", []):
            symbol = asset.get("symbol")
            if symbol in current_prices:
                asset["current_price"] = current_prices[symbol]
                asset["value_usd"] = asset["balance"] * current_prices[symbol]
                total_value += asset["value_usd"]
        
        portfolio["total_value_usd"] = total_value
        return portfolio
    
    def update_asset(self, user_id, symbol, amount, mode="commission"):
        """Kullanıcının varlığını güncelle"""
        try:
            if user_id not in self.portfolios:
                self.get_user_portfolio(user_id)  # Yeni portföy oluştur
            
            portfolio = self.portfolios[user_id]
            assets = portfolio.get("assets", [])
            
            # Varlık var mı kontrol et
            asset_exists = False
            for asset in assets:
                if asset["symbol"] == symbol:
                    asset_exists = True
                    asset["balance"] += amount
                    asset["mode"] = mode
                    break
            
            # Varlık yoksa ekle
            if not asset_exists:
                assets.append({
                    "symbol": symbol,
                    "balance": amount,
                    "commissionBalance": 0,
                    "interestBalance": 0,
                    "mode": mode,
                    "added_date": datetime.now().isoformat()
                })
                portfolio["assets"] = assets
            
            portfolio["last_updated"] = datetime.now().isoformat()
            self._save_portfolios()
            return self.get_user_portfolio(user_id)
        except Exception as e:
            self.logger.error(f"Varlık güncellenirken hata: {str(e)}")
            return None
    
    def add_commission(self, user_id, symbol, amount):
        """Komisyon ekle"""
        try:
            portfolio = self.get_user_portfolio(user_id)
            assets = portfolio.get("assets", [])
            
            for asset in assets:
                if asset["symbol"] == symbol:
                    asset["commissionBalance"] = asset.get("commissionBalance", 0) + amount
                    portfolio["total_commission_earned"] = portfolio.get("total_commission_earned", 0) + amount
                    break
            
            portfolio["last_updated"] = datetime.now().isoformat()
            self._save_portfolios()
            return True
        except Exception as e:
            self.logger.error(f"Komisyon eklenirken hata: {str(e)}")
            return False
    
    def add_interest(self, user_id, symbol, amount):
        """Faiz ekle"""
        try:
            portfolio = self.get_user_portfolio(user_id)
            assets = portfolio.get("assets", [])
            
            for asset in assets:
                if asset["symbol"] == symbol:
                    asset["interestBalance"] = asset.get("interestBalance", 0) + amount
                    portfolio["total_interest_earned"] = portfolio.get("total_interest_earned", 0) + amount
                    break
            
            portfolio["last_updated"] = datetime.now().isoformat()
            self._save_portfolios()
            return True
        except Exception as e:
            self.logger.error(f"Faiz eklenirken hata: {str(e)}")
            return False
