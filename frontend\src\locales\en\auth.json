{"login": {"title": "<PERSON><PERSON>", "subtitle": "Sign in to your account", "email": "Email", "password": "Password", "rememberMe": "Remember me", "forgotPassword": "Forgot password", "loginButton": "<PERSON><PERSON>", "noAccount": "Don't have an account?", "registerLink": "Register", "welcomeBack": "Welcome back!", "loginSuccess": "Successfully logged in", "loginError": "<PERSON><PERSON> failed", "invalidCredentials": "Invalid email or password", "accountLocked": "Your account is locked", "accountNotVerified": "Your account is not verified", "tooManyAttempts": "Too many failed attempts", "sessionExpired": "Your session has expired", "loginRequired": "You must login to access this page"}, "register": {"title": "Register", "subtitle": "Create a new account", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "phone": "Phone", "country": "Country", "city": "City", "referralCode": "Referral Code (Optional)", "agreeTerms": "I agree to the terms of service", "agreePrivacy": "I agree to the privacy policy", "marketingConsent": "I agree to receive marketing emails", "registerButton": "Register", "haveAccount": "Already have an account?", "loginLink": "<PERSON><PERSON>", "registrationSuccess": "Registration successful! Check your email", "registrationError": "Registration failed", "emailExists": "This email address is already in use", "weakPassword": "Password is too weak", "invalidEmail": "Invalid email address", "termsRequired": "You must agree to the terms of service", "privacyRequired": "You must agree to the privacy policy"}, "forgotPassword": {"title": "Forgot Password", "subtitle": "We'll send you a password reset link", "email": "Email", "sendButton": "Send", "backToLogin": "Back to login", "emailSent": "Password reset link sent to your email", "emailNotFound": "This email address was not found", "rateLimited": "Too many requests. Please wait"}, "resetPassword": {"title": "Reset Password", "subtitle": "Set your new password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "resetButton": "Reset Password", "resetSuccess": "Your password has been reset successfully", "resetError": "Password reset failed", "invalidToken": "Invalid or expired link", "passwordMismatch": "Passwords do not match"}, "verification": {"title": "Email Verification", "subtitle": "Enter the code sent to your email", "code": "Verification Code", "verifyButton": "Verify", "resendCode": "Resend code", "verificationSuccess": "Email verified successfully", "verificationError": "Verification failed", "invalidCode": "Invalid verification code", "expiredCode": "Verification code has expired", "codeSent": "Verification code sent to your email"}, "logout": {"title": "Logout", "message": "Are you sure you want to logout?", "confirmButton": "Logout", "cancelButton": "Cancel", "logoutSuccess": "Successfully logged out", "logoutError": "Error occurred while logging out"}, "twoFactor": {"title": "Two-Factor Authentication", "subtitle": "Enter your security code", "code": "Security Code", "verifyButton": "Verify", "backupCode": "Use backup code", "trustDevice": "Trust this device", "verificationSuccess": "Two-factor authentication successful", "verificationError": "Verification failed", "invalidCode": "Invalid security code", "setup": {"title": "Two-Factor Authentication Setup", "step1": "Download an authenticator app", "step2": "Scan the QR code", "step3": "Enter the verification code", "qrCode": "QR Code", "manualEntry": "Manual entry", "secretKey": "Secret Key", "verificationCode": "Verification Code", "enableButton": "Enable", "setupSuccess": "Two-factor authentication enabled", "setupError": "Setup failed"}}}