import React, { useState, useEffect } from 'react';
import {
  Box,
  Heading,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  <PERSON>ge,
  Button,
  Flex,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  HStack,
  useToast,
  Text,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Textarea,
  useDisclosure
} from '@chakra-ui/react';
import { SearchIcon } from '@chakra-ui/icons';
import { FaCheck, FaTimes, FaEye } from 'react-icons/fa';
import axios from 'axios';
import { SocketService } from '../../utils/socketService';
import useAuth from '../../hooks/useAuth';
import { adminApiService } from '../../services/adminApi';

// Định nghĩa kiểu dữ liệu cho withdrawal
interface Withdrawal {
  id: string;
  user: string;
  email: string;
  wallet: string;
  amount: number;
  currency: string;
  date: string;
  status: string;
  txHash: string;
  userId?: string;
}

const AdminWithdrawals = () => {
  const toast = useToast();
  const [withdrawals, setWithdrawals] = useState<Withdrawal[]>([]);
  const { user } = useAuth();
  const socketService = SocketService.getInstance();

  // Load withdrawals from API
  useEffect(() => {
    const loadWithdrawals = async () => {
      try {
        // Try to get data from API using adminApiService
        try {
          console.log('Fetching withdrawals using adminApiService...');
          const response = await adminApiService.getWithdrawals({
            limit: 50 // Get more withdrawals at once
          });

          console.log('Withdrawals API response:', response.data);

          if (response.data && response.data.withdrawals) {
            // Format the withdrawals data
            const apiWithdrawals = response.data.withdrawals.map((withdrawal: any): Withdrawal => ({
              id: withdrawal.id,
              user: withdrawal.user,
              email: withdrawal.email,
              wallet: withdrawal.wallet,
              amount: withdrawal.amount,
              currency: withdrawal.currency,
              date: new Date(withdrawal.date).toLocaleDateString(),
              status: withdrawal.status,
              txHash: withdrawal.txHash || ''
            }));

            console.log('Formatted withdrawals:', apiWithdrawals);
            setWithdrawals(apiWithdrawals);
            return; // Exit if API call was successful
          }
        } catch (err) {
          console.error('Error fetching withdrawals from API:', err);
          // Fall back to localStorage if API fails
        }

        // Fallback to localStorage if API is not available
        const storedTransactions = localStorage.getItem('transactions');
        if (storedTransactions) {
          const parsedTransactions = JSON.parse(storedTransactions);
          // Filter only withdrawal transactions
          const withdrawalTransactions = parsedTransactions
            .filter((tx: any) => tx.type === 'withdrawal')
            .map((tx: any, index: number): Withdrawal => ({
              id: tx.id,
              user: `User ${index + 1}`,
              email: `user${index + 1}@example.com`,
              wallet: tx.walletAddress || '******************************************',
              amount: tx.amount,
              currency: tx.currency,
              date: new Date(tx.date).toLocaleDateString(),
              status: tx.status,
              txHash: tx.txHash
            }));

          setWithdrawals(withdrawalTransactions);
        } else {
          // Nếu không có dữ liệu từ localStorage, hiển thị mảng rỗng
          setWithdrawals([]);
        }
      } catch (error) {
        console.error('Error loading withdrawals:', error);
        setWithdrawals([]);
      }
    };

    loadWithdrawals();

    // Add event listener for storage changes
    window.addEventListener('storage', loadWithdrawals);

    // Custom event for transaction updates
    window.addEventListener('transactionUpdated', loadWithdrawals);

    return () => {
      window.removeEventListener('storage', loadWithdrawals);
      window.removeEventListener('transactionUpdated', loadWithdrawals);
    };
  }, [user]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterCurrency, setFilterCurrency] = useState('all');
  const [selectedWithdrawal, setSelectedWithdrawal] = useState<Withdrawal | null>(null);
  const [adminNotes, setAdminNotes] = useState('');
  const [txHash, setTxHash] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { isOpen, onOpen, onClose } = useDisclosure();

  // Colors
  const bgColor = "#1E2329";
  const cardBgColor = "#0B0E11";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";

  // Filter withdrawals based on search query, status, and currency
  const filteredWithdrawals = withdrawals.filter(withdrawal => {
    const matchesSearch =
      withdrawal.user.toLowerCase().includes(searchQuery.toLowerCase()) ||
      withdrawal.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      withdrawal.wallet.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = filterStatus === 'all' || withdrawal.status === filterStatus;
    const matchesCurrency = filterCurrency === 'all' || withdrawal.currency === filterCurrency;

    return matchesSearch && matchesStatus && matchesCurrency;
  });

  const handleViewWithdrawal = (withdrawal: Withdrawal) => {
    setSelectedWithdrawal(withdrawal);
    setAdminNotes('');
    setTxHash(withdrawal.txHash || '');
    onOpen();
  };

  const handleApprove = async () => {
    if (!txHash && selectedWithdrawal.status === 'pending') {
      toast({
        title: "Transaction Hash Required",
        description: "Please enter a transaction hash to approve this withdrawal.",
        status: "warning",
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    setIsSubmitting(true);

    // Try to update on the server using adminApiService with cookie-based auth
    try {
      console.log('Updating withdrawal status using adminApiService...');
      // Use the adminApiService to update withdrawal status
      await adminApiService.updateWithdrawalStatus(selectedWithdrawal.id, {
        status: 'approved',
        txHash: txHash,
        adminNotes: adminNotes
      });

      console.log('Withdrawal status updated successfully');

      // Update UI immediately after successful API call
      setWithdrawals(withdrawals.map(w =>
        w.id === selectedWithdrawal.id ? { ...w, status: 'approved', txHash: txHash } : w
      ));

      toast({
        title: "Withdrawal Approved",
        description: `Withdrawal #${selectedWithdrawal.id} has been approved successfully.`,
        status: "success",
        duration: 3000,
        isClosable: true,
      });

      setIsSubmitting(false);
      onClose();
      return; // Exit if API call was successful
    } catch (err) {
      console.error('Error updating withdrawal status via API:', err);

      // Fallback to regular transactions endpoint
      try {
        const API_URL = import.meta.env.VITE_API_URL;
        if (API_URL) {
          await axios.put(`${API_URL}/transactions/${selectedWithdrawal.id}/status`, {
            status: 'approved',
            txHash: txHash,
            adminNotes: adminNotes
          }, {
            withCredentials: true // Important for cookies
          });

          // Update UI immediately after successful API call
          setWithdrawals(withdrawals.map(w =>
            w.id === selectedWithdrawal.id ? { ...w, status: 'approved', txHash: txHash } : w
          ));

          toast({
            title: "Withdrawal Approved",
            description: `Withdrawal #${selectedWithdrawal.id} has been approved successfully.`,
            status: "success",
            duration: 3000,
            isClosable: true,
          });

          setIsSubmitting(false);
          onClose();
          return; // Exit if API call was successful
        }
      } catch (error) {
        console.error('Error updating transaction on server:', error);
        // Continue to localStorage fallback
      }
    }

    // Fallback to localStorage if API is not available or fails
    try {
      const storedTransactions = localStorage.getItem('transactions');
      if (storedTransactions) {
        const transactions = JSON.parse(storedTransactions);
        const updatedTransactions = transactions.map((tx: any) =>
          tx.id === selectedWithdrawal.id ? { ...tx, status: 'approved', txHash: txHash } : tx
        );
        localStorage.setItem('transactions', JSON.stringify(updatedTransactions));

        // Dispatch event to notify other components
        window.dispatchEvent(new Event('transactionUpdated'));

        // Find the user ID associated with this withdrawal
        const transaction = transactions.find((tx: any) => tx.id === selectedWithdrawal.id);
        if (transaction && transaction.userId) {
          // Notify the user about the transaction update via WebSocket
          socketService.send({
            type: 'transaction_update',
            payload: {
              userId: transaction.userId,
              transaction: {
                id: selectedWithdrawal.id,
                type: 'withdrawal',
                status: 'approved',
                amount: selectedWithdrawal.amount,
                currency: selectedWithdrawal.currency,
                txHash: txHash,
                updatedAt: new Date()
              }
            }
          });
        }
      }
    } catch (error) {
      console.error('Error updating transaction in localStorage:', error);
    }

    // Update UI
    setWithdrawals(withdrawals.map(w =>
      w.id === selectedWithdrawal.id ? { ...w, status: 'approved', txHash: txHash } : w
    ));

    toast({
      title: "Withdrawal Approved",
      description: `Withdrawal #${selectedWithdrawal.id} has been approved successfully.`,
      status: "success",
      duration: 3000,
      isClosable: true,
    });

    setIsSubmitting(false);
    onClose();
  };

  const handleReject = async () => {
    setIsSubmitting(true);

    // Try to update on the server using adminApiService with cookie-based auth
    try {
      console.log('Rejecting withdrawal using adminApiService...');
      // Use the adminApiService to update withdrawal status
      await adminApiService.updateWithdrawalStatus(selectedWithdrawal.id, {
        status: 'rejected',
        adminNotes: adminNotes
      });

      console.log('Withdrawal rejected successfully');

      // Update UI immediately after successful API call
      setWithdrawals(withdrawals.map(w =>
        w.id === selectedWithdrawal.id ? { ...w, status: 'rejected' } : w
      ));

      toast({
        title: "Withdrawal Rejected",
        description: `Withdrawal #${selectedWithdrawal.id} has been rejected.`,
        status: "error",
        duration: 3000,
        isClosable: true,
      });

      setIsSubmitting(false);
      onClose();
      return; // Exit if API call was successful
    } catch (err) {
      console.error('Error rejecting withdrawal via API:', err);

      // Fallback to regular transactions endpoint
      try {
        const API_URL = import.meta.env.VITE_API_URL;
        if (API_URL) {
          await axios.put(`${API_URL}/transactions/${selectedWithdrawal.id}/status`, {
            status: 'rejected',
            adminNotes: adminNotes
          }, {
            withCredentials: true // Important for cookies
          });

          // Update UI immediately after successful API call
          setWithdrawals(withdrawals.map(w =>
            w.id === selectedWithdrawal.id ? { ...w, status: 'rejected' } : w
          ));

          toast({
            title: "Withdrawal Rejected",
            description: `Withdrawal #${selectedWithdrawal.id} has been rejected.`,
            status: "error",
            duration: 3000,
            isClosable: true,
          });

          setIsSubmitting(false);
          onClose();
          return; // Exit if API call was successful
        }
      } catch (error) {
        console.error('Error updating transaction on server:', error);
        // Continue to localStorage fallback
      }
    }

    // Fallback to localStorage if API is not available or fails
    try {
      const storedTransactions = localStorage.getItem('transactions');
      if (storedTransactions) {
        const transactions = JSON.parse(storedTransactions);
        const updatedTransactions = transactions.map((tx: any) =>
          tx.id === selectedWithdrawal.id ? { ...tx, status: 'rejected' } : tx
        );
        localStorage.setItem('transactions', JSON.stringify(updatedTransactions));

        // Dispatch event to notify other components
        window.dispatchEvent(new Event('transactionUpdated'));

        // Find the user ID associated with this withdrawal
        const transaction = transactions.find((tx: any) => tx.id === selectedWithdrawal.id);
        if (transaction && transaction.userId) {
          // Notify the user about the transaction update via WebSocket
          socketService.send({
            type: 'transaction_update',
            payload: {
              userId: transaction.userId,
              transaction: {
                id: selectedWithdrawal.id,
                type: 'withdrawal',
                status: 'rejected',
                amount: selectedWithdrawal.amount,
                currency: selectedWithdrawal.currency,
                updatedAt: new Date()
              }
            }
          });
        }
      }
    } catch (error) {
      console.error('Error updating transaction in localStorage:', error);
    }

    // Update UI
    setWithdrawals(withdrawals.map(w =>
      w.id === selectedWithdrawal.id ? { ...w, status: 'rejected' } : w
    ));

    toast({
      title: "Withdrawal Rejected",
      description: `Withdrawal #${selectedWithdrawal.id} has been rejected.`,
      status: "error",
      duration: 3000,
      isClosable: true,
    });

    setIsSubmitting(false);
    onClose();
  };

  return (
    <Box>
      <Heading size="lg" color="#F0B90B" mb={6}>Withdrawal Management</Heading>

      <Box bg={bgColor} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
        <Flex justify="space-between" align="center" mb={4} flexDir={{ base: "column", md: "row" }} gap={4}>
          <InputGroup maxW={{ base: "100%", md: "300px" }}>
            <InputLeftElement pointerEvents="none">
              <SearchIcon color="#848E9C" />
            </InputLeftElement>
            <Input
              placeholder="Search by user, email or wallet"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              bg={cardBgColor}
              borderColor={borderColor}
              color={textColor}
            />
          </InputGroup>

          <HStack spacing={4}>
            <Select
              maxW={{ base: "100%", md: "150px" }}
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              bg={cardBgColor}
              borderColor={borderColor}
              color={textColor}
            >
              <option value="all">All Status</option>
              <option value="approved">Approved</option>
              <option value="pending">Pending</option>
              <option value="rejected">Rejected</option>
            </Select>

            <Select
              maxW={{ base: "100%", md: "150px" }}
              value={filterCurrency}
              onChange={(e) => setFilterCurrency(e.target.value)}
              bg={cardBgColor}
              borderColor={borderColor}
              color={textColor}
            >
              <option value="all">All Currencies</option>
              <option value="USDT">USDT</option>
              <option value="BTC">BTC</option>
              <option value="ETH">ETH</option>
              <option value="DOGE">DOGE</option>
              <option value="XRP">XRP</option>
            </Select>
          </HStack>
        </Flex>

        <Box overflowX="auto">
          <Table variant="simple" size="md">
            <Thead>
              <Tr>
                <Th color={secondaryTextColor} borderColor={borderColor}>ID</Th>
                <Th color={secondaryTextColor} borderColor={borderColor}>User</Th>
                <Th color={secondaryTextColor} borderColor={borderColor}>Amount</Th>
                <Th color={secondaryTextColor} borderColor={borderColor}>Currency</Th>
                <Th color={secondaryTextColor} borderColor={borderColor}>Date</Th>
                <Th color={secondaryTextColor} borderColor={borderColor}>Status</Th>
                <Th color={secondaryTextColor} borderColor={borderColor}>Actions</Th>
              </Tr>
            </Thead>
            <Tbody>
              {filteredWithdrawals.length > 0 ? (
                filteredWithdrawals.map((withdrawal) => (
                  <Tr key={withdrawal.id}>
                    <Td color={textColor} borderColor={borderColor}>{withdrawal.id}</Td>
                    <Td color={textColor} borderColor={borderColor}>
                      <Text>{withdrawal.user}</Text>
                      <Text fontSize="xs" color={secondaryTextColor}>{withdrawal.email}</Text>
                    </Td>
                    <Td color={textColor} borderColor={borderColor}>{withdrawal.amount}</Td>
                    <Td color={textColor} borderColor={borderColor}>{withdrawal.currency}</Td>
                    <Td color={textColor} borderColor={borderColor}>{withdrawal.date}</Td>
                    <Td borderColor={borderColor}>
                      <Badge
                        colorScheme={
                          withdrawal.status === 'approved' ? 'green' :
                          withdrawal.status === 'pending' ? 'yellow' : 'red'
                        }
                        borderRadius="full"
                        px={2}
                      >
                        {withdrawal.status}
                      </Badge>
                    </Td>
                    <Td borderColor={borderColor}>
                      <HStack spacing={2}>
                        <Button
                          size="sm"
                          colorScheme="blue"
                          leftIcon={<FaEye />}
                          onClick={() => handleViewWithdrawal(withdrawal)}
                        >
                          View
                        </Button>

                        {withdrawal.status === 'pending' && (
                          <>
                            <Button
                              size="sm"
                              colorScheme="green"
                              leftIcon={<FaCheck />}
                              onClick={() => {
                                setSelectedWithdrawal(withdrawal);
                                handleViewWithdrawal(withdrawal);
                              }}
                            >
                              Process
                            </Button>
                            <Button
                              size="sm"
                              colorScheme="red"
                              leftIcon={<FaTimes />}
                              onClick={() => {
                                setSelectedWithdrawal(withdrawal);
                                handleReject();
                              }}
                            >
                              Reject
                            </Button>
                          </>
                        )}
                      </HStack>
                    </Td>
                  </Tr>
                ))
              ) : (
                <Tr>
                  <Td colSpan={7} textAlign="center" color={secondaryTextColor} borderColor={borderColor}>
                    No withdrawals found matching your search criteria.
                  </Td>
                </Tr>
              )}
            </Tbody>
          </Table>
        </Box>
      </Box>

      {/* Withdrawal Detail Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent bg={bgColor} color={textColor} borderColor={borderColor} borderWidth="1px">
          <ModalHeader>Withdrawal Details #{selectedWithdrawal?.id}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {selectedWithdrawal && (
              <Box>
                <Flex direction={{ base: "column", md: "row" }} gap={6}>
                  <Box flex="1">
                    <Text fontWeight="bold" mb={1}>User Information</Text>
                    <Box bg={cardBgColor} p={4} borderRadius="md" mb={4} borderWidth="1px" borderColor={borderColor}>
                      <Text><strong>Name:</strong> {selectedWithdrawal.user}</Text>
                      <Text><strong>Email:</strong> {selectedWithdrawal.email}</Text>
                      <Text><strong>Wallet:</strong> {selectedWithdrawal.wallet}</Text>
                    </Box>

                    <Text fontWeight="bold" mb={1}>Transaction Information</Text>
                    <Box bg={cardBgColor} p={4} borderRadius="md" mb={4} borderWidth="1px" borderColor={borderColor}>
                      <Text><strong>Amount:</strong> {selectedWithdrawal.amount} {selectedWithdrawal.currency}</Text>
                      <Text><strong>Date:</strong> {selectedWithdrawal.date}</Text>
                      <Text><strong>Status:</strong> {selectedWithdrawal.status}</Text>
                      {selectedWithdrawal.txHash && (
                        <Text><strong>Transaction Hash:</strong> {selectedWithdrawal.txHash}</Text>
                      )}
                    </Box>
                  </Box>

                  <Box flex="1">
                    {selectedWithdrawal.status === 'pending' && (
                      <>
                        <Text fontWeight="bold" mb={1}>Process Withdrawal</Text>
                        <Box bg={cardBgColor} p={4} borderRadius="md" mb={4} borderWidth="1px" borderColor={borderColor}>
                          <FormControl mb={4}>
                            <FormLabel>Transaction Hash</FormLabel>
                            <Input
                              placeholder="Enter blockchain transaction hash..."
                              value={txHash}
                              onChange={(e) => setTxHash(e.target.value)}
                              bg={cardBgColor}
                              borderColor={borderColor}
                            />
                          </FormControl>

                          <Text fontSize="sm" color={secondaryTextColor}>
                            Enter the transaction hash after sending funds to the user's wallet address.
                          </Text>
                        </Box>
                      </>
                    )}

                    <FormControl>
                      <FormLabel>Admin Notes</FormLabel>
                      <Textarea
                        placeholder="Add notes about this withdrawal..."
                        value={adminNotes}
                        onChange={(e) => setAdminNotes(e.target.value)}
                        bg={cardBgColor}
                        borderColor={borderColor}
                        rows={4}
                      />
                    </FormControl>
                  </Box>
                </Flex>
              </Box>
            )}
          </ModalBody>

          <ModalFooter>
            {selectedWithdrawal?.status === 'pending' && (
              <>
                <Button
                  colorScheme="green"
                  mr={3}
                  onClick={handleApprove}
                  isLoading={isSubmitting}
                >
                  Approve Withdrawal
                </Button>
                <Button
                  colorScheme="red"
                  mr={3}
                  onClick={handleReject}
                  isLoading={isSubmitting}
                >
                  Reject Withdrawal
                </Button>
              </>
            )}
            <Button variant="ghost" onClick={onClose}>Close</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default AdminWithdrawals;
