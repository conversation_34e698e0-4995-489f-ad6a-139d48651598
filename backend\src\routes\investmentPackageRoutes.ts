import express from 'express';
import {
  createInvestmentPackage,
  getInvestmentPackages,
  getEarningsSummary,
  requestWithdrawal,
  createInvestmentFromDeposit,
  getComprehensiveInvestmentData,
  getInterestDistributions,
  createInvestmentFromTransaction,
  getInvestmentBalances,
  getInvestmentBalanceByCurrency,
  checkWithdrawalEligibility
} from '../controllers/investmentPackageController';
import { protect } from '../middleware/authMiddleware';
import { validateInvestmentPackage, validateWithdrawal } from '../middleware/validationMiddleware';

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

/**
 * @route   POST /api/investments/create
 * @desc    Create a new investment package
 * @access  Private
 * @body    { amount: number, currency?: string, compoundEnabled?: boolean }
 */
router.post('/create', ...validateInvestmentPackage, createInvestmentPackage);

/**
 * @route   GET /api/investments/packages
 * @desc    Get user's investment packages
 * @access  Private
 * @query   { page?: number, limit?: number, status?: string, currency?: string }
 */
router.get('/packages', getInvestmentPackages);

/**
 * @route   GET /api/investments/earnings
 * @desc    Get earnings summary and statistics
 * @access  Private
 */
router.get('/earnings', getEarningsSummary);

/**
 * @route   POST /api/investments/withdraw
 * @desc    Request withdrawal of earnings
 * @access  Private
 * @body    { amount: number, currency?: string, emergency?: boolean }
 */
router.post('/withdraw', ...validateWithdrawal, requestWithdrawal);

/**
 * @route   POST /api/investments/create-from-deposit
 * @desc    Create investment package from confirmed deposit
 * @access  Private
 * @body    { depositTransactionId: string }
 */
router.post('/create-from-deposit', createInvestmentFromDeposit);

/**
 * @route   GET /api/investments/comprehensive
 * @desc    Get comprehensive investment data including packages and distributions
 * @access  Private
 */
router.get('/comprehensive', getComprehensiveInvestmentData);

/**
 * @route   GET /api/investments/distributions
 * @desc    Get interest distribution history
 * @access  Private
 * @query   { page?: number, limit?: number, packageId?: string }
 */
router.get('/distributions', getInterestDistributions);

/**
 * @route   POST /api/investments/create-from-transaction
 * @desc    Create investment package from completed transaction (automatic)
 * @access  Private
 * @body    { transactionId: string }
 */
router.post('/create-from-transaction', createInvestmentFromTransaction);

/**
 * @route   GET /api/investments/balances
 * @desc    Get investment balances for all currencies
 * @access  Private
 */
router.get('/balances', getInvestmentBalances);

/**
 * @route   GET /api/investments/balances/:currency
 * @desc    Get investment balance for specific currency
 * @access  Private
 */
router.get('/balances/:currency', getInvestmentBalanceByCurrency);

/**
 * @route   POST /api/investments/withdrawal-eligibility
 * @desc    Check withdrawal eligibility for amount and currency
 * @access  Private
 */
router.post('/withdrawal-eligibility', checkWithdrawalEligibility);

export default router;
