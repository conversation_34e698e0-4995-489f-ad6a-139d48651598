<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Investment Test Data Creator</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0B0E11 0%, #1E2026 100%);
            color: #FFFFFF;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(30, 32, 38, 0.8);
            border-radius: 16px;
            padding: 30px;
            border: 1px solid #2B3139;
            backdrop-filter: blur(10px);
        }

        h1 {
            color: #FCD535;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 0 2px 4px rgba(252, 213, 53, 0.3);
        }

        .description {
            background: rgba(11, 14, 17, 0.6);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 30px;
            border-left: 4px solid #FCD535;
        }

        .button {
            background: linear-gradient(135deg, #FCD535 0%, #F0B90B 100%);
            color: #0B0E11;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: block;
            margin: 20px auto;
            min-width: 200px;
        }

        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(252, 213, 53, 0.3);
        }

        .button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .log-container {
            background: #0B0E11;
            border: 1px solid #2B3139;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 4px 0;
        }

        .log-success { color: #02C076; }
        .log-error { color: #F84960; }
        .log-warning { color: #FCD535; }
        .log-info { color: #848E9C; }

        .status {
            text-align: center;
            padding: 10px;
            border-radius: 8px;
            margin: 20px 0;
            font-weight: bold;
        }

        .status.success {
            background: rgba(2, 192, 118, 0.2);
            color: #02C076;
            border: 1px solid #02C076;
        }

        .status.error {
            background: rgba(248, 73, 96, 0.2);
            color: #F84960;
            border: 1px solid #F84960;
        }

        .status.loading {
            background: rgba(252, 213, 53, 0.2);
            color: #FCD535;
            border: 1px solid #FCD535;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .feature {
            background: rgba(11, 14, 17, 0.4);
            padding: 20px;
            border-radius: 12px;
            border: 1px solid #2B3139;
        }

        .feature h3 {
            color: #FCD535;
            margin-top: 0;
        }

        .feature ul {
            margin: 0;
            padding-left: 20px;
        }

        .feature li {
            margin-bottom: 8px;
            color: #CCCCCC;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Investment Test Data Creator</h1>
        
        <div class="description">
            <h3>📋 What this tool does:</h3>
            <p>This tool creates comprehensive test data for the Investment Summary by Currency screen. It will:</p>
            <ul>
                <li>Create a test user account (if it doesn't exist)</li>
                <li>Generate realistic investment packages across multiple cryptocurrencies</li>
                <li>Simulate different investment statuses and time periods</li>
                <li>Calculate realistic earnings based on daily interest rates</li>
            </ul>
        </div>

        <div class="features">
            <div class="feature">
                <h3>💰 Currencies Included</h3>
                <ul>
                    <li>Bitcoin (BTC)</li>
                    <li>Ethereum (ETH)</li>
                    <li>Tether (USDT)</li>
                    <li>Binance Coin (BNB)</li>
                    <li>Solana (SOL)</li>
                    <li>Tron (TRX)</li>
                    <li>Dogecoin (DOGE)</li>
                </ul>
            </div>
            
            <div class="feature">
                <h3>📊 Investment Statuses</h3>
                <ul>
                    <li>Active investments</li>
                    <li>Pending investments</li>
                    <li>Completed investments</li>
                    <li>Withdrawn investments</li>
                </ul>
            </div>
            
            <div class="feature">
                <h3>🎯 Test Scenarios</h3>
                <ul>
                    <li>Various investment amounts</li>
                    <li>Different time periods</li>
                    <li>Realistic earnings calculations</li>
                    <li>Multiple packages per currency</li>
                </ul>
            </div>
        </div>

        <button id="createDataBtn" class="button" onclick="createTestData()">
            Create Test Data
        </button>

        <div id="status" class="status" style="display: none;"></div>

        <div class="log-container">
            <div id="logs"></div>
        </div>

        <div class="description" style="margin-top: 30px;">
            <h3>🔑 Test User Credentials:</h3>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Password:</strong> TestPassword123!</p>
            <p>Use these credentials to login and view the generated test data.</p>
        </div>
    </div>

    <script src="createInvestmentTestData.js"></script>
    <script>
        let isCreating = false;

        function log(message, type = 'info') {
            const logsContainer = document.getElementById('logs');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }

        function setStatus(message, type) {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
            statusElement.style.display = 'block';
        }

        function setButtonState(disabled, text) {
            const button = document.getElementById('createDataBtn');
            button.disabled = disabled;
            button.textContent = text;
            isCreating = disabled;
        }

        async function createTestData() {
            if (isCreating) return;

            try {
                setButtonState(true, 'Creating Test Data...');
                setStatus('Creating investment test data...', 'loading');
                
                // Clear previous logs
                document.getElementById('logs').innerHTML = '';
                
                log('Starting investment test data creation...', 'info');
                
                const creator = new InvestmentTestDataCreator();
                
                // Override console methods to capture logs
                const originalLog = console.log;
                const originalError = console.error;
                const originalWarn = console.warn;
                
                console.log = (...args) => {
                    log(args.join(' '), 'success');
                    originalLog(...args);
                };
                
                console.error = (...args) => {
                    log(args.join(' '), 'error');
                    originalError(...args);
                };
                
                console.warn = (...args) => {
                    log(args.join(' '), 'warning');
                    originalWarn(...args);
                };
                
                await creator.run();
                
                // Restore console methods
                console.log = originalLog;
                console.error = originalError;
                console.warn = originalWarn;
                
                setStatus('✅ Test data created successfully!', 'success');
                log('Test data creation completed successfully!', 'success');
                
            } catch (error) {
                setStatus('❌ Error creating test data', 'error');
                log(`Error: ${error.message}`, 'error');
                console.error('Test data creation failed:', error);
            } finally {
                setButtonState(false, 'Create Test Data');
            }
        }

        // Initial log
        log('Ready to create investment test data. Click the button to start.', 'info');
    </script>
</body>
</html>
