/**
 * Centralized utility file for cryptocurrency icons, names, and colors
 */
import {
  FaBitcoin,
  FaEthereum,
  FaCoins,
} from 'react-icons/fa';
import { 
  SiTether, 
  SiRipple, 
  SiDogecoin, 
  SiBinance,
  SiLitecoin,
  SiCardano,
  SiPolkadot,
  SiChainlink,
  SiStellar,
  SiMonero
} from 'react-icons/si';

// Define crypto icons mapping
export const cryptoIcons: Record<string, any> = {
  BTC: FaBitcoin,
  ETH: FaEthereum,
  USDT: SiTether,
  XRP: SiRipple,
  DOGE: SiDogecoin,
  BNB: SiBinance,
  LTC: SiLitecoin,
  ADA: SiCardano,
  DOT: SiPolkadot,
  LINK: SiChainlink,
  XLM: SiStellar,
  XMR: SiMonero,
  // Add more cryptocurrencies as needed
};

// Define crypto names mapping
export const cryptoNames: Record<string, string> = {
  BTC: 'Bitcoin',
  ETH: 'Ethereum',
  USDT: 'Tether',
  XRP: 'Ripple',
  DOGE: 'Dog<PERSON>oin',
  BNB: 'Binance Coin',
  LTC: 'Litecoin',
  ADA: 'Cardano',
  DOT: 'Polkadot',
  LINK: 'Chainlink',
  XLM: 'Stellar',
  XMR: 'Monero',
  // Add more cryptocurrencies as needed
};

// Define crypto colors mapping
export const cryptoColors: Record<string, string> = {
  BTC: '#F7931A',
  ETH: '#627EEA',
  USDT: '#26A17B',
  XRP: '#23292F',
  DOGE: '#C2A633',
  BNB: '#F0B90B',
  LTC: '#345D9D',
  ADA: '#0033AD',
  DOT: '#E6007A',
  LINK: '#2A5ADA',
  XLM: '#14B6E7',
  XMR: '#FF6600',
  // Add more cryptocurrencies as needed
};

/**
 * Get the icon component for a cryptocurrency
 * @param currency Cryptocurrency code (e.g., BTC, ETH)
 * @returns Icon component or default FaCoins if not found
 */
export const getCryptoIcon = (currency: string) => {
  return cryptoIcons[currency] || FaCoins;
};

/**
 * Get the name for a cryptocurrency
 * @param currency Cryptocurrency code (e.g., BTC, ETH)
 * @returns Name of the cryptocurrency or the code if not found
 */
export const getCryptoName = (currency: string) => {
  return cryptoNames[currency] || currency;
};

/**
 * Get the brand color for a cryptocurrency
 * @param currency Cryptocurrency code (e.g., BTC, ETH)
 * @param defaultColor Default color to return if not found
 * @returns Hex color code for the cryptocurrency
 */
export const getCryptoColor = (currency: string, defaultColor = '#F0B90B') => {
  return cryptoColors[currency] || defaultColor;
};
