import React from 'react';
import {
  Box,
  Flex,
  Text,
  Icon,
  Badge,
  HStack,
  VStack,
  Progress,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Divider,
  Button,
  Tooltip,
  useColorModeValue,
} from '@chakra-ui/react';
import {
  FaCalendarAlt,
  FaCoins,
  FaChartLine,
  FaInfoCircle,
  FaExchangeAlt,
  FaArrowUp,
  FaArrowDown,
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { cryptoIcons, cryptoNames, cryptoColors, getCryptoIcon, getCryptoName, getCryptoColor } from '../utils/cryptoIcons';
import { formatCurrency as formatCurrencyWithSymbol } from '../utils/formatters';

interface Investment {
  _id: string;
  userId: string;
  currency: string;
  amount: number;
  description?: string;
  status: 'pending' | 'processing' | 'approved' | 'rejected';
  receiptUrl?: string;
  cryptoAddress: string;
  txHash?: string;
  network?: string;
  adminNotes?: string;
  approvedAt?: string;
  rejectedAt?: string;
  rejectionReason?: string;
  createdAt: string;
  updatedAt: string;
}

interface GroupedInvestment {
  currency: string;
  totalAmount: number;
  investments: Investment[];
  firstInvestmentDate: string;
  status: 'pending' | 'processing' | 'approved' | 'rejected' | 'mixed';
  networks: string[];
  addresses: string[];
}

interface InvestmentCardProps {
  groupedInvestment: GroupedInvestment;
  totalEarned: number;
  onViewDetails?: (currency: string) => void;
}

const InvestmentCard: React.FC<InvestmentCardProps> = ({
  groupedInvestment,
  totalEarned,
  onViewDetails,
}) => {
  const { t } = useTranslation();

  // Colors
  const bgColor = "#1E2329";
  const cardBgColor = "#0B0E11";
  const borderColor = "#2B3139";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const primaryColor = "#F0B90B";

  // Calculate investment metrics
  const createdDate = new Date(groupedInvestment.firstInvestmentDate);
  const currentDate = new Date();
  const diffTime = Math.abs(currentDate.getTime() - createdDate.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  // Calculate earnings (1% daily)
  const dailyEarning = groupedInvestment.totalAmount * 0.01;

  // Calculate total earnings for this currency using reduce instead of forEach
  const totalEarningForThisCurrency = groupedInvestment.investments.reduce((total, investment) => {
    // Chỉ tính lợi nhuận cho các khoản đầu tư đã được approve
    if (investment.status === 'approved') {
      // Sử dụng ngày approve nếu có, nếu không thì sử dụng ngày tạo
      const investmentDate = investment.approvedAt ? new Date(investment.approvedAt) : new Date(investment.createdAt);
      const investmentDiffTime = Math.abs(currentDate.getTime() - investmentDate.getTime());
      const investmentDiffDays = Math.ceil(investmentDiffTime / (1000 * 60 * 60 * 24));
      return total + (investment.amount * 0.01 * investmentDiffDays);
    }
    return total;
  }, 0);

  const earningPercentage = groupedInvestment.totalAmount > 0
    ? (totalEarningForThisCurrency / groupedInvestment.totalAmount) * 100
    : 0;

  // Format currency with crypto symbol
  const formatCurrency = (amount: number) => {
    return formatCurrencyWithSymbol(amount, groupedInvestment.currency);
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'green';
      case 'pending':
        return 'yellow';
      case 'processing':
        return 'blue';
      case 'rejected':
        return 'red';
      case 'mixed':
        return 'purple';
      default:
        return 'gray';
    }
  };

  // Get status display text
  const getStatusText = (status: string) => {
    if (status === 'mixed') {
      return t('investment.mixedStatus', 'Mixed Status');
    }
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  // Get crypto icon
  const CryptoIcon = getCryptoIcon(groupedInvestment.currency);

  // Get crypto color
  const cryptoColor = getCryptoColor(groupedInvestment.currency, primaryColor);

  // Get crypto name
  const cryptoName = getCryptoName(groupedInvestment.currency);

  // Count investments
  const investmentCount = groupedInvestment.investments.length;

  return (
    <Box
      bg={bgColor}
      borderRadius="xl"
      borderWidth="1px"
      borderColor={borderColor}
      overflow="hidden"
      position="relative"
      transition="all 0.3s"
      _hover={{
        transform: 'translateY(-2px)',
        boxShadow: `0 8px 16px rgba(0, 0, 0, 0.2), 0 0 0 1px ${cryptoColor}40`,
        borderColor: `${cryptoColor}80`,
      }}
    >
      {/* Top colored border based on crypto */}
      <Box
        h="4px"
        bg={cryptoColor}
        position="absolute"
        top={0}
        left={0}
        right={0}
      />

      {/* Card Header */}
      <Flex
        p={5}
        align="center"
        borderBottomWidth="1px"
        borderBottomColor={borderColor}
      >
        <Flex
          bg={`${cryptoColor}20`}
          p={3}
          borderRadius="full"
          mr={4}
          align="center"
          justify="center"
        >
          <Icon as={CryptoIcon} color={cryptoColor} boxSize={6} />
        </Flex>
        <Box flex={1}>
          <HStack mb={1}>
            <Text color={textColor} fontWeight="bold" fontSize="lg">
              {groupedInvestment.currency}
            </Text>
            <Text color={secondaryTextColor} fontSize="md">
              {cryptoName}
            </Text>
            <Badge colorScheme="blue" variant="outline" fontSize="xs">
              {investmentCount} {t('investment.investments', 'investments')}
            </Badge>
          </HStack>
          <Badge colorScheme={getStatusColor(groupedInvestment.status)} borderRadius="md" px={2}>
            {getStatusText(groupedInvestment.status)}
          </Badge>
        </Box>
        <Box textAlign="right">
          <Text color={secondaryTextColor} fontSize="sm">
            {t('investment.totalInvested', 'Total Invested')}
          </Text>
          <Text color={textColor} fontWeight="bold" fontSize="xl">
            {formatCurrency(groupedInvestment.totalAmount)}
          </Text>
        </Box>
      </Flex>

      {/* Card Body */}
      <Box p={5}>
        {/* Investment Stats */}
        <Flex
          direction={{ base: 'column', sm: 'row' }}
          justify="space-between"
          mb={4}
          gap={4}
        >
          {/* Earnings */}
          <Stat>
            <StatLabel color={secondaryTextColor}>
              {t('investment.totalEarned', 'Total Earned')}
            </StatLabel>
            <StatNumber color={textColor}>
              {formatCurrency(totalEarningForThisCurrency)}
            </StatNumber>
            <StatHelpText color="green.400">
              <StatArrow type="increase" />
              {earningPercentage.toFixed(2)}% {t('investment.return', 'return')}
            </StatHelpText>
          </Stat>

          {/* Daily Earnings */}
          <Stat>
            <StatLabel color={secondaryTextColor}>
              {t('investment.dailyEarnings', 'Daily Earnings')}
            </StatLabel>
            <StatNumber color={textColor}>
              {formatCurrency(dailyEarning)}
            </StatNumber>
            <StatHelpText color="green.400">
              <StatArrow type="increase" />
              1% {t('investment.daily', 'daily')}
            </StatHelpText>
          </Stat>
        </Flex>

        {/* Investment Timeline */}
        <Box mb={4}>
          <Flex justify="space-between" align="center" mb={2}>
            <Text color={secondaryTextColor} fontSize="sm">
              {t('investment.investmentAge', 'Investment Age')}
            </Text>
            <Text color={textColor} fontWeight="medium">
              {diffDays} {t('investment.days', 'days')}
            </Text>
          </Flex>
          <Progress
            value={Math.min(diffDays, 100)}
            max={100}
            colorScheme="green"
            borderRadius="full"
            size="sm"
            bg={`${borderColor}80`}
          />
        </Box>

        {/* Investment Details */}
        <VStack spacing={2} align="stretch" mb={4}>
          <Flex justify="space-between">
            <Text color={secondaryTextColor} fontSize="sm">
              {t('investment.firstInvestmentDate', 'First Investment Date')}
            </Text>
            <Text color={textColor} fontSize="sm">
              {formatDate(groupedInvestment.firstInvestmentDate)}
            </Text>
          </Flex>

          <Flex justify="space-between">
            <Text color={secondaryTextColor} fontSize="sm">
              {t('investment.networks', 'Networks')}
            </Text>
            <Text color={textColor} fontSize="sm">
              {groupedInvestment.networks.length > 0
                ? groupedInvestment.networks.join(', ')
                : 'Default'}
            </Text>
          </Flex>

          <Flex justify="space-between">
            <Text color={secondaryTextColor} fontSize="sm">
              {t('investment.addressCount', 'Addresses')}
            </Text>
            <HStack>
              <Badge colorScheme="blue" borderRadius="full">
                {groupedInvestment.addresses.length}
              </Badge>
              <Text color={textColor} fontSize="sm">
                {t('investment.uniqueAddresses', 'unique addresses')}
              </Text>
            </HStack>
          </Flex>

          <Flex justify="space-between">
            <Text color={secondaryTextColor} fontSize="sm">
              {t('investment.investmentCount', 'Investment Count')}
            </Text>
            <Text color={textColor} fontSize="sm">
              {investmentCount} {t('investment.transactions', 'transactions')}
            </Text>
          </Flex>
        </VStack>

        {/* Action Buttons */}
        <Flex justify="space-between" gap={2}>
          <Button
            leftIcon={<Icon as={FaInfoCircle} />}
            variant="outline"
            size="sm"
            borderColor={borderColor}
            color={textColor}
            _hover={{ bg: `${cryptoColor}20`, borderColor: cryptoColor }}
            onClick={() => onViewDetails && onViewDetails(groupedInvestment.currency)}
            flex={1}
          >
            {t('investment.viewAll', 'View All')}
          </Button>

          <Button
            leftIcon={<Icon as={FaExchangeAlt} />}
            variant="solid"
            size="sm"
            bg={cryptoColor}
            color="white"
            _hover={{ bg: `${cryptoColor}D0` }}
            flex={1}
          >
            {t('investment.reinvest', 'Reinvest')}
          </Button>
        </Flex>
      </Box>
    </Box>
  );
};

export default InvestmentCard;
