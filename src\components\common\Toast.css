.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 350px;
}

.toast {
  background-color: #1E2329;
  color: #EAECEF;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  transform: translateX(120%);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.toast.visible {
  transform: translateX(0);
  opacity: 1;
}

.toast-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
}

.toast-icon {
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toast-message {
  flex: 1;
  font-size: 14px;
  line-height: 1.5;
}

.toast-close {
  background: none;
  border: none;
  color: #848E9C;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.toast-close:hover {
  background-color: rgba(132, 142, 156, 0.1);
}

.toast-progress {
  height: 4px;
  background-color: rgba(255, 255, 255, 0.2);
  transition: width 0.1s linear;
}

/* Toast types */
.toast-info {
  border-left: 4px solid #1E88E5;
}

.toast-info .toast-icon {
  color: #1E88E5;
}

.toast-success {
  border-left: 4px solid #0ECB81;
}

.toast-success .toast-icon {
  color: #0ECB81;
}

.toast-warning {
  border-left: 4px solid #F0B90B;
}

.toast-warning .toast-icon {
  color: #F0B90B;
}

.toast-error {
  border-left: 4px solid #F6465D;
}

.toast-error .toast-icon {
  color: #F6465D;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .toast {
    background-color: #0B0E11;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .toast-container {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
}
