

// Motion components
const MotionCard = motion(Card);

const Dashboard = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const toast = useOptimizedToast();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const {
    isOpen: isCryptoDepositOpen,
    onOpen: onCryptoDepositOpen,
    onClose: onCryptoDepositClose
  } = useDisclosure();

  // Investment packages state
  const [earningsData, setEarningsData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  // Colors
  const bgColor = "#0B0E11";
  const cardBgColor = "#1E2329";
  const primaryColor = "#F0B90B";
  const textColor = "#EAECEF";
  const secondaryTextColor = "#848E9C";
  const borderColor = "#2B3139";

  // Enhanced sample data
  const accountBalance = 12458.32;
  const totalEarnings = 1245.83;
  const pendingDeposits = 500;
  const activeInvestments = 10000;
  const dailyInterestEarned = 124.58;
  const totalCryptoBalance = 15234.67;

  // Sample crypto balances - memoized to prevent re-renders
  const cryptoBalances = useMemo(() => [
    { symbol: 'BTC', balance: 0.25, usdtValue: 6250.00, dailyInterest: 62.50 },
    { symbol: 'ETH', balance: 2.5, usdtValue: 4000.00, dailyInterest: 40.00 },
    { symbol: 'USDT', balance: 3000, usdtValue: 3000.00, dailyInterest: 30.00 },
    { symbol: 'BNB', balance: 10, usdtValue: 2400.00, dailyInterest: 24.00 },
    { symbol: 'SOL', balance: 50, usdtValue: 1500.00, dailyInterest: 15.00 }
  ], []);

  // Sample investment packages - memoized to prevent re-renders
  const samplePackages = useMemo(() => [
    {
      id: '1',
      currency: 'BTC',
      amount: 0.1,
      totalEarned: 0.005,
      dailyInterest: 0.001,
      status: 'active' as const,
      activatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
      nextInterestTime: new Date(Date.now() + 8 * 60 * 60 * 1000),
      withdrawalEligibleTime: new Date(Date.now() - 24 * 60 * 60 * 1000),
      minimumWithdrawalUSDT: 50,
      realTimeUSDTValue: 125.50,
      activeDays: 5,
      canWithdraw: true,
      interestRate: 0.01
    },
    {
      id: '2',
      currency: 'ETH',
      amount: 1.5,
      totalEarned: 0.045,
      dailyInterest: 0.015,
      status: 'active' as const,
      activatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      nextInterestTime: new Date(Date.now() + 8 * 60 * 60 * 1000),
      withdrawalEligibleTime: new Date(Date.now() - 24 * 60 * 60 * 1000),
      minimumWithdrawalUSDT: 50,
      realTimeUSDTValue: 72.00,
      activeDays: 3,
      canWithdraw: true,
      interestRate: 0.01
    }
  ], []);

  const recentTransactions = [
    { id: 1, type: 'deposit', amount: 1000, currency: 'USDT', status: 'completed', date: '2023-05-15' },
    { id: 2, type: 'earning', amount: 10, currency: 'USDT', status: 'completed', date: '2023-05-15' },
    { id: 3, type: 'earning', amount: 10, currency: 'USDT', status: 'completed', date: '2023-05-14' },
    { id: 4, type: 'deposit', amount: 500, currency: 'USDT', status: 'pending', date: '2023-05-14' },
    { id: 5, type: 'withdrawal', amount: 200, currency: 'USDT', status: 'completed', date: '2023-05-13' }
  ];

  // Investment packages handlers
  const handleRefresh = useCallback(() => {
    setRefreshKey(prev => prev + 1);
    toast({
      title: "Veriler Yenilendi",
      description: "Yatırım paketleri başarıyla güncellendi",
      status: "success",
      duration: 2000,
      isClosable: true,
    });
  }, [toast]);

  const handleCreateSuccess = () => {
    handleRefresh();
    onClose();
  };

  return (
    <ErrorBoundaryWrapper>
      <Box bg={bgColor} minH="100vh" py={8}>
        <Container maxW="container.xl">
          <VStack spacing={8} align="stretch">
          {/* Header */}
          <Flex justify="space-between" align="center">
            <Heading color={textColor} size="lg">{t('dashboard.title', 'Dashboard')}</Heading>
            <Button
              leftIcon={<FaDownload />}
              colorScheme="yellow"
              variant="outline"
              size="sm"
            >
              {t('dashboard.exportData', 'Export Data')}
            </Button>
          </Flex>

          {/* Investment Dashboard */}
          <InvestmentDashboard />

          {/* Stats Overview */}
          <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)", lg: "repeat(4, 1fr)" }} gap={6}>
            <GridItem>
              <Stat
                bg={cardBgColor}
                p={6}
                borderRadius="lg"
                borderWidth="1px"
                borderColor={borderColor}
              >
                <Flex justify="space-between">
                  <Box>
                    <StatLabel color={secondaryTextColor}>Total Crypto Balance</StatLabel>
                    <StatNumber color={textColor} fontSize="2xl">${totalCryptoBalance.toFixed(2)}</StatNumber>
                    <StatHelpText color={primaryColor}>
                      <StatArrow type="increase" />
                      5 Cryptocurrencies
                    </StatHelpText>
                  </Box>
                  <Flex
                    bg={`${primaryColor}20`}
                    p={3}
                    borderRadius="full"
                    alignItems="center"
                    justifyContent="center"
                    height="50px"
                    width="50px"
                  >
                    <Icon as={FaWallet} color={primaryColor} boxSize={5} />
                  </Flex>
                </Flex>
              </Stat>
            </GridItem>

            <GridItem>
              <Stat
                bg={cardBgColor}
                p={6}
                borderRadius="lg"
                borderWidth="1px"
                borderColor={borderColor}
              >
                <Flex justify="space-between">
                  <Box>
                    <StatLabel color={secondaryTextColor}>Daily Interest Earned</StatLabel>
                    <StatNumber color="#0ECB81" fontSize="2xl">${dailyInterestEarned.toFixed(2)}</StatNumber>
                    <StatHelpText color="#0ECB81">
                      <StatArrow type="increase" />
                      1% Daily Rate
                    </StatHelpText>
                  </Box>
                  <Flex
                    bg="#0ECB8120"
                    p={3}
                    borderRadius="full"
                    alignItems="center"
                    justifyContent="center"
                    height="50px"
                    width="50px"
                  >
                    <Icon as={FaArrowUp} color="#0ECB81" boxSize={5} />
                  </Flex>
                </Flex>
              </Stat>
            </GridItem>

            <GridItem>
              <Stat
                bg={cardBgColor}
                p={6}
                borderRadius="lg"
                borderWidth="1px"
                borderColor={borderColor}
              >
                <Flex justify="space-between">
                  <Box>
                    <StatLabel color={secondaryTextColor}>{t('dashboard.stats.pendingDeposits', 'Pending Deposits')}</StatLabel>
                    <StatNumber color={textColor} fontSize="2xl">${pendingDeposits.toFixed(2)}</StatNumber>
                    <StatHelpText color={secondaryTextColor}>
                      {t('dashboard.stats.processingTime', 'Processing Time')}: ~24h
                    </StatHelpText>
                  </Box>
                  <Flex
                    bg={`${primaryColor}20`}
                    p={3}
                    borderRadius="full"
                    alignItems="center"
                    justifyContent="center"
                    height="50px"
                    width="50px"
                  >
                    <Icon as={FaMoneyBillWave} color={primaryColor} boxSize={5} />
                  </Flex>
                </Flex>
              </Stat>
            </GridItem>

            <GridItem>
              <Stat
                bg={cardBgColor}
                p={6}
                borderRadius="lg"
                borderWidth="1px"
                borderColor={borderColor}
              >
                <Flex justify="space-between">
                  <Box>
                    <StatLabel color={secondaryTextColor}>{t('dashboard.stats.activeInvestments', 'Active Investments')}</StatLabel>
                    <StatNumber color={textColor} fontSize="2xl">${activeInvestments.toFixed(2)}</StatNumber>
                    <StatHelpText color={primaryColor}>
                      <StatArrow type="increase" />
                      {t('dashboard.stats.dailyProfit', 'Daily Profit')}: $100.00
                    </StatHelpText>
                  </Box>
                  <Flex
                    bg={`${primaryColor}20`}
                    p={3}
                    borderRadius="full"
                    alignItems="center"
                    justifyContent="center"
                    height="50px"
                    width="50px"
                  >
                    <Icon as={FaExchangeAlt} color={primaryColor} boxSize={5} />
                  </Flex>
                </Flex>
              </Stat>
            </GridItem>
          </Grid>

          {/* Enhanced Crypto Section - Only for authenticated users */}
          {user && (
            <Box>
              <Flex justify="space-between" align="center" mb={6}>
                <Heading size="md" color={textColor}>
                  🚀 Crypto Investment Platform
                </Heading>
                <Button
                  leftIcon={<FaCoins />}
                  bg={primaryColor}
                  color="#0B0E11"
                  _hover={{ bg: "#F8D12F" }}
                  onClick={onCryptoDepositOpen}
                  size="sm"
                >
                  Quick Deposit
                </Button>
              </Flex>

              {/* Crypto Balances Overview */}
              <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)", lg: "repeat(5, 1fr)" }} gap={4} mb={6}>
                {cryptoBalances.map((crypto, index) => (
                  <MotionCard
                    key={crypto.symbol}
                    bg={cardBgColor}
                    borderColor={borderColor}
                    borderWidth="1px"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    _hover={{
                      transform: 'translateY(-2px)',
                      borderColor: primaryColor
                    }}
                  >
                    <CardBody p={4}>
                      <VStack spacing={2}>
                        <Text fontWeight="bold" color={primaryColor} fontSize="lg">
                          {crypto.symbol}
                        </Text>
                        <Text color={textColor} fontSize="sm" textAlign="center">
                          {crypto.balance} {crypto.symbol}
                        </Text>
                        <Text color="#0ECB81" fontSize="xs" fontWeight="bold">
                          ${crypto.usdtValue.toFixed(2)}
                        </Text>
                        <Badge colorScheme="green" variant="subtle" fontSize="xs">
                          +${crypto.dailyInterest.toFixed(2)}/day
                        </Badge>
                      </VStack>
                    </CardBody>
                  </MotionCard>
                ))}
              </Grid>

              {/* Crypto Wallets Grid */}
              <MotionCard
                bg={cardBgColor}
                borderColor={borderColor}
                borderWidth="1px"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <CardBody>
                  <CryptoWalletsGrid onRefresh={handleRefresh} />
                </CardBody>
              </MotionCard>
            </Box>
          )}

          {/* Recent Transactions */}
          <Box>
            <Flex justify="space-between" align="center" mb={4}>
              <Heading size="md" color={textColor}>{t('dashboard.recentTransactions.title', 'Recent Transactions')}</Heading>
              <Icon as={FaHistory} color={primaryColor} boxSize={5} />
            </Flex>

            <SimpleTransactionHistory limit={5} />

            <Button
              variant="ghost"
              colorScheme="yellow"
              size="sm"
              mt={4}
              alignSelf="flex-end"
            >
              {t('dashboard.recentTransactions.viewAll', 'View All Transactions')}
            </Button>
          </Box>

          {/* Investment Packages Section - Only for authenticated users */}
          {user && (
            <Box>
              <Flex justify="space-between" align="center" mb={6}>
                <Heading size="md" color={textColor}>
                  💰 Yatırım Paketlerim
                </Heading>
                <Button
                  leftIcon={<FaPlus />}
                  bg={primaryColor}
                  color="#0B0E11"
                  _hover={{ bg: "#F8D12F" }}
                  onClick={onOpen}
                  size="sm"
                >
                  Yeni Yatırım
                </Button>
              </Flex>

              {/* Time Lock Status */}
              <MotionCard
                bg={cardBgColor}
                borderColor={borderColor}
                borderWidth="1px"
                mb={6}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <CardBody>
                  <TimeLockStatus />
                </CardBody>
              </MotionCard>

              {/* Earnings Summary Cards */}
              <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)", lg: "repeat(4, 1fr)" }} gap={4} mb={6}>
                <MotionCard
                  bg={cardBgColor}
                  borderColor={borderColor}
                  borderWidth="1px"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                >
                  <CardBody>
                    <Stat>
                      <StatLabel color={secondaryTextColor}>Toplam Kazanç</StatLabel>
                      <StatNumber color={textColor}>$125.50</StatNumber>
                      <StatHelpText color="green.400">
                        <StatArrow type="increase" />
                        Günlük: $50.25
                      </StatHelpText>
                    </Stat>
                  </CardBody>
                </MotionCard>

                <MotionCard
                  bg={cardBgColor}
                  borderColor={borderColor}
                  borderWidth="1px"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  <CardBody>
                    <Stat>
                      <StatLabel color={secondaryTextColor}>Toplam Yatırım</StatLabel>
                      <StatNumber color={textColor}>$5,000</StatNumber>
                      <StatHelpText color={primaryColor}>
                        3 Aktif Paket
                      </StatHelpText>
                    </Stat>
                  </CardBody>
                </MotionCard>

                <MotionCard
                  bg={cardBgColor}
                  borderColor={borderColor}
                  borderWidth="1px"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                >
                  <CardBody>
                    <Stat>
                      <StatLabel color={secondaryTextColor}>Çekim Durumu</StatLabel>
                      <StatNumber color="green.400">Uygun</StatNumber>
                      <StatHelpText color={secondaryTextColor}>
                        Min: 50 USDT
                      </StatHelpText>
                    </Stat>
                  </CardBody>
                </MotionCard>

                <MotionCard
                  bg={cardBgColor}
                  borderColor={borderColor}
                  borderWidth="1px"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  <CardBody>
                    <Stat>
                      <StatLabel color={secondaryTextColor}>Sonraki Hesaplama</StatLabel>
                      <StatNumber color={textColor} fontSize="lg">2s 38d 41s</StatNumber>
                      <StatHelpText color={secondaryTextColor}>
                        03:00 UTC+3
                      </StatHelpText>
                    </Stat>
                  </CardBody>
                </MotionCard>
              </Grid>

              {/* Earnings Chart and Investment Packages */}
              <Grid templateColumns={{ base: "1fr", lg: "1fr 1fr" }} gap={6} mb={6}>
                <MotionCard
                  bg={cardBgColor}
                  borderColor={borderColor}
                  borderWidth="1px"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                >
                  <CardBody>
                    <Heading size="md" color={textColor} mb={4}>
                      📈 Kazanç Grafiği
                    </Heading>
                    <EarningsChart />
                  </CardBody>
                </MotionCard>

                <MotionCard
                  bg={cardBgColor}
                  borderColor={borderColor}
                  borderWidth="1px"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                >
                  <CardBody>
                    <Heading size="md" color={textColor} mb={4}>
                      📦 Enhanced Investment Packages
                    </Heading>
                    <VStack spacing={4}>
                      {samplePackages.map((pkg, index) => (
                        <EnhancedInvestmentPackageCard
                          key={pkg.id}
                          package={pkg}
                          onWithdraw={(packageId) => {
                            toast({
                              title: 'Withdrawal Initiated',
                              description: `Withdrawal for package ${packageId} has been initiated`,
                              status: 'success',
                              duration: 3000,
                            });
                          }}
                          onViewDetails={(packageId) => {
                            toast({
                              title: 'Package Details',
                              description: `Viewing details for package ${packageId}`,
                              status: 'info',
                              duration: 2000,
                            });
                          }}
                        />
                      ))}
                    </VStack>
                  </CardBody>
                </MotionCard>
              </Grid>
            </Box>
          )}

          {/* Investment Summary */}
          <Grid templateColumns={{ base: "1fr", lg: "2fr 1fr" }} gap={6}>
            <GridItem>
              <Box
                bg={cardBgColor}
                p={6}
                borderRadius="lg"
                borderWidth="1px"
                borderColor={borderColor}
                h="full"
              >
                <Heading size="md" color={textColor} mb={4}>{t('dashboard.investmentSummary.title', 'Investment Summary')}</Heading>

                <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)" }} gap={6}>
                  <GridItem>
                    <VStack align="flex-start" spacing={4}>
                      <HStack justify="space-between" w="full">
                        <Text color={secondaryTextColor}>{t('dashboard.investmentSummary.totalInvested', 'Total Invested')}:</Text>
                        <Text color={textColor} fontWeight="bold">$10,000.00</Text>
                      </HStack>

                      <HStack justify="space-between" w="full">
                        <Text color={secondaryTextColor}>{t('dashboard.investmentSummary.activeInvestments', 'Active Investments')}:</Text>
                        <Text color={textColor} fontWeight="bold">$10,000.00</Text>
                      </HStack>

                      <HStack justify="space-between" w="full">
                        <Text color={secondaryTextColor}>{t('dashboard.investmentSummary.totalEarnings', 'Total Earnings')}:</Text>
                        <Text color="green.400" fontWeight="bold">$1,245.83</Text>
                      </HStack>
                    </VStack>
                  </GridItem>

                  <GridItem>
                    <VStack align="flex-start" spacing={4}>
                      <HStack justify="space-between" w="full">
                        <Text color={secondaryTextColor}>{t('dashboard.investmentSummary.dailyReturn', 'Daily Return')}:</Text>
                        <Text color={primaryColor} fontWeight="bold">1% ($100.00)</Text>
                      </HStack>

                      <HStack justify="space-between" w="full">
                        <Text color={secondaryTextColor}>{t('dashboard.investmentSummary.monthlyReturn', 'Monthly Return')}:</Text>
                        <Text color={primaryColor} fontWeight="bold">30% ($3,000.00)</Text>
                      </HStack>

                      <HStack justify="space-between" w="full">
                        <Text color={secondaryTextColor}>{t('dashboard.investmentSummary.yearlyReturn', 'Yearly Return')}:</Text>
                        <Text color={primaryColor} fontWeight="bold">365% ($36,500.00)</Text>
                      </HStack>
                    </VStack>
                  </GridItem>
                </Grid>

                <Button
                  colorScheme="yellow"
                  bg={primaryColor}
                  color="#0B0E11"
                  _hover={{ bg: "#F8D12F" }}
                  mt={6}
                  w="full"
                >
                  {t('dashboard.investmentSummary.newInvestment', 'Make New Investment')}
                </Button>
              </Box>
            </GridItem>

            <GridItem>
              <Box
                bg={cardBgColor}
                p={6}
                borderRadius="lg"
                borderWidth="1px"
                borderColor={borderColor}
                h="full"
              >
                <Heading size="md" color={textColor} mb={4}>{t('dashboard.referralStats.title', 'Referral Stats')}</Heading>

                <VStack align="flex-start" spacing={4}>
                  <HStack justify="space-between" w="full">
                    <Text color={secondaryTextColor}>{t('dashboard.referralStats.totalReferrals', 'Total Referrals')}:</Text>
                    <Text color={textColor} fontWeight="bold">12</Text>
                  </HStack>

                  <HStack justify="space-between" w="full">
                    <Text color={secondaryTextColor}>{t('dashboard.referralStats.activeReferrals', 'Active Referrals')}:</Text>
                    <Text color={textColor} fontWeight="bold">8</Text>
                  </HStack>

                  <HStack justify="space-between" w="full">
                    <Text color={secondaryTextColor}>{t('dashboard.referralStats.referralEarnings', 'Referral Earnings')}:</Text>
                    <Text color="green.400" fontWeight="bold">$245.83</Text>
                  </HStack>

                  <HStack justify="space-between" w="full">
                    <Text color={secondaryTextColor}>{t('dashboard.referralStats.referralLevel', 'Referral Level')}:</Text>
                    <Badge colorScheme="yellow" variant="solid" px={2} py={1}>
                      VIP 1
                    </Badge>
                  </HStack>

                  <HStack justify="space-between" w="full">
                    <Text color={secondaryTextColor}>{t('dashboard.referralStats.commissionRate', 'Commission Rate')}:</Text>
                    <Text color={primaryColor} fontWeight="bold">1%</Text>
                  </HStack>
                </VStack>

                <Button
                  variant="outline"
                  colorScheme="yellow"
                  mt={6}
                  w="full"
                >
                  {t('dashboard.referralStats.inviteFriends', 'Invite Friends')}
                </Button>
              </Box>
            </GridItem>
          </Grid>
        </VStack>
      </Container>

      {/* Create Investment Modal */}
      <CreateInvestmentModal
        isOpen={isOpen}
        onClose={onClose}
        onSuccess={handleCreateSuccess}
      />

      {/* Enhanced Crypto Deposit Modal */}
      <EnhancedCryptoDepositModal
        isOpen={isCryptoDepositOpen}
        onClose={onCryptoDepositClose}
        onDepositSuccess={() => {
          handleRefresh();
          toast({
            title: 'Deposit Successful',
            description: 'Your crypto deposit has been initiated successfully',
            status: 'success',
            duration: 5000,
          });
        }}
      />
        </VStack>
      </Container>
    </Box>
    </ErrorBoundaryWrapper>
  );
};

export default Dashboard;
