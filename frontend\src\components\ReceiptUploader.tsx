import { useState, useRef } from 'react';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Heading,
  Text,
  Flex,
  Image,
  Progress,
  useToast,
  VStack,
  HStack,
  IconButton,
  Badge,
  Container,
} from '@chakra-ui/react';
import { CloseIcon, CheckIcon, AttachmentIcon, CopyIcon } from '@chakra-ui/icons';
import useAuth from '../hooks/useAuth';

interface ReceiptData {
  transactionDate: string;
  amount: string;
  bitcoinAddress: string;
  description: string;
}

const ReceiptUploader = () => {
  const { user } = useAuth();
  const toast = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [files, setFiles] = useState<File[]>([]);
  const [previews, setPreviews] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [bitcoinAddress, setBitcoinAddress] = useState('******************************************');

  const [receiptData, setReceiptData] = useState<ReceiptData>({
    transactionDate: new Date().toISOString().split('T')[0],
    amount: '',
    bitcoinAddress: bitcoinAddress,
    description: '',
  });

  // Renk değişkenleri
  const bgColor = '#1E2329';
  const textColor = '#EAECEF';
  const borderColor = '#2B3139';
  const primaryColor = '#F0B90B';
  const inputBgColor = '#0B0E11';

  // Dosya seçme işlevi
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return;

    const selectedFiles = Array.from(e.target.files);

    // Maksimum 3 dosya kontrolü
    if (files.length + selectedFiles.length > 3) {
      toast({
        title: 'Maksimum 3 dosya yükleyebilirsiniz',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    const newFiles = [...files];
    const newPreviews = [...previews];

    selectedFiles.forEach(file => {
      // Dosya boyutu kontrolü (10MB)
      if (file.size > 10 * 1024 * 1024) {
        toast({
          title: 'Dosya boyutu çok büyük',
          description: `${file.name} 10MB'dan küçük olmalıdır`,
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      // Dosya türü kontrolü
      if (!['image/jpeg', 'image/png', 'application/pdf'].includes(file.type)) {
        toast({
          title: 'Desteklenmeyen dosya formatı',
          description: 'Lütfen JPG, PNG veya PDF dosyası yükleyin',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      newFiles.push(file);

      // PDF dışındaki dosyalar için önizleme oluştur
      if (file.type !== 'application/pdf') {
        const reader = new FileReader();
        reader.onload = () => {
          newPreviews.push(reader.result as string);
          setPreviews([...newPreviews]);
        };
        reader.readAsDataURL(file);
      } else {
        // PDF için standart bir ikon kullan
        newPreviews.push('/pdf-icon.png');
        setPreviews([...newPreviews]);
      }
    });

    setFiles(newFiles);

    // Input'u sıfırla (aynı dosyayı tekrar seçebilmek için)
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Dosya kaldırma işlevi
  const removeFile = (index: number) => {
    const newFiles = [...files];
    const newPreviews = [...previews];
    newFiles.splice(index, 1);
    newPreviews.splice(index, 1);
    setFiles(newFiles);
    setPreviews(newPreviews);
  };

  // Bitcoin adresini kopyalama
  const copyBitcoinAddress = () => {
    navigator.clipboard.writeText(bitcoinAddress);
    toast({
      title: 'Bitcoin adresi kopyalandı',
      status: 'success',
      duration: 2000,
      isClosable: true,
    });
  };

  // Form gönderme işlevi
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (files.length === 0) {
      toast({
        title: 'Lütfen en az bir dekont yükleyin',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    if (!receiptData.amount || !receiptData.transactionDate) {
      toast({
        title: 'Lütfen tüm zorunlu alanları doldurun',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    // Demo amaçlı simüle edilmiş yükleme
    const simulateUpload = () => {
      let progress = 0;
      const interval = setInterval(() => {
        progress += Math.floor(Math.random() * 10) + 5;
        if (progress > 100) progress = 100;
        setUploadProgress(progress);

        if (progress === 100) {
          clearInterval(interval);
          setTimeout(() => {
            setIsUploading(false);
            toast({
              title: 'Dekont başarıyla yüklendi',
              description: 'İnceleme sonrası bilgilendirileceksiniz',
              status: 'success',
              duration: 5000,
              isClosable: true,
            });

            // Formu sıfırla
            setFiles([]);
            setPreviews([]);
            setReceiptData({
              transactionDate: new Date().toISOString().split('T')[0],
              amount: '',
              bitcoinAddress: bitcoinAddress,
              description: '',
            });
          }, 500);
        }
      }, 300);
    };

    // Gerçek API entegrasyonu yerine simülasyon kullanıyoruz
    simulateUpload();

    // Gerçek API entegrasyonu şu şekilde olurdu:
    /*
    try {
      const formData = new FormData();
      files.forEach((file, index) => {
        formData.append(`receipt${index + 1}`, file);
      });

      formData.append('transactionDate', receiptData.transactionDate);
      formData.append('amount', receiptData.amount);
      formData.append('bitcoinAddress', receiptData.bitcoinAddress);
      formData.append('description', receiptData.description);
      formData.append('userId', user?.id || '');

      const response = await axios.post('/api/receipts/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 1));
          setUploadProgress(percentCompleted);
        },
      });

      if (response.data.success) {
        toast({
          title: 'Dekont başarıyla yüklendi',
          description: 'İnceleme sonrası bilgilendirileceksiniz',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });

        // Formu sıfırla
        setFiles([]);
        setPreviews([]);
        setReceiptData({
          transactionDate: new Date().toISOString().split('T')[0],
          amount: '',
          bitcoinAddress: bitcoinAddress,
          description: '',
        });
      } else {
        throw new Error(response.data.message || 'Bir hata oluştu');
      }
    } catch (error: any) {
      toast({
        title: 'Dekont yüklenemedi',
        description: error.message || 'Bir hata oluştu, lütfen tekrar deneyin',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      setIsUploading(false);
    }
    */
  };

  return (
    <Container maxW="container.md" py={8}>
      <Box
        bg={bgColor}
        borderRadius="md"
        p={6}
        borderWidth="1px"
        borderColor={borderColor}
      >
        <Heading size="lg" color={primaryColor} mb={6}>Bitcoin Dekontu Yükle</Heading>

        <VStack spacing={6} align="stretch">
          {/* Bitcoin Adresi Bölümü */}
          <Box
            bg={inputBgColor}
            p={4}
            borderRadius="md"
            borderWidth="1px"
            borderColor={borderColor}
          >
            <Text color={textColor} fontWeight="bold" mb={2}>
              Bitcoin Adresine Ödeme Yapın
            </Text>

            <Flex align="center" mb={3}>
              <Text
                color={textColor}
                fontFamily="monospace"
                fontSize="sm"
                flex="1"
                overflowX="auto"
                whiteSpace="nowrap"
                p={2}
                bg="#0B0E11"
                borderRadius="md"
              >
                {bitcoinAddress}
              </Text>
              <IconButton
                aria-label="Copy address"
                icon={<CopyIcon />}
                size="sm"
                ml={2}
                colorScheme="yellow"
                onClick={copyBitcoinAddress}
              />
            </Flex>

            <Text color="#848E9C" fontSize="sm">
              * Lütfen tam olarak belirttiğiniz tutarı bu adrese gönderin ve ardından dekontunuzu yükleyin.
            </Text>
          </Box>

          {/* Form */}
          <form onSubmit={handleSubmit}>
            <VStack spacing={4} align="stretch">
              <HStack spacing={4}>
                <FormControl isRequired>
                  <FormLabel color="#848E9C" fontSize="sm">İşlem Tarihi</FormLabel>
                  <Input
                    type="date"
                    value={receiptData.transactionDate}
                    onChange={(e) => setReceiptData({...receiptData, transactionDate: e.target.value})}
                    bg={inputBgColor}
                    borderColor={borderColor}
                    color={textColor}
                    _hover={{ borderColor: primaryColor }}
                    _focus={{ borderColor: primaryColor, boxShadow: "none" }}
                  />
                </FormControl>

                <FormControl isRequired>
                  <FormLabel color="#848E9C" fontSize="sm">İşlem Tutarı (USD)</FormLabel>
                  <Input
                    type="number"
                    value={receiptData.amount}
                    onChange={(e) => setReceiptData({...receiptData, amount: e.target.value})}
                    placeholder="1000.00"
                    min="1"
                    step="0.01"
                    bg={inputBgColor}
                    borderColor={borderColor}
                    color={textColor}
                    _hover={{ borderColor: primaryColor }}
                    _focus={{ borderColor: primaryColor, boxShadow: "none" }}
                  />
                </FormControl>
              </HStack>

              <FormControl>
                <FormLabel color="#848E9C" fontSize="sm">İşlem Açıklaması (Opsiyonel)</FormLabel>
                <Textarea
                  value={receiptData.description}
                  onChange={(e) => setReceiptData({...receiptData, description: e.target.value})}
                  placeholder="Ödeme hakkında ek bilgiler..."
                  bg={inputBgColor}
                  borderColor={borderColor}
                  color={textColor}
                  _hover={{ borderColor: primaryColor }}
                  _focus={{ borderColor: primaryColor, boxShadow: "none" }}
                  maxLength={200}
                  rows={3}
                />
              </FormControl>

              {/* Dosya Yükleme Bölümü */}
              <Box
                mt={4}
                p={4}
                borderWidth="1px"
                borderRadius="md"
                borderColor={files.length > 0 ? primaryColor : borderColor}
                borderStyle="dashed"
                bg={inputBgColor}
                textAlign="center"
                cursor="pointer"
                onClick={() => fileInputRef.current?.click()}
                _hover={{ borderColor: primaryColor }}
                transition="all 0.2s"
              >
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileSelect}
                  style={{ display: 'none' }}
                  multiple
                  accept=".jpg,.jpeg,.png,.pdf"
                />

                <VStack spacing={2}>
                  <AttachmentIcon boxSize={10} color={primaryColor} />
                  <Text color={textColor} fontWeight="medium">
                    {files.length > 0
                      ? `${files.length}/3 dosya seçildi - Daha fazla eklemek için tıklayın`
                      : 'Dekont dosyalarını yüklemek için tıklayın'}
                  </Text>
                  <Text color="#848E9C" fontSize="xs">
                    Desteklenen formatlar: JPG, PNG, PDF (Maks. 10MB)
                  </Text>
                </VStack>
              </Box>

              {/* Önizleme Bölümü */}
              {previews.length > 0 && (
                <Flex mt={4} flexWrap="wrap" gap={4}>
                  {previews.map((preview, index) => (
                    <Box
                      key={index}
                      position="relative"
                      width="100px"
                      height="100px"
                      borderRadius="md"
                      overflow="hidden"
                      borderWidth="1px"
                      borderColor={borderColor}
                    >
                      <Image
                        src={preview}
                        alt={`Dekont ${index + 1}`}
                        objectFit="cover"
                        width="100%"
                        height="100%"
                      />
                      <IconButton
                        aria-label="Remove file"
                        icon={<CloseIcon />}
                        size="xs"
                        colorScheme="red"
                        position="absolute"
                        top={1}
                        right={1}
                        onClick={(e) => {
                          e.stopPropagation();
                          removeFile(index);
                        }}
                      />
                      <Badge
                        position="absolute"
                        bottom={1}
                        left={1}
                        colorScheme="yellow"
                        fontSize="xs"
                      >
                        {files[index].name.split('.').pop()?.toUpperCase()}
                      </Badge>
                    </Box>
                  ))}
                </Flex>
              )}

              {/* Yükleme İlerleme Çubuğu */}
              {isUploading && (
                <Box mt={4}>
                  <Text color={textColor} fontSize="sm" mb={2}>
                    Yükleniyor... {uploadProgress}%
                  </Text>
                  <Progress
                    value={uploadProgress}
                    size="sm"
                    colorScheme="yellow"
                    borderRadius="full"
                  />
                </Box>
              )}

              {/* Gönder Butonu */}
              <Button
                mt={6}
                type="submit"
                colorScheme="yellow"
                size="lg"
                width="full"
                isLoading={isUploading}
                loadingText="Yükleniyor..."
                leftIcon={<CheckIcon />}
                disabled={isUploading || files.length === 0}
              >
                Dekont Gönder
              </Button>
            </VStack>
          </form>

          {/* Bilgi Kutusu */}
          <Box
            mt={4}
            p={4}
            borderRadius="md"
            bg="#F0B90B10"
            borderWidth="1px"
            borderColor="#F0B90B30"
          >
            <Text color={textColor} fontWeight="medium" mb={2}>
              Dekont Yükleme İpuçları
            </Text>
            <VStack align="start" spacing={1}>
              <Text color="#848E9C" fontSize="sm">• Dekont görüntüsünün net ve okunaklı olduğundan emin olun</Text>
              <Text color="#848E9C" fontSize="sm">• İşlem tutarı ve Bitcoin adresi görünür olmalıdır</Text>
              <Text color="#848E9C" fontSize="sm">• İşlem hash'i görünür olmalıdır</Text>
              <Text color="#848E9C" fontSize="sm">• Onay süreci genellikle 24 saat içinde tamamlanır</Text>
            </VStack>
          </Box>
        </VStack>
      </Box>
    </Container>
  );
};

export default ReceiptUploader;
