import express from 'express';
import {
  getTransactions,
  getTransactionById,
  calculateCommission,
  calculateInterest,
  getAdminTransactions,
  updateTransactionStatus
} from '../controllers/transactionController';
import { protect, admin } from '../middleware/authMiddleware';
import { cacheMiddleware, clearCache } from '../middleware/cacheMiddleware';
import { wrapController } from '../utils/routeWrapper';

const router = express.Router();

// User routes - all are protected
router.get('/', protect, cacheMiddleware({ keyPrefix: 'api:transactions:' }), wrapController(getTransactions));
router.get('/:id', protect, cacheMiddleware({ keyPrefix: 'api:transaction:' }), wrapController(getTransactionById));
router.post('/calculate-commission', protect, wrapController(calculateCommission));
router.post('/calculate-interest', protect, wrapController(calculateInterest));

// Admin routes - protected and require admin role
router.get('/admin/all', protect, admin, wrapController(getAdminTransactions));
router.put('/:id/status', protect, admin, clearCache('transactions:'), wrapController(updateTransactionStatus));

export default router;
