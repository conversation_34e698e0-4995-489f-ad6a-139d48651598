# Investment Test Data Creation Guide

This guide explains how to create comprehensive test data for the **Investment Summary by C<PERSON><PERSON>cy** screen in the Shipping Finance platform.

## 🎯 Overview

The test data creation tools generate realistic investment packages across multiple cryptocurrencies to help you test and demonstrate the Investment Summary functionality. The generated data includes:

- **Multiple Currencies**: BTC, ETH, USDT, BNB, SOL, TRX, DOGE
- **Various Statuses**: pending, active, completed, withdrawn
- **Realistic Earnings**: Based on daily interest calculations (1% daily)
- **Time Diversity**: Different creation and activation dates
- **Amount Variety**: Different investment amounts per currency

## 🛠️ Available Methods

### Method 1: Backend Database Script (Recommended)

This method directly creates data in the database and is the most reliable.

#### Prerequisites
- Backend server environment set up
- MongoDB connection configured
- Node.js and TypeScript installed

#### Steps
1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Run the test data creation script:
   ```bash
   # For local environment
   npm run create-investment-test-data:local
   
   # For Docker environment
   npm run create-investment-test-data:docker
   
   # For production environment
   npm run create-investment-test-data
   ```

3. The script will:
   - Create a test user: `<EMAIL>` / `TestPassword123!`
   - Generate 25+ investment packages across 7 cryptocurrencies
   - Display a summary of created data

### Method 2: Frontend API Script

This method creates data through API calls and can be run in the browser.

#### Option A: Browser Console
1. Open your browser and navigate to your frontend application
2. Open Developer Tools (F12)
3. Go to the Console tab
4. Copy and paste the contents of `frontend/scripts/createInvestmentTestData.js`
5. Run the script:
   ```javascript
   const creator = new InvestmentTestDataCreator();
   creator.run();
   ```

#### Option B: HTML Interface
1. Open `frontend/scripts/test-data-creator.html` in your browser
2. Click the "Create Test Data" button
3. Monitor the progress in the log area
4. Use the provided credentials to login and view the data

#### Option C: Node.js Script
1. Navigate to the frontend scripts directory:
   ```bash
   cd frontend/scripts
   ```

2. Install dependencies if needed:
   ```bash
   npm install node-fetch  # If running in Node.js environment
   ```

3. Run the script:
   ```bash
   node createInvestmentTestData.js
   ```

## 📊 Generated Test Data Structure

### Test User
- **Email**: `<EMAIL>`
- **Password**: `TestPassword123!`
- **Name**: Test Investor
- **Status**: Verified user

### Investment Packages by Currency

#### Bitcoin (BTC)
- 4 packages with amounts: 0.5, 0.25, 1.0, 0.1 BTC
- Mix of active, completed, and pending statuses
- Investment periods: 2-120 days

#### Ethereum (ETH)
- 4 packages with amounts: 5.0, 2.5, 10.0, 1.5 ETH
- Various statuses including withdrawn packages
- Investment periods: 1-90 days

#### Tether (USDT)
- 5 packages with amounts: 1000, 500, 2000, 750, 300 USDT
- Largest variety to test different scenarios
- Investment periods: 3-100 days

#### Binance Coin (BNB)
- 3 packages with amounts: 20, 50, 15 BNB
- Active and pending statuses
- Investment periods: 1-50 days

#### Solana (SOL)
- 3 packages with amounts: 100, 250, 75 SOL
- Mix of active, withdrawn, and pending
- Investment periods: 2-80 days

#### Tron (TRX)
- 3 packages with amounts: 10000, 5000, 15000 TRX
- Active and completed statuses
- Investment periods: 18-110 days

#### Dogecoin (DOGE)
- 3 packages with amounts: 5000, 2500, 7500 DOGE
- Active, pending, and withdrawn statuses
- Investment periods: 1-65 days

## 🔍 Testing the Investment Summary

After creating the test data:

1. **Login** with the test user credentials
2. **Navigate** to the Investments page
3. **Locate** the "Investment Summary by Currency" section
4. **Verify** that you see:
   - Multiple currency cards (BTC, ETH, USDT, BNB, SOL, TRX, DOGE)
   - Total invested amounts for each currency
   - Total earned amounts (calculated based on daily interest)
   - Active package counts
   - ROI percentages

## 🧹 Cleaning Up Test Data

To remove the test data:

### Database Cleanup
```javascript
// In MongoDB shell or through your database client
db.users.deleteOne({ email: "<EMAIL>" });
db.investmentpackages.deleteMany({ userId: ObjectId("USER_ID_HERE") });
```

### API Cleanup
You can create a cleanup script similar to the creation script, but using DELETE endpoints instead.

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Ensure MongoDB is running
   - Check your environment variables
   - Verify database connection string

2. **API Authentication Error**
   - Ensure the backend server is running
   - Check API endpoints are accessible
   - Verify CORS settings

3. **User Already Exists Error**
   - This is normal - the script will proceed with login
   - You can manually delete the existing user if needed

4. **Investment Creation Fails**
   - Check if the investment package API endpoints are working
   - Verify the user has proper permissions
   - Check server logs for detailed error messages

### Debug Mode

For more detailed logging, you can modify the scripts to include debug information:

```javascript
// Add this to enable debug mode
const DEBUG = true;
if (DEBUG) {
  console.log('Debug info:', data);
}
```

## 📈 Expected Results

After successful execution, you should see:

- **Investment Summary by Currency** section populated with data
- **7 different cryptocurrencies** represented
- **25+ investment packages** total
- **Realistic earnings** based on time and interest calculations
- **Various statuses** demonstrating different investment states

The test data provides a comprehensive view of how the Investment Summary feature works with real-world-like data scenarios.

## 🔄 Updating Test Data

To refresh or update the test data:

1. Run the cleanup process
2. Execute the creation script again
3. The script will generate fresh data with current timestamps

This ensures you always have up-to-date test scenarios for development and testing purposes.
