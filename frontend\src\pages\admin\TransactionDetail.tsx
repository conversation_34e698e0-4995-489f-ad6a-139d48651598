import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Flex,
  Text,
  Heading,
  Button,
  Badge,
  VStack,
  HStack,
  Divider,
  Image,
  useToast,
  Textarea,
  FormControl,
  FormLabel,
  Card,
  CardBody,
  CardHeader,
  CardFooter,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  useColorModeValue,
  Icon,
  Spinner,
  Center
} from '@chakra-ui/react';
import { CheckIcon, CloseIcon, ArrowBackIcon } from '@chakra-ui/icons';
import { FaUser, FaCalendarAlt, FaBitcoin, FaMoneyBillWave } from 'react-icons/fa';
import { useNavigate, useParams } from 'react-router-dom';
import { Transaction } from '../../components/TransactionHistory';

// Default transaction data (fallback)
const defaultTransaction = {
  id: '1',
  user: {
    id: '101',
    name: '<PERSON>',
    email: '<EMAIL>',
    walletAddress: '******************************************',
    joinDate: '2023-01-15'
  },
  amount: 1500,
  currency: 'BTC',
  btcAmount: 0.025,
  status: 'pending',
  date: '2023-04-28',
  type: 'deposit',
  receiptImage: 'https://via.placeholder.com/800x600',
  description: 'Bitcoin deposit for commission',
  transactionHash: '0x7d91c6f29b4b3c1d9a8d5b6c7d8e9f0a1b2c3d4e5f6a7b8c9d0e1f2a3b4c5d6e',
  commission: 75, // 5% of 1500
  notes: '',
  walletAddress: '******************************************'
};

// Helper function to process receipt URL
const processReceiptUrl = (url: string | undefined): string => {
  if (!url) return '/uploads/receipts/default-receipt.jpg';

  // If URL is already absolute (starts with http:// or https://)
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }

  // If URL is relative, make it absolute
  const baseUrl = window.location.origin;

  // If URL already starts with a slash, don't add another one
  if (url.startsWith('/')) {
    return `${baseUrl}${url}`;
  }

  // Otherwise, add a slash
  return `${baseUrl}/${url}`;
};

const TransactionDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const toast = useToast();
  const [transaction, setTransaction] = useState<Transaction | null>(null);
  const [adminNotes, setAdminNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Background colors - defined here to avoid React Hook rules violation
  const bgColor = useColorModeValue('#1E2329', '#1E2329');
  const cardBgColor = useColorModeValue('#0B0E11', '#0B0E11');
  const borderColor = useColorModeValue('#2B3139', '#2B3139');

  // Load transaction from API
  useEffect(() => {
    const loadTransaction = async () => {
      try {
        setIsLoading(true);

        // Gọi API để lấy thông tin giao dịch
        const response = await fetch(`/api/transactions/${id}`);

        if (response.ok) {
          const data = await response.json();
          console.log('Transaction data from API:', data);

          // Xử lý dữ liệu từ API
          const txData = data;

          // Tính toán số lượng BTC và hoa hồng
          const btcAmount = txData.amount * 0.00001; // Chỉ để hiển thị
          const commission = txData.amount * 0.05; // 5% hoa hồng

          // Lấy thông tin người dùng
          let userData = {
            id: txData.userId?._id || txData.userId || '101',
            name: 'Unknown User',
            email: '<EMAIL>',
            walletAddress: txData.walletAddress || '******************************************',
            joinDate: '2023-01-15'
          };

          // Nếu có thông tin người dùng từ API
          if (txData.userId && typeof txData.userId === 'object') {
            userData = {
              ...userData,
              id: txData.userId._id || txData.userId,
              name: `${txData.userId.firstName || ''} ${txData.userId.lastName || ''}`.trim() || 'Unknown User',
              email: txData.userId.email || '<EMAIL>'
            };
          }

          // Tạo đối tượng giao dịch với dữ liệu từ API
          const adminTransaction = {
            ...defaultTransaction,
            id: txData._id || txData.id || '1',
            amount: txData.amount || 0,
            currency: txData.asset || txData.currency || 'BTC',
            btcAmount: txData.btcAmount || btcAmount,
            commission: txData.commissionAmount || commission,
            commissionRate: txData.commissionRate || 0.05,
            status: txData.status || 'pending',
            date: txData.createdAt ? new Date(txData.createdAt).toISOString().split('T')[0] : '2023-04-28',
            type: txData.type || 'deposit',
            receiptImage: processReceiptUrl(txData.receiptUrl || txData.metadata?.receiptUrl),
            description: txData.description || 'Bitcoin deposit for commission',
            transactionHash: txData.txHash || '0x7d91c6f29b4b3c1d9a8d5b6c7d8e9f0a1b2c3d4e5f6a7b8c9d0e1f2a3b4c5d6e',
            notes: txData.metadata?.adminNotes || '',
            walletAddress: txData.walletAddress || '',
            user: {
              ...userData,
              transactionCount: txData.userTransactionHistory?.count || 5,
              totalVolume: txData.userTransactionHistory?.totalVolume || 7500
            }
          };

          console.log('Processed transaction data:', adminTransaction);
          setTransaction(adminTransaction);
          setAdminNotes(adminTransaction.notes);
        } else {
          // Nếu không tìm thấy giao dịch, sử dụng dữ liệu mặc định
          console.warn('Transaction not found in API, using default data');
          setTransaction(defaultTransaction);
          toast({
            title: "Transaction not found",
            description: "Using sample data for display",
            status: "warning",
            duration: 3000,
            isClosable: true,
          });
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Error loading transaction:', error);
        setTransaction(defaultTransaction);
        setIsLoading(false);
        toast({
          title: "Error loading data",
          description: "Cannot connect to server. Using sample data.",
          status: "error",
          duration: 3000,
          isClosable: true,
        });
      }
    };

    // Handle storage changes
    const handleStorageChange = () => {
      loadTransaction();
    };

    // Add event listeners
    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('transactionUpdated', handleStorageChange);

    // Initial load
    loadTransaction();

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('transactionUpdated', handleStorageChange);
    };
  }, [id, toast]);

  // If still loading
  if (isLoading) {
    return (
      <Box bg="#0B0E11" minH="100vh" display="flex" alignItems="center" justifyContent="center">
        <Center>
          <VStack spacing={4}>
            <Spinner size="xl" color="#F0B90B" thickness="4px" />
            <Text color="#EAECEF">Loading transaction details...</Text>
          </VStack>
        </Center>
      </Box>
    );
  }

  // If transaction not found or error
  if (!transaction) {
    return (
      <Box bg="#0B0E11" minH="100vh" py={8}>
        <Container maxW="container.xl">
          <VStack spacing={4}>
            <Heading color="#F0B90B">Transaction Not Found</Heading>
            <Text color="#EAECEF">The transaction you are looking for does not exist.</Text>
            <Button
              leftIcon={<ArrowBackIcon />}
              colorScheme="yellow"
              onClick={() => navigate('/admin/transactions')}
            >
              Back to Transaction List
            </Button>
          </VStack>
        </Container>
      </Box>
    );
  }



  // Update transaction in localStorage - kept for reference but not used directly
  // as we're now using the API for all updates
  const _updateTransactionInLocalStorage = (transactionId: string, newStatus: string) => {
    try {
      // Get transactions from localStorage
      const storedTransactions = localStorage.getItem('transactions');

      if (storedTransactions) {
        const transactions = JSON.parse(storedTransactions);
        let updatedTransaction = null;

        // Find and update the transaction
        const updatedTransactions = transactions.map((tx: Record<string, any>) => {
          if (tx.id === transactionId) {
            updatedTransaction = { ...tx, status: newStatus };
            return updatedTransaction;
          }
          return tx;
        });

        // Save updated transactions back to localStorage
        localStorage.setItem('transactions', JSON.stringify(updatedTransactions));

        // Update timestamp to trigger storage events
        localStorage.setItem('lastTransactionUpdate', Date.now().toString());

        // Dispatch custom event to notify components about the update
        const event = new CustomEvent('transactionUpdated', {
          detail: updatedTransaction
        });
        window.dispatchEvent(event);
        console.log(`TransactionDetail: Transaction ${transactionId} status updated to ${newStatus}`, updatedTransaction);

        // Dispatch a second event after a small delay to ensure it's caught
        setTimeout(() => {
          const secondEvent = new CustomEvent('transactionUpdated', {
            detail: updatedTransaction
          });
          window.dispatchEvent(secondEvent);
          console.log('TransactionDetail: Second transaction updated event dispatched');

          // Force a storage event by updating the timestamp again
          localStorage.setItem('lastTransactionUpdate', Date.now().toString());
        }, 100);
      }
    } catch (error) {
      console.error('Error updating transaction in localStorage:', error);
    }
  };

  const handleApprove = async () => {
    setIsSubmitting(true);

    try {
      // Gọi API để cập nhật trạng thái giao dịch
      const response = await fetch(`/api/transactions/${transaction.id}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'approved',
          notes: adminNotes
        }),
        credentials: 'include'
      });

      if (response.ok) {
        // Cập nhật state local
        setTransaction({
          ...transaction,
          status: 'approved',
          notes: adminNotes
        });

        toast({
          title: "Transaction approved",
          description: `Transaction #${transaction.id} has been successfully approved.`,
          status: "success",
          duration: 3000,
          isClosable: true,
        });
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Unable to update transaction status');
      }
    } catch (error) {
      console.error('Error approving transaction:', error);
      const errorMessage = error instanceof Error ? error.message : 'An error occurred while approving the transaction';
      toast({
        title: "Error approving transaction",
        description: errorMessage,
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReject = async () => {
    setIsSubmitting(true);

    try {
      // Gọi API để cập nhật trạng thái giao dịch
      const response = await fetch(`/api/transactions/${transaction.id}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'rejected',
          notes: adminNotes
        }),
        credentials: 'include'
      });

      if (response.ok) {
        // Cập nhật state local
        setTransaction({
          ...transaction,
          status: 'rejected',
          notes: adminNotes
        });

        toast({
          title: "Transaction rejected",
          description: `Transaction #${transaction.id} has been rejected.`,
          status: "error",
          duration: 3000,
          isClosable: true,
        });
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Unable to update transaction status');
      }
    } catch (error) {
      console.error('Error rejecting transaction:', error);
      const errorMessage = error instanceof Error ? error.message : 'An error occurred while rejecting the transaction';
      toast({
        title: "Error rejecting transaction",
        description: errorMessage,
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box bg="#0B0E11" minH="100vh" pb={8}>
      <Container maxW="container.xl" px={{ base: 2, md: 4 }} py={8}>
        <Flex justify="space-between" align="center" mb={6}>
          <HStack>
            <Button
              leftIcon={<ArrowBackIcon />}
              variant="ghost"
              color="#EAECEF"
              onClick={() => navigate('/admin/transactions')}
            >
              Back to Transaction List
            </Button>
            <Heading size="lg" color="#F0B90B">Transaction Details</Heading>
          </HStack>

          <Badge
            colorScheme={
              transaction.status === 'approved' ? 'green' :
              transaction.status === 'pending' ? 'yellow' : 'red'
            }
            fontSize="md"
            py={1}
            px={3}
            borderRadius="md"
          >
            {transaction.status.toUpperCase()}
          </Badge>
        </Flex>

        <Flex
          direction={{ base: "column", lg: "row" }}
          gap={6}
        >
          {/* Left Column - Transaction Details */}
          <Box flex="1">
            <Card bg={bgColor} borderColor={borderColor} borderWidth="1px" mb={6}>
              <CardHeader>
                <Heading size="md" color="#EAECEF">Transaction Information</Heading>
              </CardHeader>
              <Divider borderColor={borderColor} />
              <CardBody>
                <VStack spacing={4} align="stretch">
                  <Flex justify="space-between">
                    <Text color="#848E9C">Transaction ID</Text>
                    <Text color="#EAECEF" fontWeight="medium">{transaction.id}</Text>
                  </Flex>

                  <Flex justify="space-between">
                    <Text color="#848E9C">Type</Text>
                    <Badge
                      colorScheme={transaction.type === 'deposit' ? 'green' : 'red'}
                      borderRadius="full"
                      px={2}
                    >
                      {transaction.type}
                    </Badge>
                  </Flex>

                  <Flex justify="space-between">
                    <Text color="#848E9C">Amount</Text>
                    <Text color="#EAECEF" fontWeight="medium">{transaction.amount.toLocaleString()} {transaction.currency}</Text>
                  </Flex>

                  <Flex justify="space-between">
                    <Text color="#848E9C">Amount (BTC)</Text>
                    <Text color="#EAECEF" fontWeight="medium">{transaction.btcAmount} BTC</Text>
                  </Flex>

                  {transaction.type !== 'withdrawal' && (
                    <Flex justify="space-between">
                      <Text color="#848E9C">Commission (5%)</Text>
                      <Text color="#0ECB81" fontWeight="medium">{transaction.commission.toLocaleString()} {transaction.currency}</Text>
                    </Flex>
                  )}

                  <Flex justify="space-between">
                    <Text color="#848E9C">Date</Text>
                    <Text color="#EAECEF" fontWeight="medium">{transaction.date}</Text>
                  </Flex>

                  <Flex justify="space-between">
                    <Text color="#848E9C">Transaction Hash</Text>
                    <Text color="#EAECEF" fontWeight="medium" fontSize="sm" maxW="250px" isTruncated>
                      {transaction.transactionHash}
                    </Text>
                  </Flex>

                  <Box>
                    <Text color="#848E9C" mb={1}>Description</Text>
                    <Text color="#EAECEF" fontWeight="medium">
                      {transaction.description || 'No description provided'}
                    </Text>
                  </Box>
                </VStack>
              </CardBody>
            </Card>

            <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
              <CardHeader>
                <Heading size="md" color="#EAECEF">User Information</Heading>
              </CardHeader>
              <Divider borderColor={borderColor} />
              <CardBody>
                <VStack spacing={4} align="stretch">
                  <Flex align="center">
                    <Icon as={FaUser} color="#F0B90B" mr={3} />
                    <VStack align="start" spacing={0}>
                      <Text color="#EAECEF" fontWeight="medium">{transaction.user.name}</Text>
                      <Text color="#848E9C" fontSize="sm">{transaction.user.email}</Text>
                    </VStack>
                  </Flex>

                  <Flex align="center">
                    <Icon as={FaBitcoin} color="#F0B90B" mr={3} />
                    <VStack align="start" spacing={0}>
                      <Text color="#EAECEF" fontWeight="medium" fontSize="sm">{transaction.user.walletAddress}</Text>
                      <Text color="#848E9C" fontSize="sm">Wallet Address</Text>
                    </VStack>
                  </Flex>

                  <Flex align="center">
                    <Icon as={FaCalendarAlt} color="#F0B90B" mr={3} />
                    <VStack align="start" spacing={0}>
                      <Text color="#EAECEF" fontWeight="medium">Member since {transaction.user.joinDate}</Text>
                      <Text color="#848E9C" fontSize="sm">User ID: {transaction.user.id}</Text>
                    </VStack>
                  </Flex>

                  <Flex align="center">
                    <Icon as={FaMoneyBillWave} color="#F0B90B" mr={3} />
                    <VStack align="start" spacing={0}>
                      <Text color="#EAECEF" fontWeight="medium">{transaction.user.transactionCount || 0} previous transactions</Text>
                      <Text color="#848E9C" fontSize="sm">Total volume: {transaction.user.totalVolume?.toLocaleString() || 0} {transaction.currency}</Text>
                    </VStack>
                  </Flex>
                </VStack>
              </CardBody>
            </Card>
          </Box>

          {/* Right Column - Receipt and Actions */}
          <Box flex="1">
            {/* Transaction Receipt - Only shown for non-withdrawal transactions */}
            {transaction.type !== 'withdrawal' && (
              <Card bg={bgColor} borderColor={borderColor} borderWidth="1px" mb={6}>
                <CardHeader>
                  <Heading size="md" color="#EAECEF">Transaction Receipt</Heading>
                </CardHeader>
                <Divider borderColor={borderColor} />
                <CardBody>
                  <Box
                    borderWidth="1px"
                    borderColor={borderColor}
                    borderRadius="md"
                    overflow="hidden"
                    mb={4}
                    position="relative"
                    minHeight="200px"
                  >
                    {transaction.receiptImage ? (
                      <Image
                        src={transaction.receiptImage}
                        alt="Transaction Receipt"
                        width="100%"
                        fallback={
                          <Center h="200px">
                            <VStack>
                              <Spinner size="xl" color="#F0B90B" thickness="4px" />
                              <Text color="#EAECEF">Loading image...</Text>
                            </VStack>
                          </Center>
                        }
                        onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                          console.error('Error loading receipt image:', e);
                          // Try to use a full URL if the relative path fails
                          const baseUrl = window.location.origin;
                          const imgElement = e.currentTarget as HTMLImageElement;
                          imgElement.src = `${baseUrl}/uploads/receipts/default-receipt.jpg`;
                        }}
                      />
                    ) : (
                      <Center h="200px">
                        <Text color="#848E9C">No receipt image available</Text>
                      </Center>
                    )}
                  </Box>

                  <Button
                    colorScheme="blue"
                    width="full"
                    size="sm"
                    mb={4}
                    onClick={() => {
                      if (transaction.receiptImage) {
                        window.open(transaction.receiptImage, '_blank');
                      } else {
                        toast({
                          title: "No receipt available",
                          description: "This transaction has no receipt to download",
                          status: "warning",
                          duration: 3000,
                          isClosable: true,
                        });
                      }
                    }}
                    isDisabled={!transaction.receiptImage}
                  >
                    Download Receipt
                  </Button>
                </CardBody>
              </Card>
            )}

            {/* Admin Actions Card - Always shown */}
            <Card bg={bgColor} borderColor={borderColor} borderWidth="1px" mb={6}>
              <CardHeader>
                <Heading size="md" color="#EAECEF">Admin Actions</Heading>
              </CardHeader>
              <Divider borderColor={borderColor} />
              <CardBody>
                <FormControl>
                  <FormLabel color="#848E9C">Admin Notes</FormLabel>
                  <Textarea
                    placeholder="Add notes about this transaction..."
                    value={adminNotes}
                    onChange={(e) => setAdminNotes(e.target.value)}
                    bg={cardBgColor}
                    borderColor={borderColor}
                    color="#EAECEF"
                    rows={4}
                  />
                </FormControl>
              </CardBody>

              {transaction.status === 'pending' && (
                <CardFooter>
                  <HStack width="full" spacing={4}>
                    <Button
                      colorScheme="green"
                      leftIcon={<CheckIcon />}
                      flex="1"
                      onClick={handleApprove}
                      isLoading={isSubmitting}
                    >
                      Approve Transaction
                    </Button>
                    <Button
                      colorScheme="red"
                      leftIcon={<CloseIcon />}
                      flex="1"
                      onClick={handleReject}
                      isLoading={isSubmitting}
                    >
                      Reject Transaction
                    </Button>
                  </HStack>
                </CardFooter>
              )}

              {transaction.status !== 'pending' && (
                <CardFooter>
                  <Box width="full" p={4} bg={cardBgColor} borderRadius="md">
                    <Text color="#EAECEF" fontWeight="medium">
                      This transaction has been {transaction.status}.
                    </Text>
                    <Text color="#848E9C" fontSize="sm" mt={1}>
                      {transaction.status === 'approved'
                        ? 'Commission has been credited to the user\'s account.'
                        : 'No commission was credited for this transaction.'}
                    </Text>
                  </Box>
                </CardFooter>
              )}
            </Card>

            {transaction.type !== 'withdrawal' && (
              <Card bg={bgColor} borderColor={borderColor} borderWidth="1px">
                <CardHeader>
                  <Heading size="md" color="#EAECEF">Commission Calculation</Heading>
                </CardHeader>
                <Divider borderColor={borderColor} />
                <CardBody>
                  <VStack spacing={4} align="stretch">
                    <Stat>
                      <StatLabel color="#848E9C">Deposit Amount</StatLabel>
                      <StatNumber color="#EAECEF">{transaction.amount.toLocaleString()} {transaction.currency}</StatNumber>
                      <StatHelpText color="#848E9C">{transaction.btcAmount} BTC</StatHelpText>
                    </Stat>

                    <Stat>
                      <StatLabel color="#848E9C">Commission Rate</StatLabel>
                      <StatNumber color="#EAECEF">{(transaction.commissionRate * 100).toFixed(0)}%</StatNumber>
                      <StatHelpText color="#848E9C">Standard rate for all users</StatHelpText>
                    </Stat>

                    <Stat>
                      <StatLabel color="#848E9C">Commission Amount</StatLabel>
                      <StatNumber color="#0ECB81">{transaction.commission.toLocaleString()} {transaction.currency}</StatNumber>
                      <StatHelpText color="#848E9C">{(transaction.btcAmount * 0.05).toFixed(8)} BTC</StatHelpText>
                    </Stat>
                  </VStack>
                </CardBody>
              </Card>
            )}
          </Box>
        </Flex>
      </Container>
    </Box>
  );
};

export default TransactionDetail;
