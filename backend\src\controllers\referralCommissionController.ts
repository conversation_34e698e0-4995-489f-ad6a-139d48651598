import { Request, Response } from 'express';
import ReferralCommission from '../models/referralCommission';
import ReferralCommissionConfig from '../models/referralCommissionConfig';
import User from '../models/userModel';
import { logger } from '../utils/logger';
import referralCommissionService from '../services/referralCommissionService';

/**
 * @desc    Lấy danh sách hoa hồng của người dùng hiện tại
 * @route   GET /api/commissions
 * @access  Private
 */
export const getUserCommissions = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user._id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const status = req.query.status as string;
    const currency = req.query.currency as string;

    // Xây dựng query
    const query: any = { referrerId: userId };
    if (status) query.status = status;
    if (currency) query.currency = currency.toUpperCase();

    // Thực hiện truy vấn với phân trang
    const [commissions, total] = await Promise.all([
      ReferralCommission.find(query)
        .populate('referredId', 'firstName lastName email')
        .populate('investmentId', 'amount currency status')
        .sort({ createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit),
      ReferralCommission.countDocuments(query)
    ]);

    // Tính tổng hoa hồng theo từng loại tiền tệ
    const totalsByAsset = await ReferralCommission.aggregate([
      { $match: { referrerId: userId, status: 'approved' } },
      { $group: { _id: '$currency', total: { $sum: '$amount' } } }
    ]);

    res.json({
      commissions,
      totals: totalsByAsset,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error: any) {
    logger.error('Error fetching user commissions:', error);
    res.status(500).json({
      message: 'An error occurred while fetching commissions',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Lấy tổng hoa hồng của người dùng hiện tại
 * @route   GET /api/commissions/total
 * @access  Private
 */
export const getUserTotalCommission = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user._id;
    const totals = await referralCommissionService.getUserTotalCommission(userId.toString());
    
    res.json({
      totals
    });
  } catch (error: any) {
    logger.error('Error fetching user total commission:', error);
    res.status(500).json({
      message: 'An error occurred while fetching total commission',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Lấy danh sách cấu hình hoa hồng
 * @route   GET /api/admin/commissions/config
 * @access  Admin
 */
export const getCommissionConfigs = async (req: Request, res: Response): Promise<void> => {
  try {
    const configs = await ReferralCommissionConfig.find().sort({ level: 1 });
    
    res.json({
      configs
    });
  } catch (error: any) {
    logger.error('Error fetching commission configs:', error);
    res.status(500).json({
      message: 'An error occurred while fetching commission configs',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Tạo hoặc cập nhật cấu hình hoa hồng
 * @route   POST /api/admin/commissions/config
 * @access  Admin
 */
export const createOrUpdateCommissionConfig = async (req: Request, res: Response): Promise<void> => {
  try {
    const { level, commissionRate, minInvestmentAmount, isActive } = req.body;

    // Validate input
    if (!level || commissionRate === undefined) {
      res.status(400).json({
        message: 'Invalid input',
        errors: {
          level: !level ? 'Level is required' : undefined,
          commissionRate: commissionRate === undefined ? 'Commission rate is required' : undefined
        }
      });
      return;
    }

    // Kiểm tra xem cấu hình đã tồn tại chưa
    let config = await ReferralCommissionConfig.findOne({ level });

    if (config) {
      // Cập nhật cấu hình hiện có
      config.commissionRate = commissionRate;
      if (minInvestmentAmount !== undefined) config.minInvestmentAmount = minInvestmentAmount;
      if (isActive !== undefined) config.isActive = isActive;
      
      await config.save();
      
      logger.info(`Commission config updated for level ${level}`);
      
      res.json({
        message: 'Commission config updated successfully',
        config
      });
    } else {
      // Tạo cấu hình mới
      config = await ReferralCommissionConfig.create({
        level,
        commissionRate,
        minInvestmentAmount: minInvestmentAmount || 0,
        isActive: isActive !== undefined ? isActive : true
      });
      
      logger.info(`New commission config created for level ${level}`);
      
      res.status(201).json({
        message: 'Commission config created successfully',
        config
      });
    }
  } catch (error: any) {
    logger.error('Error creating/updating commission config:', error);
    res.status(500).json({
      message: 'An error occurred while creating/updating commission config',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Xóa cấu hình hoa hồng
 * @route   DELETE /api/admin/commissions/config/:id
 * @access  Admin
 */
export const deleteCommissionConfig = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    const config = await ReferralCommissionConfig.findById(id);
    
    if (!config) {
      res.status(404).json({ message: 'Commission config not found' });
      return;
    }
    
    await config.deleteOne();
    
    logger.info(`Commission config deleted: ${id}`);
    
    res.json({ message: 'Commission config deleted successfully' });
  } catch (error: any) {
    logger.error('Error deleting commission config:', error);
    res.status(500).json({
      message: 'An error occurred while deleting commission config',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Lấy danh sách tất cả hoa hồng (admin)
 * @route   GET /api/admin/commissions
 * @access  Admin
 */
export const getAllCommissions = async (req: Request, res: Response): Promise<void> => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const status = req.query.status as string;
    const currency = req.query.currency as string;
    const search = req.query.search as string;

    // Tìm người dùng phù hợp với từ khóa tìm kiếm
    let userIds: any[] = [];
    if (search) {
      const users = await User.find({
        $or: [
          { email: { $regex: search, $options: 'i' } },
          { firstName: { $regex: search, $options: 'i' } },
          { lastName: { $regex: search, $options: 'i' } }
        ]
      }).select('_id');

      userIds = users.map(user => user._id);
    }

    // Xây dựng query
    const query: any = {};
    if (status) query.status = status;
    if (currency) query.currency = currency.toUpperCase();
    
    if (search && userIds.length > 0) {
      query.$or = [
        { referrerId: { $in: userIds } },
        { referredId: { $in: userIds } }
      ];
    } else if (search) {
      // Nếu không tìm thấy người dùng, kiểm tra xem search có phải là ID không
      query.$or = [
        { _id: search.length === 24 ? search : null },
        { investmentId: search.length === 24 ? search : null }
      ];
    }

    // Thực hiện truy vấn với phân trang
    const [commissions, total] = await Promise.all([
      ReferralCommission.find(query)
        .populate('referrerId', 'firstName lastName email')
        .populate('referredId', 'firstName lastName email')
        .populate('investmentId', 'amount currency status')
        .sort({ createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit),
      ReferralCommission.countDocuments(query)
    ]);

    res.json({
      commissions,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error: any) {
    logger.error('Error fetching all commissions:', error);
    res.status(500).json({
      message: 'An error occurred while fetching commissions',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

export default {
  getUserCommissions,
  getUserTotalCommission,
  getCommissionConfigs,
  createOrUpdateCommissionConfig,
  deleteCommissionConfig,
  getAllCommissions
};
