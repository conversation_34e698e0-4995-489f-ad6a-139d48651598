import express from 'express';
import multer, { FileFilterCallback } from 'multer';
import path from 'path';
import fs from 'fs';
import crypto from 'crypto';
import {
  createInvestment,
  uploadReceipt,
  getInvestments,
  getInvestmentById,
  updateTransactionHash
} from '../controllers/investmentController';
import { protect } from '../middleware/authMiddleware';
import { cacheMiddleware, clearCache } from '../middleware/cacheMiddleware';
import { wrapController } from '../utils/routeWrapper';

const router = express.Router();

// Set up multer storage for receipt uploads
const storage = multer.diskStorage({
  destination: (_req: Express.Request, _file: Express.Multer.File, cb: (error: Error | null, destination: string) => void) => {
    const uploadDir = path.join(__dirname, '../../uploads/receipts');

    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    cb(null, uploadDir);
  },
  filename: (_req: Express.Request, file: Express.Multer.File, cb: (error: Error | null, filename: string) => void) => {
    // Generate unique filename
    const uniqueSuffix = `${Date.now()}-${crypto.randomBytes(6).toString('hex')}`;
    const fileExt = path.extname(file.originalname);
    cb(null, `receipt-${uniqueSuffix}${fileExt}`);
  }
});

// Set up multer upload
const upload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (_req: Express.Request, file: Express.Multer.File, cb: FileFilterCallback) => {
    // Accept only images and PDFs
    const allowedTypes = /jpeg|jpg|png|gif|pdf/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (extname && mimetype) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files (jpeg, jpg, png, gif) and PDFs are allowed') as any);
    }
  }
});

// All routes are protected
router.post('/', protect, clearCache('investments:'), wrapController(createInvestment));
router.post('/:id/receipt', protect, clearCache('investments:'), upload.single('receipt'), wrapController(uploadReceipt));
router.get('/', protect, cacheMiddleware({ keyPrefix: 'api:investments:' }), wrapController(getInvestments));
router.get('/:id', protect, cacheMiddleware({ keyPrefix: 'api:investments:' }), wrapController(getInvestmentById));
router.put('/:id/txhash', protect, clearCache('investments:'), wrapController(updateTransactionHash));

export default router;
