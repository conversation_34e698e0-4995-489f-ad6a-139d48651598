import React from 'react';
import {
  Box,
  IconButton,
  <PERSON>ge,
  Tooltip
} from '@chakra-ui/react';
import { FaBell } from 'react-icons/fa';

const SimpleNotifications: React.FC = () => {
  return (
    <Tooltip label="Notifications">
      <Box position="relative" display="inline-block">
        <IconButton
          aria-label="Notifications"
          icon={<FaBell />}
          variant="ghost"
          color="#F0B90B"
          _hover={{ bg: 'transparent', color: '#F0B90B' }}
        />
      </Box>
    </Tooltip>
  );
};

export default SimpleNotifications;
