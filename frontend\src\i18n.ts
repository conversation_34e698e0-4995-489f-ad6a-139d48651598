import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import translations - Supporting exactly 3 languages: English, German, French
import translationEN from './locales/en/translation.json';
import translationDE from './locales/de/translation.json';
import translationFR from './locales/fr/translation.json';

// Import KYC translations
import kycEN from './locales/en/kyc.json';
import kycDE from './locales/de/kyc.json';
import kycFR from './locales/fr/kyc.json';

// the translations - supporting exactly 3 languages: English, German, French
const resources = {
  en: {
    translation: translationEN,
    kyc: kycEN
  },
  de: {
    translation: translationDE,
    kyc: kycDE
  },
  fr: {
    translation: translationFR,
    kyc: kycFR
  }
};

// Initialize i18next synchronously to avoid race conditions
const initI18n = () => {
  if (!i18n.isInitialized) {
    i18n
      // detect user language
      .use(LanguageDetector)
      // pass the i18n instance to react-i18next
      .use(initReactI18next)
      // init i18next
      .init({
        resources,
        lng: 'en', // Force English as default language
        fallbackLng: 'en',
        debug: process.env.NODE_ENV === 'development', // Enable debug only in development

        // Language detection options - Disabled to force English
        detection: {
          order: [], // Disable automatic language detection
          lookupLocalStorage: 'i18nextLng',
          caches: [],
          excludeCacheFor: ['cimode']
        },

        interpolation: {
          escapeValue: false // not needed for react as it escapes by default
        },

        react: {
          useSuspense: false // Changed to false to prevent issues with Suspense
        },

        // Namespace configuration
        defaultNS: 'translation',
        ns: ['translation', 'kyc'],

        // Load missing translations
        saveMissing: process.env.NODE_ENV === 'development',
        missingKeyHandler: (lng, ns, key) => {
          if (process.env.NODE_ENV === 'development') {
            console.warn(`Missing translation: ${lng}.${ns}.${key}`);
          }
        }
      }, (err) => {
        if (err) {
          console.error('Error initializing i18n:', err);
        } else {
          console.log('i18n initialized successfully');
        }
      });
  }
  return i18n;
};

// Initialize i18n immediately
const i18nInstance = initI18n();

export default i18nInstance;
