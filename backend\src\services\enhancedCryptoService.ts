import InvestmentPackage from '../models/investmentPackageModel';
import DepositTransaction from '../models/depositTransactionModel';
import UserWallet from '../models/userWalletModel';
import cryptoApiService from './cryptoApiService';
import cryptoTimeService from './cryptoTimeService';
import socketService from './socketService';
import { EventEmitter } from 'events';

interface CryptoBalance {
  symbol: string;
  balance: number;
  usdtValue: number;
  dailyInterest: number;
  address: string;
  qrCodeUrl?: string;
}

interface InterestCalculationResult {
  packageId: string;
  previousBalance: number;
  interestAmount: number;
  newBalance: number;
  calculatedAt: Date;
}

interface WithdrawalValidation {
  canWithdraw: boolean;
  reason?: string;
  minimumRequired: number;
  currentUSDTValue: number;
  timeUntilAllowed?: number;
}

/**
 * Enhanced crypto service for comprehensive investment platform
 */
class EnhancedCryptoService extends EventEmitter {
  private readonly SUPPORTED_CURRENCIES = ['BTC', 'ETH', 'USDT', 'BNB', 'SOL'];
  private readonly DAILY_INTEREST_RATE = 0.01; // 1%
  private readonly MINIMUM_WITHDRAWAL_USDT = 50;

  /**
   * Get comprehensive crypto balances for a user
   */
  async getUserCryptoBalances(userId: string): Promise<CryptoBalance[]> {
    try {
      const balances: CryptoBalance[] = [];

      for (const currency of this.SUPPORTED_CURRENCIES) {
        const wallet = await UserWallet.findOne({ userId, currency });
        const packages = await InvestmentPackage.find({ 
          userId, 
          currency, 
          status: 'active' 
        });

        const totalBalance = packages.reduce((sum, pkg) => sum + pkg.amount + pkg.totalEarned, 0);
        const dailyInterest = packages.reduce((sum, pkg) => sum + pkg.calculateDailyInterest(), 0);

        // Get current USDT value
        let usdtValue = 0;
        if (currency === 'USDT') {
          usdtValue = totalBalance;
        } else {
          try {
            const rate = await cryptoApiService.getExchangeRate(currency, 'USDT');
            usdtValue = totalBalance * rate.rate;
          } catch (error) {
            console.warn(`Failed to get exchange rate for ${currency}:`, error);
          }
        }

        balances.push({
          symbol: currency,
          balance: totalBalance,
          usdtValue,
          dailyInterest,
          address: wallet?.address || '',
          qrCodeUrl: wallet?.qrCodeUrl
        });
      }

      return balances;
    } catch (error) {
      console.error('Error getting user crypto balances:', error);
      throw new Error('Failed to fetch crypto balances');
    }
  }

  /**
   * Calculate daily interest for all active packages
   */
  async calculateDailyInterest(): Promise<InterestCalculationResult[]> {
    try {
      const results: InterestCalculationResult[] = [];
      const activePackages = await InvestmentPackage.find({ status: 'active' });

      for (const pkg of activePackages) {
        // Check if package can earn interest today
        if (!cryptoTimeService.canEarnInterestToday(pkg.lastCalculatedAt)) {
          continue;
        }

        const previousBalance = pkg.totalEarned;
        const interestAmount = pkg.calculateDailyInterest();
        
        // Update package
        pkg.totalEarned += interestAmount;
        pkg.dailyInterest = interestAmount;
        pkg.lastCalculatedAt = new Date();
        pkg.activeDays += 1;
        pkg.nextInterestTime = cryptoTimeService.getNextInterestTimeForPackage(new Date());

        // Update real-time USDT value
        if (pkg.currency !== 'USDT') {
          try {
            const rate = await cryptoApiService.getExchangeRate(pkg.currency, 'USDT');
            pkg.realTimeUSDTValue = pkg.totalEarned * rate.rate;
          } catch (error) {
            console.warn(`Failed to update USDT value for ${pkg.currency}:`, error);
          }
        } else {
          pkg.realTimeUSDTValue = pkg.totalEarned;
        }

        pkg.lastUSDTUpdate = new Date();
        await pkg.save();

        const result: InterestCalculationResult = {
          packageId: pkg._id.toString(),
          previousBalance,
          interestAmount,
          newBalance: pkg.totalEarned,
          calculatedAt: new Date()
        };

        results.push(result);

        // Emit interest earned event
        this.emit('interestEarned', {
          userId: pkg.userId,
          packageId: pkg._id,
          currency: pkg.currency,
          amount: interestAmount,
          totalEarned: pkg.totalEarned
        });

        // Send real-time notification
        socketService.sendToUser(pkg.userId.toString(), {
          type: 'interest_earned',
          data: {
            packageId: pkg._id,
            currency: pkg.currency,
            amount: interestAmount,
            totalEarned: pkg.totalEarned,
            message: `${interestAmount.toFixed(6)} ${pkg.currency} earned! 🎉`
          }
        });
      }

      console.log(`✅ Calculated interest for ${results.length} packages`);
      return results;
    } catch (error) {
      console.error('Error calculating daily interest:', error);
      throw new Error('Failed to calculate daily interest');
    }
  }

  /**
   * Validate withdrawal eligibility
   */
  async validateWithdrawal(userId: string, packageId: string): Promise<WithdrawalValidation> {
    try {
      const pkg = await InvestmentPackage.findOne({ _id: packageId, userId });
      
      if (!pkg) {
        return {
          canWithdraw: false,
          reason: 'Package not found',
          minimumRequired: this.MINIMUM_WITHDRAWAL_USDT,
          currentUSDTValue: 0
        };
      }

      // Update real-time USDT value
      let currentUSDTValue = pkg.realTimeUSDTValue || 0;
      if (pkg.currency !== 'USDT') {
        try {
          const rate = await cryptoApiService.getExchangeRate(pkg.currency, 'USDT');
          currentUSDTValue = pkg.totalEarned * rate.rate;
          
          // Update package with latest value
          pkg.realTimeUSDTValue = currentUSDTValue;
          pkg.lastUSDTUpdate = new Date();
          await pkg.save();
        } catch (error) {
          console.warn(`Failed to get current exchange rate for ${pkg.currency}`);
        }
      } else {
        currentUSDTValue = pkg.totalEarned;
      }

      // Check minimum threshold
      if (currentUSDTValue < this.MINIMUM_WITHDRAWAL_USDT) {
        return {
          canWithdraw: false,
          reason: `Minimum withdrawal amount is ${this.MINIMUM_WITHDRAWAL_USDT} USDT`,
          minimumRequired: this.MINIMUM_WITHDRAWAL_USDT,
          currentUSDTValue
        };
      }

      // Check time lock
      const timeStatus = cryptoTimeService.getTimeUntilWithdrawalAllowed();
      if (!timeStatus.isAllowed) {
        return {
          canWithdraw: false,
          reason: 'Withdrawals are only allowed after 03:00 UTC+3',
          minimumRequired: this.MINIMUM_WITHDRAWAL_USDT,
          currentUSDTValue,
          timeUntilAllowed: timeStatus.totalMilliseconds
        };
      }

      return {
        canWithdraw: true,
        minimumRequired: this.MINIMUM_WITHDRAWAL_USDT,
        currentUSDTValue
      };
    } catch (error) {
      console.error('Error validating withdrawal:', error);
      throw new Error('Failed to validate withdrawal');
    }
  }

  /**
   * Process crypto deposit and create investment package
   */
  async processDeposit(depositData: {
    userId: string;
    currency: string;
    amount: number;
    transactionHash: string;
    walletAddress: string;
  }): Promise<{ depositTransaction: any; investmentPackage: any }> {
    try {
      const { userId, currency, amount, transactionHash, walletAddress } = depositData;

      // Calculate USDT value
      const conversionResult = await cryptoApiService.convertCurrency(amount, currency, 'USDT');
      
      // Create deposit transaction
      const depositTransaction = new DepositTransaction({
        userId,
        currency,
        amount,
        walletAddress,
        transactionHash,
        confirmations: 0,
        requiredConfirmations: 3,
        status: 'pending',
        usdtValue: conversionResult.amount,
        conversionRate: conversionResult.rate.rate,
        autoInvestmentEnabled: true
      });

      await depositTransaction.save();

      // Create investment package
      const investmentPackage = new InvestmentPackage({
        userId,
        amount,
        currency,
        status: 'pending',
        interestRate: this.DAILY_INTEREST_RATE,
        depositTransactionId: depositTransaction._id,
        depositCurrency: currency,
        depositAmount: amount,
        conversionRate: conversionResult.rate.rate,
        autoCreated: true,
        originalUSDTValue: conversionResult.amount,
        minimumWithdrawalUSDT: this.MINIMUM_WITHDRAWAL_USDT,
        realTimeUSDTValue: 0,
        lastUSDTUpdate: new Date()
      });

      // Set activation time (next 03:00 UTC+3)
      await investmentPackage.activate();

      // Emit deposit event
      this.emit('depositProcessed', {
        userId,
        depositTransaction,
        investmentPackage,
        usdtValue: conversionResult.amount
      });

      // Send real-time notification
      socketService.sendToUser(userId, {
        type: 'deposit_processed',
        data: {
          transactionId: depositTransaction._id,
          packageId: investmentPackage._id,
          currency,
          amount,
          usdtValue: conversionResult.amount,
          message: `Deposit of ${amount} ${currency} processed successfully!`
        }
      });

      return { depositTransaction, investmentPackage };
    } catch (error) {
      console.error('Error processing deposit:', error);
      throw new Error('Failed to process deposit');
    }
  }

  /**
   * Get investment summary for user
   */
  async getInvestmentSummary(userId: string): Promise<{
    totalInvested: number;
    totalEarned: number;
    dailyEarnings: number;
    activePackages: number;
    totalUSDTValue: number;
    canWithdraw: boolean;
    nextInterestTime: Date;
    timeUntilNext: string;
  }> {
    try {
      const packages = await InvestmentPackage.find({ userId });
      const activePackages = packages.filter(pkg => pkg.status === 'active');

      const totalInvested = packages.reduce((sum, pkg) => sum + (pkg.originalUSDTValue || 0), 0);
      const totalEarned = packages.reduce((sum, pkg) => sum + pkg.totalEarned, 0);
      const dailyEarnings = activePackages.reduce((sum, pkg) => sum + pkg.calculateDailyInterest(), 0);

      // Calculate total USDT value
      let totalUSDTValue = 0;
      for (const pkg of packages) {
        if (pkg.currency === 'USDT') {
          totalUSDTValue += pkg.totalEarned;
        } else {
          totalUSDTValue += pkg.realTimeUSDTValue || 0;
        }
      }

      const canWithdraw = totalUSDTValue >= this.MINIMUM_WITHDRAWAL_USDT && 
                         cryptoTimeService.isWithdrawalTimeAllowed();

      const nextInterestTime = cryptoTimeService.getNextInterestTime();
      const timeUntilNext = cryptoTimeService.formatTimeUntil(
        cryptoTimeService.getTimeUntilNextInterest()
      );

      return {
        totalInvested,
        totalEarned,
        dailyEarnings,
        activePackages: activePackages.length,
        totalUSDTValue,
        canWithdraw,
        nextInterestTime,
        timeUntilNext
      };
    } catch (error) {
      console.error('Error getting investment summary:', error);
      throw new Error('Failed to get investment summary');
    }
  }

  /**
   * Update real-time USDT values for all packages
   */
  async updateRealTimeUSDTValues(): Promise<void> {
    try {
      const packages = await InvestmentPackage.find({ 
        status: 'active',
        currency: { $ne: 'USDT' }
      });

      for (const pkg of packages) {
        try {
          const rate = await cryptoApiService.getExchangeRate(pkg.currency, 'USDT');
          pkg.realTimeUSDTValue = pkg.totalEarned * rate.rate;
          pkg.lastUSDTUpdate = new Date();
          await pkg.save();
        } catch (error) {
          console.warn(`Failed to update USDT value for package ${pkg._id}:`, error);
        }
      }

      console.log(`✅ Updated USDT values for ${packages.length} packages`);
    } catch (error) {
      console.error('Error updating real-time USDT values:', error);
    }
  }
}

export default new EnhancedCryptoService();
