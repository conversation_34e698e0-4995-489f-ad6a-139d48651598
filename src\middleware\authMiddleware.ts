import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { AppError, createUnauthorizedError, createForbiddenError } from '../utils/AppError';
import User from '../models/userModel';
import { catchAsync } from '../utils/catchAsync';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: any;
    }
  }
}

/**
 * Middleware to protect routes that require authentication
 */
export const protect = catchAsync(async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  // 1) Get token from Authorization header
  let token;
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }

  if (!token) {
    throw createUnauthorizedError('You are not logged in. Please log in to get access.');
  }

  // 2) Verify token
  const decoded = jwt.verify(token, process.env.JWT_SECRET as string) as any;

  // 3) Check if user still exists
  const user = await User.findById(decoded.id);
  if (!user) {
    throw createUnauthorizedError('The user belonging to this token no longer exists.');
  }

  // 4) Check if user changed password after the token was issued
  if (user.changedPasswordAfter && user.changedPasswordAfter(decoded.iat)) {
    throw createUnauthorizedError('User recently changed password. Please log in again.');
  }

  // Grant access to protected route
  req.user = user;
  next();
});

/**
 * Middleware to restrict access to specific roles
 * @param roles Array of allowed roles
 */
export const restrictTo = (...roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Check if user exists (should be set by protect middleware)
    if (!req.user) {
      return next(createUnauthorizedError());
    }
    
    // Check if user has required role
    if (!roles.includes(req.user.role)) {
      return next(createForbiddenError('You do not have permission to perform this action'));
    }
    
    next();
  };
};
