/**
 * Service for handling authentication-related functionality
 */

/**
 * Check if user is authenticated based on cookies
 * @returns True if authentication cookie exists, false otherwise
 */
export const getToken = (): string | null => {
  // We're using cookie-based auth, so we don't need to check localStorage for tokens
  // We can only check if we're logged in by checking if user data exists
  const user = localStorage.getItem('user');
  if (user) {
    try {
      const userData = JSON.parse(user);
      if (userData._id) {
        // User is authenticated
        return 'authenticated';
      }
    } catch (error) {
      console.error('Error parsing user data from localStorage:', error);
    }
  }

  return null;
};

/**
 * Check if user is authenticated
 * @returns True if user is authenticated, false otherwise
 */
export const isAuthenticated = (): boolean => {
  return getToken() !== null;
};

/**
 * Check if user is an admin
 * @returns True if user is an admin, false otherwise
 */
export const isAdmin = (): boolean => {
  // Check for admin cookie
  if (document.cookie.includes('adminToken=true')) {
    return true;
  }

  // Check localStorage
  const user = localStorage.getItem('user');
  if (user) {
    try {
      const userData = JSON.parse(user);
      return userData.isAdmin === true;
    } catch (error) {
      console.error('Error parsing user data from localStorage:', error);
    }
  }

  return false;
};

/**
 * Get user ID from authentication data
 * @returns User ID or null if not found
 */
export const getUserId = (): string | null => {
  const user = localStorage.getItem('user');
  if (user) {
    try {
      const userData = JSON.parse(user);
      return userData._id || null;
    } catch (error) {
      console.error('Error parsing user data from localStorage:', error);
    }
  }
  return null;
};
