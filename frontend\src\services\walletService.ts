import { apiClient } from '../utils/apiClient';

/**
 * API service for wallet-related endpoints
 */
export const walletService = {
  /**
   * Connect wallet
   * @param address Wallet address
   */
  connectWallet: (address: string) => apiClient.post('/wallets/connect', { address }),

  /**
   * Get wallet balance
   */
  getWalletBalance: () => apiClient.get('/wallets/balance'),

  /**
   * Toggle between commission and interest mode
   * @param asset Asset symbol
   * @param mode Mode ('commission' or 'interest')
   */
  toggleMode: (asset: string, mode: 'commission' | 'interest') =>
    apiClient.post('/wallets/toggle-mode', { asset, mode }),

  /**
   * Deposit asset
   * @param data Deposit data
   */
  depositAsset: (data: {
    asset: string;
    amount: number;
    txHash?: string;
    network?: string;
  }) => apiClient.post('/wallets/deposit', data),

  /**
   * Withdraw asset
   * @param data Withdrawal data
   */
  withdrawAsset: (data: {
    asset: string;
    amount: number;
    address: string;
    memo?: string;
    withdrawalType: 'main' | 'interest' | 'commission';
    network?: string;
    blockchainNetwork?: string;
    txHash?: string;
  }) => apiClient.post('/wallets/withdraw', data),

  /**
   * Get transaction history
   * @param params Optional query parameters
   */
  getTransactionHistory: (params?: {
    page?: number;
    limit?: number;
    type?: string;
    asset?: string;
  }) => apiClient.get('/wallets/transactions', { params }),
};

export default walletService;
