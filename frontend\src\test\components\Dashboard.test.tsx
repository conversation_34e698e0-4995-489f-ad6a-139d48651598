// Temporarily disabled for compilation
/*
import { describe, it, expect, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>Provider } from '@chakra-ui/react';
import { BrowserRouter } from 'react-router-dom';
import { AuthProvider } from '../../context/AuthContext';
import Dashboard from '../../pages/Dashboard';
import userEvent from '@testing-library/user-event';

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      <AuthProvider>
        <ChakraProvider>
          {component}
        </ChakraProvider>
      </AuthProvider>
    </BrowserRouter>
  );
};

describe('Dashboard Component', () => {
  beforeEach(() => {
    // Mock localStorage
    const mockUser = {
      _id: '123',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      token: 'mock-jwt-token'
    };
    localStorage.setItem('user', JSON.stringify(mockUser));
  });

  it('renders dashboard with wallet balance', async () => {
    renderWithProviders(<Dashboard />);

    // Wait for wallet data to load
    await waitFor(() => {
      expect(screen.getByText(/Total Commission Earned/i)).toBeInTheDocument();
    });

    // Check if assets are displayed
    expect(screen.getByText('BTC')).toBeInTheDocument();
    expect(screen.getByText('ETH')).toBeInTheDocument();

    // Verify balances
    expect(screen.getByText('0.5 BTC')).toBeInTheDocument();
    expect(screen.getByText('5 ETH')).toBeInTheDocument();
  });

  it('handles mode switching correctly', async () => {
    renderWithProviders(<Dashboard />);
    const user = userEvent.setup();

    // Wait for content to load
    await waitFor(() => {
      expect(screen.getByText(/Commission Mode/i)).toBeInTheDocument();
    });

    // Find and click mode toggle button
    const toggleButton = screen.getByRole('button', { name: /switch to interest mode/i });
    await user.click(toggleButton);

    // Verify mode change
    await waitFor(() => {
      expect(screen.getByText(/Interest Mode/i)).toBeInTheDocument();
    });
  });

  it('shows correct commission calculations', async () => {
    renderWithProviders(<Dashboard />);

    // Wait for commission data to load
    await waitFor(() => {
      expect(screen.getByText('0.005 BTC')).toBeInTheDocument();
    });

    // Verify commission details
    expect(screen.getByText(/Commission Rate: 1%/i)).toBeInTheDocument();
    expect(screen.getByText(/Total Commission Earned/i)).toBeInTheDocument();
  });

  it('displays transaction history', async () => {
    renderWithProviders(<Dashboard />);

    // Wait for transactions to load
    await waitFor(() => {
      expect(screen.getByText(/Recent Transactions/i)).toBeInTheDocument();
    });

    // Verify transaction details
    const transactions = screen.getAllByRole('row');
    expect(transactions.length).toBeGreaterThan(1); // Header + at least one transaction

    // Check transaction type
    expect(screen.getByText(/deposit/i)).toBeInTheDocument();
    expect(screen.getByText(/commission/i)).toBeInTheDocument();
  });

  it('shows loading state initially', () => {
    renderWithProviders(<Dashboard />);
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('handles error states appropriately', async () => {
    // Clear auth token to trigger error
    localStorage.clear();
    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      expect(screen.getByText(/please connect your wallet/i)).toBeInTheDocument();
    });
  });
});
*/