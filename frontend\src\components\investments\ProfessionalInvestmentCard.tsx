import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardBody,
  VStack,
  HStack,
  Text,
  Badge,
  Button,
  Progress,
  Divider,
  Icon,
  Flex,
  Tooltip,
  useColorModeValue,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Grid,
  GridItem,
  CircularProgress,
  CircularProgressLabel,
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import {
  FaCoins,
  FaChartLine,
  FaCalendarAlt,
  FaPercentage,
  FaShieldAlt,
  FaTrophy,
  FaInfoCircle,
  FaArrowUp,
  FaArrowDown,
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { formatCurrency } from '../../utils/formatters';
import { getCryptoIcon, getCryptoColor } from '../../utils/cryptoIcons';

const MotionCard = motion(Card);

interface InvestmentPackage {
  id: string;
  currency: string;
  amount: number;
  totalEarned: number;
  dailyInterest: number;
  status: 'pending' | 'active' | 'completed' | 'withdrawn';
  activatedAt: Date;
  nextInterestTime: Date;
  withdrawalEligibleTime: Date;
  minimumWithdrawalUSDT: number;
  realTimeUSDTValue: number;
  activeDays: number;
  canWithdraw: boolean;
  interestRate: number;
  tier?: string;
  annualRate?: number;
  projectedEarnings?: number;
  riskLevel?: 'low' | 'medium' | 'high';
  marketCondition?: string;
}

interface ProfessionalInvestmentCardProps {
  package: InvestmentPackage;
  onWithdraw?: (packageId: string) => void;
  onViewDetails?: (packageId: string) => void;
  onReinvest?: (packageId: string) => void;
}

const ProfessionalInvestmentCard: React.FC<ProfessionalInvestmentCardProps> = ({
  package: pkg,
  onWithdraw,
  onViewDetails,
  onReinvest,
}) => {
  const { t } = useTranslation();
  const [timeToNextInterest, setTimeToNextInterest] = useState<string>('');

  // Theme colors - Binance inspired
  const bgColor = useColorModeValue('#FFFFFF', '#1E2026');
  const cardBgColor = useColorModeValue('#FAFAFA', '#1E2026');
  const borderColor = useColorModeValue('#E2E8F0', '#2D3748');
  const textColor = useColorModeValue('#1A202C', '#EAECEF');
  const secondaryTextColor = useColorModeValue('#718096', '#848E9C');
  const primaryColor = '#FCD535';
  const successColor = '#02C076';
  const errorColor = '#F84960';

  // Calculate performance metrics
  const totalReturn = pkg.amount + pkg.totalEarned;
  const returnPercentage = ((pkg.totalEarned / pkg.amount) * 100);
  const dailyReturnPercentage = (pkg.interestRate * 100);
  const annualReturnPercentage = (pkg.annualRate || pkg.interestRate * 365) * 100;

  // Get tier information
  const getTierInfo = (tier?: string) => {
    const tiers = {
      tier1: { name: 'Starter', color: '#718096', icon: FaCoins },
      tier2: { name: 'Bronze', color: '#CD7F32', icon: FaShieldAlt },
      tier3: { name: 'Silver', color: '#C0C0C0', icon: FaChartLine },
      tier4: { name: 'Gold', color: '#FFD700', icon: FaTrophy },
      tier5: { name: 'Platinum', color: '#E5E4E2', icon: FaTrophy },
    };
    return tiers[tier as keyof typeof tiers] || tiers.tier1;
  };

  const tierInfo = getTierInfo(pkg.tier);

  // Status configuration
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'active':
        return { color: 'green', label: t('investment.active', 'Active'), bg: successColor };
      case 'pending':
        return { color: 'yellow', label: t('investment.pending', 'Pending'), bg: primaryColor };
      case 'completed':
        return { color: 'blue', label: t('investment.completed', 'Completed'), bg: '#3375BB' };
      case 'withdrawn':
        return { color: 'gray', label: t('investment.withdrawn', 'Withdrawn'), bg: '#718096' };
      default:
        return { color: 'gray', label: status, bg: '#718096' };
    }
  };

  const statusConfig = getStatusConfig(pkg.status);

  // Risk level configuration
  const getRiskConfig = (risk?: string) => {
    switch (risk) {
      case 'low':
        return { color: successColor, label: t('investment.lowRisk', 'Low Risk'), icon: FaShieldAlt };
      case 'medium':
        return { color: primaryColor, label: t('investment.mediumRisk', 'Medium Risk'), icon: FaInfoCircle };
      case 'high':
        return { color: errorColor, label: t('investment.highRisk', 'High Risk'), icon: FaArrowUp };
      default:
        return { color: primaryColor, label: t('investment.mediumRisk', 'Medium Risk'), icon: FaInfoCircle };
    }
  };

  const riskConfig = getRiskConfig(pkg.riskLevel);

  // Update countdown timer
  useEffect(() => {
    const updateTimer = () => {
      const now = new Date();
      const nextInterest = new Date(pkg.nextInterestTime);
      const diff = nextInterest.getTime() - now.getTime();

      if (diff > 0) {
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        setTimeToNextInterest(`${hours}h ${minutes}m`);
      } else {
        setTimeToNextInterest(t('investment.calculating', 'Calculating...'));
      }
    };

    updateTimer();
    const interval = setInterval(updateTimer, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [pkg.nextInterestTime, t]);

  return (
    <MotionCard
      bg={bgColor}
      borderColor={borderColor}
      borderWidth="1px"
      borderRadius="xl"
      overflow="hidden"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      _hover={{
        transform: 'translateY(-4px)',
        boxShadow: `0 8px 25px rgba(252, 213, 53, 0.15)`,
        borderColor: primaryColor
      }}
      position="relative"
    >
      {/* Status indicator */}
      <Box
        position="absolute"
        top={0}
        left={0}
        right={0}
        height="4px"
        bg={statusConfig.bg}
      />

      <CardBody p={6}>
        <VStack spacing={5} align="stretch">
          {/* Header */}
          <Flex justify="space-between" align="center">
            <HStack spacing={3}>
              <Box
                bg={`${getCryptoColor(pkg.currency, primaryColor)}20`}
                p={3}
                borderRadius="lg"
              >
                <Icon
                  as={getCryptoIcon(pkg.currency)}
                  color={getCryptoColor(pkg.currency, primaryColor)}
                  boxSize={6}
                />
              </Box>
              <VStack align="start" spacing={0}>
                <Text fontWeight="bold" color={textColor} fontSize="lg">
                  {pkg.currency} Investment
                </Text>
                <HStack spacing={2}>
                  <Badge
                    colorScheme={statusConfig.color}
                    variant="solid"
                    borderRadius="md"
                  >
                    {statusConfig.label}
                  </Badge>
                  <Badge
                    bg={`${tierInfo.color}20`}
                    color={tierInfo.color}
                    borderRadius="md"
                  >
                    <Icon as={tierInfo.icon} mr={1} boxSize={3} />
                    {tierInfo.name}
                  </Badge>
                </HStack>
              </VStack>
            </HStack>
            
            <VStack align="end" spacing={0}>
              <Text color={secondaryTextColor} fontSize="sm">
                {t('investment.totalValue', 'Total Value')}
              </Text>
              <Text color={textColor} fontWeight="bold" fontSize="xl">
                ${pkg.realTimeUSDTValue.toFixed(2)}
              </Text>
            </VStack>
          </Flex>

          {/* Performance Overview */}
          <Grid templateColumns="repeat(2, 1fr)" gap={4}>
            <GridItem>
              <Stat>
                <StatLabel color={secondaryTextColor} fontSize="sm">
                  {t('investment.principal', 'Principal')}
                </StatLabel>
                <StatNumber color={textColor} fontSize="lg">
                  {formatCurrency(pkg.amount, pkg.currency)}
                </StatNumber>
              </Stat>
            </GridItem>
            <GridItem>
              <Stat>
                <StatLabel color={secondaryTextColor} fontSize="sm">
                  {t('investment.totalEarned', 'Total Earned')}
                </StatLabel>
                <StatNumber color={successColor} fontSize="lg">
                  {formatCurrency(pkg.totalEarned, pkg.currency)}
                </StatNumber>
                <StatHelpText color={successColor}>
                  <StatArrow type="increase" />
                  {returnPercentage.toFixed(2)}%
                </StatHelpText>
              </Stat>
            </GridItem>
          </Grid>

          <Divider borderColor={borderColor} />

          {/* Interest Information */}
          <VStack spacing={3} align="stretch">
            <HStack justify="space-between">
              <HStack>
                <Icon as={FaPercentage} color={primaryColor} />
                <Text color={textColor} fontSize="sm">
                  {t('investment.dailyRate', 'Daily Rate')}
                </Text>
              </HStack>
              <Text color={successColor} fontWeight="bold">
                {dailyReturnPercentage.toFixed(3)}%
              </Text>
            </HStack>

            <HStack justify="space-between">
              <HStack>
                <Icon as={FaChartLine} color={primaryColor} />
                <Text color={textColor} fontSize="sm">
                  {t('investment.annualRate', 'Annual Rate')}
                </Text>
              </HStack>
              <Text color={successColor} fontWeight="bold">
                {annualReturnPercentage.toFixed(1)}% APY
              </Text>
            </HStack>

            <HStack justify="space-between">
              <HStack>
                <Icon as={FaCalendarAlt} color={primaryColor} />
                <Text color={textColor} fontSize="sm">
                  {t('investment.activeDays', 'Active Days')}
                </Text>
              </HStack>
              <Text color={textColor} fontWeight="bold">
                {pkg.activeDays}
              </Text>
            </HStack>
          </VStack>

          {/* Next Interest Timer */}
          {pkg.status === 'active' && (
            <Box
              bg={cardBgColor}
              p={4}
              borderRadius="lg"
              borderWidth="1px"
              borderColor={borderColor}
            >
              <HStack justify="space-between" align="center">
                <VStack align="start" spacing={1}>
                  <Text color={secondaryTextColor} fontSize="sm">
                    {t('investment.nextInterest', 'Next Interest')}
                  </Text>
                  <Text color={textColor} fontWeight="bold">
                    {timeToNextInterest}
                  </Text>
                </VStack>
                <CircularProgress
                  value={75} // This would be calculated based on time remaining
                  color={primaryColor}
                  size="50px"
                  thickness="8px"
                >
                  <CircularProgressLabel fontSize="xs" color={textColor}>
                    75%
                  </CircularProgressLabel>
                </CircularProgress>
              </HStack>
            </Box>
          )}

          {/* Action Buttons */}
          <HStack spacing={3}>
            <Button
              variant="outline"
              colorScheme="gray"
              size="sm"
              flex={1}
              onClick={() => onViewDetails?.(pkg.id)}
            >
              {t('investment.viewDetails', 'Details')}
            </Button>
            
            {pkg.canWithdraw && (
              <Button
                colorScheme="red"
                size="sm"
                flex={1}
                onClick={() => onWithdraw?.(pkg.id)}
              >
                {t('investment.withdraw', 'Withdraw')}
              </Button>
            )}
            
            {pkg.status === 'active' && (
              <Button
                bg={primaryColor}
                color="black"
                size="sm"
                flex={1}
                _hover={{ bg: '#E6C200' }}
                onClick={() => onReinvest?.(pkg.id)}
              >
                {t('investment.reinvest', 'Reinvest')}
              </Button>
            )}
          </HStack>
        </VStack>
      </CardBody>
    </MotionCard>
  );
};

export default ProfessionalInvestmentCard;
