import mongoose, { Document, Schema } from 'mongoose';

export interface IWithdrawalTransaction extends Document {
  userId: mongoose.Types.ObjectId;
  currency: string;
  amount: number;
  targetAddress: string;
  withdrawalType: 'interest' | 'commission' | 'main';
  status: 'pending' | 'processing' | 'approved' | 'rejected' | 'completed' | 'failed';
  txHash?: string;
  blockchainNetwork?: string;
  networkFee?: number;
  usdtValue?: number;
  conversionRate?: number;
  adminNotes?: string;
  rejectionReason?: string;
  createdAt: Date;
  updatedAt: Date;
  processedAt?: Date;
  completedAt?: Date;
  
  // Balance validation
  availableBalance?: number;
  minimumWithdrawal?: number;
  
  // Admin tracking
  approvedBy?: mongoose.Types.ObjectId;
  processedBy?: mongoose.Types.ObjectId;
  
  // Instance methods
  canProcess(): boolean;
  markAsProcessed(): Promise<IWithdrawalTransaction>;
  calculateNetworkFee(): Promise<number>;
  validateBalance(): Promise<boolean>;
}

const withdrawalTransactionSchema = new Schema<IWithdrawalTransaction>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
      index: true
    },
    currency: {
      type: String,
      required: [true, 'Currency is required'],
      uppercase: true,
      enum: ['BTC', 'ETH', 'USDT', 'BNB', 'DOGE', 'TRX'],
      index: true
    },
    amount: {
      type: Number,
      required: [true, 'Amount is required'],
      min: [0.000001, 'Amount must be positive'],
      validate: {
        validator: function(value: number) {
          return value > 0;
        },
        message: 'Amount must be positive'
      }
    },
    targetAddress: {
      type: String,
      required: [true, 'Target address is required'],
      trim: true,
      index: true
    },
    withdrawalType: {
      type: String,
      enum: ['interest', 'commission', 'main'],
      required: [true, 'Withdrawal type is required'],
      default: 'interest'
    },
    status: {
      type: String,
      enum: ['pending', 'processing', 'approved', 'rejected', 'completed', 'failed'],
      default: 'pending',
      index: true
    },
    txHash: {
      type: String,
      trim: true,
      sparse: true, // Allow multiple null values
      index: true
    },
    blockchainNetwork: {
      type: String,
      enum: ['ethereum', 'bsc', 'tron'],
      default: 'ethereum'
    },
    networkFee: {
      type: Number,
      min: 0,
      default: 0
    },
    usdtValue: {
      type: Number,
      min: 0
    },
    conversionRate: {
      type: Number,
      min: 0
    },
    adminNotes: {
      type: String,
      trim: true,
      maxlength: 1000
    },
    rejectionReason: {
      type: String,
      trim: true,
      maxlength: 500
    },
    processedAt: {
      type: Date,
      index: true
    },
    completedAt: {
      type: Date,
      index: true
    },
    availableBalance: {
      type: Number,
      min: 0
    },
    minimumWithdrawal: {
      type: Number,
      min: 0,
      default: 50 // 50 USDT equivalent
    },
    approvedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    processedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Indexes for performance
withdrawalTransactionSchema.index({ userId: 1, status: 1 });
withdrawalTransactionSchema.index({ currency: 1, status: 1 });
withdrawalTransactionSchema.index({ createdAt: -1 });
withdrawalTransactionSchema.index({ processedAt: -1 });
withdrawalTransactionSchema.index({ withdrawalType: 1 });

// Virtual for time since creation
withdrawalTransactionSchema.virtual('timeSinceCreation').get(function() {
  return Date.now() - this.createdAt.getTime();
});

// Virtual for processing time
withdrawalTransactionSchema.virtual('processingTime').get(function() {
  if (!this.processedAt) return null;
  return this.processedAt.getTime() - this.createdAt.getTime();
});

// Virtual for status display
withdrawalTransactionSchema.virtual('statusDisplay').get(function() {
  const statusMap = {
    pending: 'Pending Review',
    processing: 'Processing',
    approved: 'Approved',
    rejected: 'Rejected',
    completed: 'Completed',
    failed: 'Failed'
  };
  return statusMap[this.status] || this.status;
});

// Instance method to check if withdrawal can be processed
withdrawalTransactionSchema.methods.canProcess = function(): boolean {
  return this.status === 'approved' && !this.processedAt;
};

// Instance method to mark as processed
withdrawalTransactionSchema.methods.markAsProcessed = async function(): Promise<IWithdrawalTransaction> {
  this.status = 'processing';
  this.processedAt = new Date();
  return await this.save();
};

// Instance method to calculate network fee
withdrawalTransactionSchema.methods.calculateNetworkFee = async function(): Promise<number> {
  // Basic fee calculation - can be enhanced with real-time fee estimation
  const feeRates = {
    BTC: 0.0005,
    ETH: 0.005,
    USDT: 1.0,
    BNB: 0.001,
    DOGE: 1.0,
    TRX: 1.0
  };
  
  return feeRates[this.currency as keyof typeof feeRates] || 0;
};

// Instance method to validate balance
withdrawalTransactionSchema.methods.validateBalance = async function(): Promise<boolean> {
  if (!this.availableBalance) return false;
  return this.availableBalance >= this.amount;
};

// Pre-save middleware
withdrawalTransactionSchema.pre('save', async function(next) {
  // Calculate network fee if not set
  if (!this.networkFee) {
    this.networkFee = await this.calculateNetworkFee();
  }
  
  // Set processed timestamp when status changes to processing
  if (this.isModified('status') && this.status === 'processing' && !this.processedAt) {
    this.processedAt = new Date();
  }
  
  // Set completed timestamp when status changes to completed
  if (this.isModified('status') && this.status === 'completed' && !this.completedAt) {
    this.completedAt = new Date();
  }
  
  next();
});

// Static methods interface
interface IWithdrawalTransactionModel extends mongoose.Model<IWithdrawalTransaction> {
  getPendingWithdrawals(): Promise<IWithdrawalTransaction[]>;
  getUserWithdrawals(userId: string): Promise<IWithdrawalTransaction[]>;
  getWithdrawalsByStatus(status: string): Promise<IWithdrawalTransaction[]>;
  getTotalWithdrawalsByUser(userId: string): Promise<any[]>;
}

// Static method to get pending withdrawals
withdrawalTransactionSchema.statics.getPendingWithdrawals = function() {
  return this.find({
    status: 'pending'
  }).populate('userId', 'email firstName lastName');
};

// Static method to get user withdrawals
withdrawalTransactionSchema.statics.getUserWithdrawals = function(userId: string) {
  return this.find({ userId })
    .sort({ createdAt: -1 });
};

// Static method to get withdrawals by status
withdrawalTransactionSchema.statics.getWithdrawalsByStatus = function(status: string) {
  return this.find({ status })
    .populate('userId', 'email firstName lastName')
    .sort({ createdAt: -1 });
};

// Static method to get total withdrawals by user
withdrawalTransactionSchema.statics.getTotalWithdrawalsByUser = function(userId: string) {
  return this.aggregate([
    { $match: { userId: new mongoose.Types.ObjectId(userId), status: { $in: ['completed', 'approved'] } } },
    {
      $group: {
        _id: '$currency',
        totalAmount: { $sum: '$amount' },
        totalUSDTValue: { $sum: '$usdtValue' },
        count: { $sum: 1 }
      }
    }
  ]);
};

const WithdrawalTransaction = mongoose.model<IWithdrawalTransaction, IWithdrawalTransactionModel>(
  'WithdrawalTransaction',
  withdrawalTransactionSchema
);

export default WithdrawalTransaction;
