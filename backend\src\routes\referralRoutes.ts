import express from 'express';
import { getReferralInfo, applyReferralCode, getReferralStats } from '../controllers/referralController';
import { protect, admin } from '../middleware/authMiddleware';

const router = express.Router();

// Get user's referral information
router.get('/info', protect, getReferralInfo);

// Apply referral code
router.post('/apply', protect, applyReferralCode);

// Get referral statistics (admin only)
router.get('/stats', protect, admin, getReferralStats);

export default router;
