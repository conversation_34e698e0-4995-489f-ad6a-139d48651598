# Node environment
NODE_ENV=development

# Server port
PORT=5001

# MongoDB connection
MONGO_URI=mongodb://localhost:27017/cryptoyield
MONGO_USER=
MONGO_PASSWORD=

# Redis configuration
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT configuration
JWT_SECRET=crypto_yield_hub_dev_jwt_secret
JWT_EXPIRES_IN=1d
JWT_REFRESH_EXPIRES_IN=7d

# CORS
FRONTEND_URL=http://localhost:3003

# Blockchain
CONTRACT_ADDRESS=******************************************
PROVIDER_URL=https://mainnet.infura.io/v3/********************************

# Logging
LOG_LEVEL=info

# Rate limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=100

# Cache
CACHE_TTL=300
