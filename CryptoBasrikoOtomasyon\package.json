{"name": "cryptobasriko-otomasyon", "version": "1.0.0", "description": "CryptoBasriko <PERSON>yon Uygulaması - Kripto varlık yönetimi ve komisyon/faiz hesaplama", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "pack": "electron-builder --dir", "dist": "electron-builder"}, "keywords": ["crypto", "blockchain", "otomas<PERSON>", "komisyon", "faiz"], "author": "CryptoBasriko", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.6.4"}, "build": {"appId": "com.cryptobasriko.otomasyon", "productName": "<PERSON>pto<PERSON><PERSON><PERSON><PERSON>", "directories": {"output": "dist"}, "win": {"target": ["nsis"], "icon": "assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "dependencies": {"cryptobasriko-otomasyon": "file:"}}