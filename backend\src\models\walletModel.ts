import mongoose, { Document, Schema } from 'mongoose';

export interface IWallet extends Document {
  userId: mongoose.Types.ObjectId;
  assets: {
    symbol: string;
    balance: number;
    commissionBalance: number;
    interestBalance: number;
    mode: 'commission' | 'interest';
    network?: string;
    address?: string;
  }[];
  totalCommissionEarned: number;
  totalInterestEarned: number;
  createdAt: Date;
  updatedAt: Date;
}

const walletSchema = new Schema<IWallet>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    assets: [
      {
        symbol: {
          type: String,
          required: true,
          trim: true,
          uppercase: true,
        },
        balance: {
          type: Number,
          required: true,
          default: 0,
        },
        commissionBalance: {
          type: Number,
          default: 0,
        },
        interestBalance: {
          type: Number,
          default: 0,
        },
        mode: {
          type: String,
          enum: ['commission', 'interest'],
        },
        network: {
          type: String,
          trim: true,
        },
        address: {
          type: String,
          trim: true,
        },
      },
    ],
    totalCommissionEarned: {
      type: Number,
      default: 0,
    },
    totalInterestEarned: {
      type: Number,
      default: 0,
    },
  },
  {
    timestamps: true,
  }
);

const Wallet = mongoose.model<IWallet>('Wallet', walletSchema);

export default Wallet;
