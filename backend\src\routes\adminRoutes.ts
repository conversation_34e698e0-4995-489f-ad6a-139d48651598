import express from 'express';
import {
  adminLogin,
  checkAdminStatus,
  getUsers,
  getUserById,
  updateUser,
  deleteUser,
  toggleAdminStatus,
  loginAsUser,
  getAdminDeposits,
  updateDepositStatus,
  getAdminWithdrawals,
  updateWithdrawalStatus,
  getAdminTransactions
} from '../controllers/adminController';
import { updateTransactionStatus } from '../controllers/transactionController';
import {
  getSystemConfig,
  updateSystemConfig,
  getCryptoAddresses,
  updateCryptoAddresses,
} from '../controllers/systemConfigController';
import { protect, admin } from '../middleware/authMiddleware';
import { wrapController } from '../utils/routeWrapper';
import adminReferralRoutes from './adminReferralRoutes';

const router = express.Router();

// Public admin routes
router.post('/login', wrapController(adminLogin));

// Admin check routes
router.get('/check', protect, admin, wrapController(checkAdminStatus));
router.get('/check-auth', protect, admin, wrapController(checkAdminStatus));

// Apply admin middleware to protected routes
// Instead of using router.use, we'll apply middleware to each route individually

// User management routes
router.route('/users')
  .get(protect, admin, wrapController(getUsers));

router.route('/users/:id')
  .get(protect, admin, wrapController(getUserById))
  .put(protect, admin, wrapController(updateUser))
  .delete(protect, admin, wrapController(deleteUser));

router.route('/users/:id/toggle-admin')
  .put(protect, admin, wrapController(toggleAdminStatus));

router.route('/users/:id/login-as')
  .post(protect, admin, wrapController(loginAsUser));

// Deposit management routes
router.route('/deposits')
  .get(protect, admin, wrapController(getAdminDeposits));

router.route('/deposits/:id/status')
  .put(protect, admin, wrapController(updateDepositStatus));

// Withdrawal management routes
router.route('/withdrawals')
  .get(protect, admin, wrapController(getAdminWithdrawals));

// Withdrawal status update route
router.route('/withdrawals/:id/status')
  .put(protect, admin, wrapController(updateWithdrawalStatus));

// Transaction management routes
router.route('/transactions')
  .get(protect, admin, wrapController(getAdminTransactions));

// Transaction status update route
router.route('/transactions/:id/status')
  .put(protect, admin, wrapController(updateTransactionStatus));

// System configuration routes (admin only)
router.route('/system/config')
  .get(protect, admin, wrapController(getSystemConfig))
  .put(protect, admin, wrapController(updateSystemConfig));

// Crypto addresses management routes (admin only)
router.route('/system/crypto-addresses')
  .get(protect, admin, wrapController(getCryptoAddresses))
  .put(protect, admin, wrapController(updateCryptoAddresses));

// Specific crypto address routes (admin only)
router.route('/system/crypto-addresses/:currency')
  .get(protect, admin, wrapController(getCryptoAddresses))
  .put(protect, admin, wrapController(updateCryptoAddresses));

// Public crypto addresses route for home page display
router.route('/crypto-addresses')
  .get(wrapController(getCryptoAddresses));

// Use referral routes
router.use('/referrals', adminReferralRoutes);

export default router;
