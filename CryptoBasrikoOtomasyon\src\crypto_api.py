import requests
import json
import logging
import time
from datetime import datetime

class CryptoAPI:
    """Kripto para API'leri ile iletişim kuran sınıf"""
    
    def __init__(self, api_key, api_secret):
        self.api_key = api_key
        self.api_secret = api_secret
        self.base_url = "https://api.binance.com"
        self.logger = logging.getLogger('CryptoAPI')
        self.logger.info("CryptoAPI başlatıldı")
        
    def get_current_prices(self):
        """Güncel kripto para fiyatlarını getir"""
        try:
            # Demo modunda gerçek API çağrısı yapmak yerine örnek veri döndürüyoruz
            self.logger.info("Güncel fiyatlar alınıyor...")
            
            # Gerçek uygulamada burada API çağrısı yapılır
            # response = requests.get(f"{self.base_url}/api/v3/ticker/price")
            # return response.json()
            
            # Demo için örnek veri
            return {
                "BTC": 60000.0,
                "ETH": 3000.0,
                "BNB": 500.0,
                "SOL": 150.0,
                "ADA": 1.2,
                "USDT": 1.0
            }
        except Exception as e:
            self.logger.error(f"Fiyat alınırken hata: {str(e)}")
            return {}
    
    def get_historical_prices(self, symbol, interval, limit=100):
        """Geçmiş fiyat verilerini getir"""
        try:
            self.logger.info(f"{symbol} için geçmiş fiyatlar alınıyor...")
            
            # Gerçek uygulamada burada API çağrısı yapılır
            # params = {
            #     'symbol': f"{symbol}USDT",
            #     'interval': interval,
            #     'limit': limit
            # }
            # response = requests.get(f"{self.base_url}/api/v3/klines", params=params)
            # return response.json()
            
            # Demo için örnek veri
            current_price = self.get_current_prices().get(symbol, 100)
            data = []
            for i in range(limit):
                # Basit bir fiyat simülasyonu
                price = current_price * (0.95 + 0.1 * (i % 10) / 10)
                timestamp = int(time.time() * 1000) - (limit - i) * 3600 * 1000
                data.append([
                    timestamp,  # Open time
                    price * 0.99,  # Open
                    price * 1.01,  # High
                    price * 0.98,  # Low
                    price,  # Close
                    1000 + i * 10,  # Volume
                    timestamp + 3600 * 1000,  # Close time
                    price * (1000 + i * 10),  # Quote asset volume
                    100 + i,  # Number of trades
                    500 + i * 5,  # Taker buy base asset volume
                    price * (500 + i * 5),  # Taker buy quote asset volume
                    0  # Ignore
                ])
            return data
        except Exception as e:
            self.logger.error(f"Geçmiş fiyatlar alınırken hata: {str(e)}")
            return []
    
    def place_order(self, user_id, symbol, side, quantity):
        """Alım/satım emri ver (demo)"""
        try:
            self.logger.info(f"Emir veriliyor: {user_id}, {symbol}, {side}, {quantity}")
            
            # Gerçek uygulamada burada API çağrısı yapılır
            # params = {
            #     'symbol': f"{symbol}USDT",
            #     'side': side,
            #     'type': 'MARKET',
            #     'quantity': quantity
            # }
            # response = requests.post(f"{self.base_url}/api/v3/order", params=params, headers={'X-MBX-APIKEY': self.api_key})
            # return response.json()
            
            # Demo için örnek veri
            order_id = int(time.time() * 1000)
            price = self.get_current_prices().get(symbol, 100)
            
            return {
                "symbol": f"{symbol}USDT",
                "orderId": order_id,
                "clientOrderId": f"demo_{order_id}",
                "transactTime": int(time.time() * 1000),
                "price": price,
                "origQty": quantity,
                "executedQty": quantity,
                "status": "FILLED",
                "timeInForce": "GTC",
                "type": "MARKET",
                "side": side
            }
        except Exception as e:
            self.logger.error(f"Emir verilirken hata: {str(e)}")
            return {"error": str(e)}
