import { Request, Response } from 'express';
import mongoose from 'mongoose';
import Wallet from '../models/walletModel';
import Transaction from '../models/transactionModel';
import User from '../models/userModel';
import { logger } from '../utils/logger';
import { catchAsync } from '../utils/errorHandler';
import { AppError } from '../utils/AppError';
import { cacheService } from '../services/cacheService';
import { notificationService } from '../services/notificationService';
import { createTransaction, updateTransaction } from '../utils/transactionUtils';
import { CRYPTO_NETWORKS, getNetworkById } from '../utils/cryptoNetworks';
import walletService from '../services/walletService';
import depositMonitorService from '../services/depositMonitorService';

// @desc    Initialize user wallet
// @route   POST /api/wallets/connect
// @access  Private
export const connectWallet = async (req: Request, res: Response): Promise<void> => {
  try {
    const { address, asset, network } = req.body;

    // Check if wallet already exists
    let wallet = await Wallet.findOne({ userId: req.user._id });

    if (wallet) {
      // If asset and network are provided, add or update the asset
      if (asset && network) {
        const assetIndex = wallet.assets.findIndex(a => a.symbol === asset);
        if (assetIndex === -1) {
          // Add new asset
          wallet.assets.push({
            symbol: asset,
            balance: 0,
            commissionBalance: 0,
            interestBalance: 0,
            mode: 'commission',
            network,
            address: address || undefined
          });
        } else {
          // Update existing asset
          if (address) wallet.assets[assetIndex].address = address;
          if (network) wallet.assets[assetIndex].network = network;
        }
        await wallet.save();
        logger.info(`Asset ${asset} updated for user: ${req.user._id}`);
      }
    } else {
      // Create new wallet
      const assets = [];
      if (asset && network) {
        assets.push({
          symbol: asset,
          balance: 0,
          commissionBalance: 0,
          interestBalance: 0,
          mode: 'commission',
          network,
          address: address || undefined
        });
      }

      wallet = await Wallet.create({
        userId: req.user._id,
        assets,
        totalCommissionEarned: 0,
        totalInterestEarned: 0
      });
      logger.info(`New wallet created for user: ${req.user._id}`);
    }

    // If address is provided, update user's wallet address
    if (address && /^0x[a-fA-F0-9]{40}$/.test(address)) {
      await User.findByIdAndUpdate(req.user._id, { walletAddress: address });
    }

    res.status(201).json(wallet);
  } catch (error: any) {
    logger.error('Wallet connection error:', error);
    res.status(500).json({
      message: 'An error occurred while connecting the wallet',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Get wallet balance
// @route   GET /api/wallets/balance
// @access  Private
export const getWalletBalance = catchAsync(async (req: Request, res: Response) => {
  const cacheKey = `wallet:balance:${req.user._id}`;

  // Try to get from cache first
  const cachedWallet = cacheService.get(cacheKey);
  if (cachedWallet) {
    return res.json(cachedWallet);
  }

  // Find or create wallet
  let wallet = await Wallet.findOne({ userId: req.user._id });

  if (!wallet) {
    // Create a new wallet with default values
    wallet = await Wallet.create({
      userId: req.user._id,
      assets: [],
      totalCommissionEarned: 0,
      totalInterestEarned: 0
    });

    logger.info(`Auto-created wallet for user: ${req.user._id}`);
  }

  // Cache the wallet for 5 minutes
  cacheService.set(cacheKey, wallet, 300);

  res.json(wallet);
});

// @desc    Toggle between commission and interest mode
// @route   POST /api/wallets/toggle-mode
// @access  Private
export const toggleMode = async (req: Request, res: Response): Promise<void> => {
  try {
    const { asset, mode } = req.body;

    if (!['commission', 'interest'].includes(mode)) {
      res.status(400).json({ message: 'Invalid mode' });
      return;
    }

    // Find or create wallet
    let wallet = await Wallet.findOne({ userId: req.user._id });

    if (!wallet) {
      // Create a new wallet with default values
      wallet = await Wallet.create({
        userId: req.user._id,
        assets: [],
        totalCommissionEarned: 0,
        totalInterestEarned: 0
      });

      logger.info(`Auto-created wallet for user: ${req.user._id} during toggle mode request`);
    }

    // Find the asset in the wallet
    const assetIndex = wallet.assets.findIndex(a => a.symbol === asset);

    if (assetIndex === -1) {
      res.status(404).json({ message: 'Asset not found in wallet' });
      return;
    }

    // Update the mode
    wallet.assets[assetIndex].mode = mode;
    await wallet.save();

    res.json({ message: `Mode changed to ${mode} for ${asset}` });
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Deposit asset to wallet
// @route   POST /api/wallets/deposit
// @access  Private
export const depositAsset = async (req: Request, res: Response): Promise<void> => {
  try {
    const { asset, amount, txHash, blockchainNetwork } = req.body;

    // Enhanced validation
    if (!asset || !amount || amount <= 0 || !blockchainNetwork) {
      res.status(400).json({
        message: 'Missing or invalid information',
        errors: {
          asset: !asset ? 'Cryptocurrency is required' : undefined,
          amount: !amount ? 'Amount is required' : amount <= 0 ? 'Amount must be greater than 0' : undefined,
          blockchainNetwork: !blockchainNetwork ? 'Blockchain network is required' : undefined
        }
      });
      return;
    }

    // Find or create wallet
    let wallet = await Wallet.findOne({ userId: req.user._id });

    if (!wallet) {
      // Create a new wallet with default values
      wallet = await Wallet.create({
        userId: req.user._id,
        assets: [],
        totalCommissionEarned: 0,
        totalInterestEarned: 0
      });

      logger.info(`Auto-created wallet for user: ${req.user._id} during deposit request`);
    }

    // Find or create asset
    let assetIndex = wallet.assets.findIndex(a => a.symbol === asset);
    if (assetIndex === -1) {
      wallet.assets.push({
        symbol: asset,
        balance: 0,
        commissionBalance: 0,
        interestBalance: 0,
        mode: 'commission',
        network: blockchainNetwork
      });
      assetIndex = wallet.assets.length - 1;
    }

    // Update balances
    wallet.assets[assetIndex].balance += amount;

    // Calculate commission
    const commissionRate = process.env.COMMISSION_RATE ? parseFloat(process.env.COMMISSION_RATE) : 0.01;
    const commissionAmount = amount * commissionRate;

    // Add commission if in commission mode
    if (wallet.assets[assetIndex].mode === 'commission') {
      wallet.assets[assetIndex].commissionBalance += commissionAmount;
      wallet.totalCommissionEarned += commissionAmount;
    }

    await wallet.save();
    logger.info(`Deposit processed for user: ${req.user._id}, amount: ${amount} ${asset}`);

    // Create transactions
    const [depositTx, commissionTx] = await Promise.all([
      createTransaction({
        userId: req.user._id,
        walletId: wallet._id,
        type: 'deposit',
        asset,
        amount,
        status: 'completed',
        txHash,
        blockchainNetwork,
        userName: `${req.user.firstName} ${req.user.lastName}`,
        userEmail: req.user.email
      }),
      wallet.assets[assetIndex].mode === 'commission' ?
        createTransaction({
          userId: req.user._id,
          walletId: wallet._id,
          type: 'commission',
          asset,
          amount: commissionAmount,
          status: 'completed',
          blockchainNetwork,
          userName: `${req.user.firstName} ${req.user.lastName}`,
          userEmail: req.user.email
        }) : null
    ]);

    res.status(201).json({
      message: 'Deposit successful',
      deposit: depositTx,
      commission: commissionTx,
      newBalance: wallet.assets[assetIndex].balance,
      newCommissionBalance: wallet.assets[assetIndex].commissionBalance
    });

  } catch (error: any) {
    logger.error('Deposit error:', error);
    res.status(500).json({
      message: 'An error occurred during the deposit process',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Withdraw asset from wallet
// @route   POST /api/wallets/withdraw
// @access  Private
export const withdrawAsset = async (req: Request, res: Response): Promise<void> => {
  // Khởi tạo session cho transaction
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    const { asset, amount, txHash, blockchainNetwork, address, memo, withdrawalType, network } = req.body;

    // Log withdrawal request for debugging
    logger.info(`Withdrawal request received:`, {
      userId: req.user._id,
      asset,
      amount,
      withdrawalType,
      address: address?.substring(0, 10) + '...',
      network: network || blockchainNetwork
    });

    // Validate input
    if (!asset || !amount || amount <= 0 || !address) {
      await session.abortTransaction();
      session.endSession();
      res.status(400).json({ message: 'Please provide all required fields (asset, amount, address)' });
      return;
    }

    if (!['interest', 'commission', 'main'].includes(withdrawalType)) {
      await session.abortTransaction();
      session.endSession();
      res.status(400).json({ message: 'Invalid withdrawal type. Must be "interest", "commission", or "main"' });
      return;
    }

    // Validate minimum withdrawal amount (50 USDT equivalent)
    const minimumWithdrawal = 50;
    if (amount < minimumWithdrawal && asset !== 'USDT') {
      // For non-USDT currencies, we should convert to USDT equivalent
      // For now, we'll use a simple validation
      if (amount < 0.001) { // Minimum for crypto
        await session.abortTransaction();
        session.endSession();
        res.status(400).json({
          message: `Minimum withdrawal amount is ${minimumWithdrawal} USDT equivalent`
        });
        return;
      }
    } else if (asset === 'USDT' && amount < minimumWithdrawal) {
      await session.abortTransaction();
      session.endSession();
      res.status(400).json({
        message: `Minimum withdrawal amount is ${minimumWithdrawal} USDT`
      });
      return;
    }

    // Find wallet with session
    let wallet = await Wallet.findOne({ userId: req.user._id }).session(session);

    if (!wallet) {
      // Create a new wallet with default values using session
      const newWallet = await Wallet.create([{
        userId: req.user._id,
        assets: [],
        totalCommissionEarned: 0,
        totalInterestEarned: 0
      }], { session });

      wallet = newWallet[0]; // Mongoose create with session returns an array

      logger.info(`Auto-created wallet for user: ${req.user._id} during withdrawal request`);

      // Since this is a new wallet, it won't have the requested asset
      await session.abortTransaction();
      session.endSession();
      res.status(404).json({ message: 'No wallet found. Please make a deposit first.' });
      return;
    }

    // For interest withdrawals, check investment package balances
    if (withdrawalType === 'interest') {
      try {
        const InvestmentPackage = require('../models/investmentPackageModel').default;

        // Get user's active investment packages with available earnings
        const activePackages = await InvestmentPackage.find({
          userId: req.user._id,
          status: 'active',
          totalEarned: { $gt: 0 }
        }).session(session);

        // Calculate total available earnings
        const totalAvailableEarnings = activePackages.reduce((sum, pkg) => {
          return sum + (pkg.totalEarned || 0);
        }, 0);

        // Check if user has sufficient earnings for withdrawal
        if (totalAvailableEarnings < amount) {
          await session.abortTransaction();
          session.endSession();
          res.status(400).json({
            message: `Insufficient earnings balance. Available: ${totalAvailableEarnings.toFixed(6)} ${asset}`,
            availableBalance: totalAvailableEarnings
          });
          return;
        }

        // Deduct the withdrawal amount from investment packages (proportionally)
        let remainingAmount = amount;
        for (const pkg of activePackages) {
          if (remainingAmount <= 0) break;

          const packageEarnings = pkg.totalEarned || 0;
          const deductionAmount = Math.min(remainingAmount, packageEarnings);

          pkg.totalEarned -= deductionAmount;
          pkg.totalWithdrawn = (pkg.totalWithdrawn || 0) + deductionAmount;
          pkg.lastWithdrawalDate = new Date();

          await pkg.save({ session });
          remainingAmount -= deductionAmount;

          logger.info(`Deducted ${deductionAmount} ${asset} from investment package ${pkg._id}`);
        }

      } catch (error) {
        logger.error('Error checking investment package balances:', error);
        await session.abortTransaction();
        session.endSession();
        res.status(500).json({
          message: 'Error validating withdrawal balance',
          details: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
        return;
      }
    }

    // Find the asset in the wallet
    const assetIndex = wallet.assets.findIndex(a => a.symbol === asset);

    if (assetIndex === -1) {
      await session.abortTransaction();
      session.endSession();
      res.status(404).json({ message: 'Asset not found in wallet' });
      return;
    }

    // Check if there's enough balance (skip for interest withdrawals as they're handled by investment packages)
    if (withdrawalType !== 'interest') {
      let balance: number;
      switch (withdrawalType) {
        case 'main':
          balance = wallet.assets[assetIndex].balance;
          break;
        case 'commission':
          balance = wallet.assets[assetIndex].commissionBalance;
          break;
        default:
          balance = 0;
      }

      if (balance < amount) {
        await session.abortTransaction();
        session.endSession();
        res.status(400).json({ message: 'Insufficient balance' });
        return;
      }
    }

    // Update the balance (skip for interest withdrawals as they're handled by investment packages)
    if (withdrawalType !== 'interest') {
      switch (withdrawalType) {
        case 'main':
          wallet.assets[assetIndex].balance -= amount;
          break;
        case 'commission':
          wallet.assets[assetIndex].commissionBalance -= amount;
          break;
      }
    }

    // Save wallet with session
    await wallet.save({ session });

    // Create withdrawal transaction with session
    const withdrawalTransaction = new Transaction({
      userId: req.user._id,
      walletId: wallet._id,
      type: 'withdrawal',
      asset,
      amount,
      status: 'pending', // All withdrawals start as pending for admin approval
      txHash: txHash || undefined, // Only set if provided
      blockchainNetwork: network || blockchainNetwork || 'ethereum',
      walletAddress: address,
      description: memo || `Withdrawal of ${amount} ${asset} (${withdrawalType})`,
      metadata: {
        withdrawalType,
        requestedBy: req.user.email,
        requestedAt: new Date(),
        userName: `${req.user.firstName} ${req.user.lastName}`,
        userEmail: req.user.email,
        targetAddress: address,
        network: network || blockchainNetwork || 'ethereum',
        minimumWithdrawal: minimumWithdrawal,
        memo: memo || undefined,
        ipAddress: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent')
      }
    });

    await withdrawalTransaction.save({ session });

    // Log the transaction
    logger.info(`Withdrawal request created for user: ${req.user._id}, amount: ${amount} ${asset}, type: ${withdrawalType}`);

    // Commit the transaction
    await session.commitTransaction();
    session.endSession();

    // Notify user about the transaction
    await notificationService.notifyUserAboutTransactionUpdate(
      req.user._id.toString(),
      withdrawalTransaction
    );

    // Notify admins about the withdrawal
    await notificationService.notifyAdminsAboutWithdrawal({
      ...withdrawalTransaction.toObject(),
      userName: `${req.user.firstName} ${req.user.lastName}`,
      userEmail: req.user.email
    });

    // Get the new balance based on withdrawal type
    let newBalance: number;
    switch (withdrawalType) {
      case 'main':
        newBalance = wallet.assets[assetIndex].balance;
        break;
      case 'interest':
        // For interest withdrawals, calculate remaining balance from investment packages
        try {
          const InvestmentPackage = require('../models/investmentPackageModel').default;
          const activePackages = await InvestmentPackage.find({
            userId: req.user._id,
            status: 'active'
          });
          newBalance = activePackages.reduce((sum, pkg) => sum + (pkg.totalEarned || 0), 0);
        } catch (error) {
          newBalance = 0;
        }
        break;
      case 'commission':
        newBalance = wallet.assets[assetIndex].commissionBalance;
        break;
      default:
        newBalance = 0;
    }

    res.status(201).json({
      message: 'Withdrawal request submitted successfully',
      withdrawal: withdrawalTransaction,
      newBalance,
    });
  } catch (error: any) {
    // Rollback transaction in case of error
    await session.abortTransaction();
    session.endSession();

    logger.error('Withdrawal error:', error);
    res.status(500).json({
      message: error.message,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

// @desc    Get transaction history
// @route   GET /api/wallets/transactions
// @access  Private
export const getTransactionHistory = catchAsync(async (req: Request, res: Response): Promise<void> => {
  const cacheKey = `wallet:transactions:${req.user._id}`;

  // Try to get from cache first
  const cachedTransactions = cacheService.get(cacheKey);
  if (cachedTransactions) {
    res.json(cachedTransactions);
    return;
  }

  // Find or create wallet
  let wallet = await Wallet.findOne({ userId: req.user._id });

  if (!wallet) {
    // Create a new wallet with default values
    wallet = await Wallet.create({
      userId: req.user._id,
      assets: [],
      totalCommissionEarned: 0,
      totalInterestEarned: 0
    });

    logger.info(`Auto-created wallet for user: ${req.user._id} during transaction history request`);
  }

  // Get pagination parameters
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const skip = (page - 1) * limit;

  // Get filter parameters
  const type = req.query.type as string;
  const asset = req.query.asset as string;
  const status = req.query.status as string;

  // Build filter
  const filter: any = { walletId: wallet._id };
  if (type) filter.type = type;
  if (asset) filter.asset = asset;
  if (status) filter.status = status;

  // Get total count for pagination
  const total = await Transaction.countDocuments(filter);

  // Get transactions with pagination
  const transactions = await Transaction.find(filter)
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);

  const result = {
    transactions,
    pagination: {
      total,
      page,
      limit,
      pages: Math.ceil(total / limit)
    }
  };

  // Cache the transactions for 5 minutes
  cacheService.set(cacheKey, result, 300);

  res.json(result);
});

// @desc    Get crypto deposit address for a currency
// @route   GET /api/wallets/deposit-address/:currency
// @access  Private
export const getDepositAddress = catchAsync(async (req: Request, res: Response) => {
  const { currency } = req.params;
  const { network } = req.query;

  if (!currency) {
    throw new AppError('Currency is required', 400);
  }

  // Import SystemConfig model
  const SystemConfig = require('../models/systemConfigModel').default;

  // Find system config
  const config = await SystemConfig.findOneOrCreate();

  // Find the crypto address configuration for the currency and network if specified
  let cryptoAddressConfig: any = null;

  if (network) {
    // Tìm địa chỉ theo network cụ thể
    cryptoAddressConfig = config.cryptoAddresses.find(
      (ca: any) => ca.currency === currency.toUpperCase() && ca.enabled && ca.network === network
    );
  }

  // Nếu không tìm thấy địa chỉ theo network hoặc không có network được chỉ định
  if (!cryptoAddressConfig) {
    cryptoAddressConfig = config.cryptoAddresses.find(
      (ca: any) => ca.currency === currency.toUpperCase() && ca.enabled
    );
  }

  if (!cryptoAddressConfig || cryptoAddressConfig.addresses.length === 0) {
    throw new AppError(`No addresses available for ${currency}${network ? ` on network ${network}` : ''}`, 404);
  }

  // Get the next address
  let address: string = '';
  let addressNetwork: string = cryptoAddressConfig.network || 'default';

  // Kiểm tra cấu trúc của addresses
  const addressItem = cryptoAddressConfig.addresses[cryptoAddressConfig.currentIndex];
  if (typeof addressItem === 'string') {
    // Cấu trúc cũ: mảng chuỗi
    address = addressItem;
  } else if (typeof addressItem === 'object' && addressItem !== null) {
    // Cấu trúc mới: mảng đối tượng
    address = addressItem.address;
    addressNetwork = addressItem.network || addressNetwork;
  }

  // Update the current index
  cryptoAddressConfig.currentIndex =
    (cryptoAddressConfig.currentIndex + 1) % cryptoAddressConfig.addresses.length;

  // Save the updated config
  await config.save();

  // Return the address
  res.status(200).json({
    success: true,
    data: {
      currency: currency.toUpperCase(),
      address,
      network: addressNetwork
    }
  });
});

// @desc    Get available wallet addresses for all currencies or a specific currency
// @route   GET /api/wallets/available
// @access  Private
export const getAvailableWallets = catchAsync(async (req: Request, res: Response) => {
  const { currency } = req.query;

  // Danh sách các loại tiền điện tử được hỗ trợ
  const supportedCurrencies = ['BTC', 'ETH', 'USDT', 'BNB', 'DOGE', 'TRX'];

  // Import SystemConfig model
  const SystemConfig = require('../models/systemConfigModel').default;

  // Find system config
  const config = await SystemConfig.findOneOrCreate();

  // Tìm tất cả các cấu hình địa chỉ cho loại tiền được chỉ định hoặc tất cả các loại tiền
  let cryptoAddressConfigs: any[] = [];

  if (currency) {
    // Nếu có tham số currency, chỉ lấy địa chỉ cho loại tiền đó
    const currencyUpper = (currency as string).toUpperCase();

    // Kiểm tra xem loại tiền có được hỗ trợ không
    if (!supportedCurrencies.includes(currencyUpper)) {
      throw new AppError(`Currency ${currency} is not supported`, 400);
    }

    cryptoAddressConfigs = config.cryptoAddresses.filter(
      (ca: any) => ca.currency === currencyUpper && ca.enabled
    );

    // Nếu không có địa chỉ nào cho loại tiền này, trả về mảng rỗng thay vì lỗi
    if (!cryptoAddressConfigs || cryptoAddressConfigs.length === 0) {
      // Tạo cấu trúc dữ liệu rỗng cho loại tiền này
      cryptoAddressConfigs = [];
    }
  } else {
    // Nếu không có tham số currency, lấy tất cả các địa chỉ đã bật
    cryptoAddressConfigs = config.cryptoAddresses.filter(
      (ca: any) => ca.enabled
    );

    // Đảm bảo tất cả các loại tiền được hỗ trợ đều có trong kết quả
    const existingCurrencies = cryptoAddressConfigs.map((ca: any) => ca.currency);
    const missingCurrencies = supportedCurrencies.filter(c => !existingCurrencies.includes(c));

    // Thêm các loại tiền còn thiếu vào kết quả với mảng địa chỉ rỗng
    missingCurrencies.forEach(currency => {
      cryptoAddressConfigs.push({
        currency,
        addresses: [],
        currentIndex: 0,
        enabled: true
      });
    });
  }

  // Tạo đối tượng chứa danh sách địa chỉ theo network với thông tin chi tiết
  interface NetworkInfo {
    id: string;
    name: string;
    description: string;
    fee: number;
    processingTime: string;
    warningMessage?: string;
    addresses: string[];
  }

  // Tạo đối tượng kết quả theo cấu trúc: { currency: { networkId: NetworkInfo } }
  const result: { [currency: string]: { [networkId: string]: NetworkInfo } } = {};

  // Đảm bảo tất cả các loại tiền được hỗ trợ đều có trong kết quả
  supportedCurrencies.forEach(currencyCode => {
    if (!result[currencyCode]) {
      result[currencyCode] = {};
    }

    // Lấy danh sách network cho loại tiền này từ CRYPTO_NETWORKS
    const networks = CRYPTO_NETWORKS[currencyCode] || [];

    // Khởi tạo tất cả các network với mảng địa chỉ rỗng
    networks.forEach(network => {
      result[currencyCode][network.id] = {
        id: network.id,
        name: network.name,
        description: network.description,
        fee: network.fee,
        processingTime: network.processingTime,
        warningMessage: network.warningMessage,
        addresses: []
      };
    });
  });

  // Phân loại địa chỉ theo currency và network
  cryptoAddressConfigs.forEach((config: any) => {
    const currencyCode = config.currency;

    // Bỏ qua nếu loại tiền không được hỗ trợ
    if (!supportedCurrencies.includes(currencyCode)) {
      return;
    }

    // Xử lý cấu trúc cũ (mảng chuỗi với network ở cấp độ config)
    if (config.addresses && config.addresses.length > 0 && typeof config.addresses[0] === 'string') {
      const networkId = config.network || 'default';

      // Kiểm tra xem network có tồn tại trong kết quả không
      if (!result[currencyCode][networkId]) {
        // Lấy thông tin network từ CRYPTO_NETWORKS
        const networkInfo = getNetworkById(currencyCode, networkId);

        result[currencyCode][networkId] = {
          id: networkId,
          name: networkInfo?.name || networkId,
          description: networkInfo?.description || '',
          fee: networkInfo?.fee || 0,
          processingTime: networkInfo?.processingTime || '',
          warningMessage: networkInfo?.warningMessage,
          addresses: []
        };
      }

      result[currencyCode][networkId].addresses = config.addresses;
    }
    // Xử lý cấu trúc mới (mảng đối tượng với network ở cấp độ địa chỉ)
    else if (config.addresses && config.addresses.length > 0 && typeof config.addresses[0] === 'object') {
      config.addresses.forEach((item: any) => {
        const networkId = item.network || 'default';

        // Kiểm tra xem network có tồn tại trong kết quả không
        if (!result[currencyCode][networkId]) {
          // Lấy thông tin network từ CRYPTO_NETWORKS
          const networkInfo = getNetworkById(currencyCode, networkId);

          result[currencyCode][networkId] = {
            id: networkId,
            name: networkInfo?.name || networkId,
            description: networkInfo?.description || '',
            fee: networkInfo?.fee || 0,
            processingTime: networkInfo?.processingTime || '',
            warningMessage: networkInfo?.warningMessage,
            addresses: []
          };
        }

        result[currencyCode][networkId].addresses.push(item.address);
      });
    }
  });

  // Nếu có tham số currency, chỉ trả về dữ liệu cho loại tiền đó
  const responseData = currency ? result[(currency as string).toUpperCase()] : result;

  // Return the available wallets with network info
  res.status(200).json({
    success: true,
    data: responseData
  });
});

// ===== NEW CRYPTO DEPOSIT SYSTEM ENDPOINTS =====

/**
 * @desc    Get user's crypto wallet addresses
 * @route   GET /api/wallets/user-addresses
 * @access  Private
 */
export const getUserAddresses = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?._id || 'mock-user-id';

    const wallets = await walletService.getUserWallets(userId);

    const addresses = wallets.map(wallet => ({
      currency: wallet.currency,
      address: wallet.address,
      qrCodeUrl: walletService.generateQRCode(wallet.address, wallet.currency),
      balance: wallet.balance,
      lastUpdated: wallet.lastUpdated,
      network: wallet.network
    }));

    res.json({
      status: 'success',
      data: {
        addresses,
        totalWallets: addresses.length
      }
    });

  } catch (error: any) {
    logger.error('Get user addresses error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get user addresses',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get balance for a specific currency
 * @route   GET /api/wallets/:currency/balance
 * @access  Private
 */
export const getCurrencyBalance = async (req: Request, res: Response): Promise<void> => {
  try {
    const { currency } = req.params;
    const userId = req.user?._id || 'mock-user-id';

    if (!currency) {
      res.status(400).json({
        status: 'error',
        message: 'Currency parameter is required'
      });
      return;
    }

    const wallets = await walletService.getUserWallets(userId);
    const wallet = wallets.find(w => w.currency === currency.toUpperCase());

    if (!wallet) {
      res.status(404).json({
        status: 'error',
        message: `Wallet not found for currency: ${currency}`
      });
      return;
    }

    // Get USDT value
    const balances = await walletService.getWalletBalances(userId);
    const balanceInfo = balances.find(b => b.currency === currency.toUpperCase());

    res.json({
      status: 'success',
      data: {
        currency: wallet.currency,
        balance: wallet.balance,
        usdtValue: balanceInfo?.usdtValue || 0,
        lastUpdated: wallet.lastUpdated,
        address: wallet.address
      }
    });

  } catch (error: any) {
    logger.error('Get currency balance error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get currency balance',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get all wallet balances with USDT values
 * @route   GET /api/wallets/balances
 * @access  Private
 */
export const getAllBalances = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?._id || 'mock-user-id';

    const balances = await walletService.getWalletBalances(userId);

    // Calculate total USDT value
    const totalUSDTValue = balances.reduce((sum, balance) => sum + balance.usdtValue, 0);

    res.json({
      status: 'success',
      data: {
        balances,
        totalUSDTValue,
        lastUpdated: new Date()
      }
    });

  } catch (error: any) {
    logger.error('Get all balances error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get wallet balances',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Start monitoring deposits for user addresses
 * @route   POST /api/wallets/monitor
 * @access  Private
 */
export const startDepositMonitoring = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?._id || 'mock-user-id';

    // Ensure user has wallets
    await walletService.getUserWallets(userId);

    // Start monitoring if not already running
    const status = depositMonitorService.getStatus();
    if (!status.isRunning) {
      await depositMonitorService.startMonitoring();
    }

    res.json({
      status: 'success',
      message: 'Deposit monitoring started',
      data: {
        monitoringStatus: depositMonitorService.getStatus(),
        userId
      }
    });

  } catch (error: any) {
    logger.error('Start deposit monitoring error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to start deposit monitoring',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * @desc    Get deposit history for user
 * @route   GET /api/wallets/deposits/history
 * @access  Private
 */
export const getDepositHistory = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?._id || 'mock-user-id';
    const limit = parseInt(req.query.limit as string) || 10;
    const page = parseInt(req.query.page as string) || 1;

    const deposits = await walletService.getDepositHistory(userId, limit * page);
    const paginatedDeposits = deposits.slice((page - 1) * limit, page * limit);

    res.json({
      status: 'success',
      data: {
        deposits: paginatedDeposits,
        pagination: {
          page,
          limit,
          total: deposits.length,
          pages: Math.ceil(deposits.length / limit)
        }
      }
    });

  } catch (error: any) {
    logger.error('Get deposit history error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get deposit history',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};
