{"title": "Admin Panel", "navigation": {"dashboard": "Dashboard", "users": "Users", "investments": "Investments", "transactions": "Transactions", "withdrawals": "<PERSON><PERSON><PERSON><PERSON>", "deposits": "Deposits", "reports": "Reports", "analytics": "Analytics", "settings": "Settings", "system": "System", "logs": "Logs", "security": "Security", "notifications": "Notifications", "support": "Support"}, "users": {"title": "User Management", "totalUsers": "Total Users", "activeUsers": "Active Users", "newUsers": "New Users", "verifiedUsers": "Verified Users", "suspendedUsers": "Suspended Users", "userDetails": "User Details", "personalInfo": "Personal Information", "accountInfo": "Account Information", "investmentHistory": "Investment History", "transactionHistory": "Transaction History", "loginHistory": "Login History", "actions": {"view": "View", "edit": "Edit", "suspend": "Suspend", "activate": "Activate", "delete": "Delete", "verify": "Verify", "sendMessage": "Send Message", "resetPassword": "Reset Password", "loginAs": "<PERSON><PERSON>"}, "filters": {"all": "All", "active": "Active", "inactive": "Inactive", "verified": "Verified", "unverified": "Unverified", "suspended": "Suspended"}, "search": {"placeholder": "Search users...", "byEmail": "By Email", "byName": "By Name", "byPhone": "By Phone", "byId": "By ID"}}, "investments": {"title": "Investment Management", "totalInvestments": "Total Investments", "activeInvestments": "Active Investments", "completedInvestments": "Completed Investments", "pendingInvestments": "Pending Investments", "investmentPackages": "Investment Packages", "packageDetails": "Package Details", "createPackage": "Create Package", "editPackage": "Edit Package", "deletePackage": "Delete Package", "packageName": "Package Name", "minAmount": "Minimum Amount", "maxAmount": "Maximum Amount", "dailyReturn": "Daily Return", "duration": "Duration", "description": "Description", "features": "Features", "isActive": "Is Active", "actions": {"approve": "Approve", "reject": "Reject", "cancel": "Cancel", "extend": "Extend", "modify": "Modify"}}, "transactions": {"title": "Transaction Management", "allTransactions": "All Transactions", "pendingTransactions": "Pending Transactions", "completedTransactions": "Completed Transactions", "failedTransactions": "Failed Transactions", "transactionDetails": "Transaction Details", "transactionId": "Transaction ID", "userId": "User ID", "amount": "Amount", "currency": "<PERSON><PERSON><PERSON><PERSON>", "type": "Type", "status": "Status", "date": "Date", "description": "Description", "actions": {"approve": "Approve", "reject": "Reject", "reverse": "Reverse", "investigate": "Investigate"}, "types": {"deposit": "<PERSON><PERSON><PERSON><PERSON>", "withdrawal": "<PERSON><PERSON><PERSON>", "investment": "Investment", "earning": "Earning", "referral": "Referral", "bonus": "Bonus", "fee": "Fee", "refund": "Refund"}}, "withdrawals": {"title": "Withdrawal Management", "pendingWithdrawals": "Pending Withdrawals", "approvedWithdrawals": "Approved Withdrawals", "rejectedWithdrawals": "Rejected <PERSON>s", "withdrawalDetails": "<PERSON><PERSON><PERSON>", "withdrawalId": "Withdrawal ID", "requestDate": "Request Date", "processDate": "Process Date", "bankDetails": "Bank Details", "cryptoAddress": "Crypto Address", "actions": {"approve": "Approve", "reject": "Reject", "process": "Process", "hold": "Hold"}, "reasons": {"insufficientFunds": "Insufficient Funds", "invalidDetails": "Invalid Details", "securityConcern": "Security Concern", "fraudSuspicion": "<PERSON><PERSON>", "technicalIssue": "Technical Issue"}}, "reports": {"title": "Reports", "financialReports": "Financial Reports", "userReports": "User Reports", "investmentReports": "Investment Reports", "transactionReports": "Transaction Reports", "performanceReports": "Performance Reports", "generateReport": "Generate Report", "downloadReport": "Download Report", "scheduleReport": "Schedule Report", "reportPeriod": "Report Period", "reportFormat": "Report Format", "reportType": "Report Type", "filters": {"dateRange": "Date Range", "userType": "User Type", "transactionType": "Transaction Type", "status": "Status", "amount": "Amount"}}, "deposits": {"title": "Deposit Management", "pendingDeposits": "Pending Deposits", "approvedDeposits": "Approved Deposits", "rejectedDeposits": "Rejected Deposits", "depositDetails": "De<PERSON>sit Details", "depositId": "Deposit ID", "requestDate": "Request Date", "processDate": "Process Date", "originalAmount": "Original Amount", "correctedAmount": "Corrected Amount", "currentAmount": "Current Amount", "actualReceivedAmount": "Actual Received Amount", "actualAmountHelper": "Enter the actual amount received on blockchain. This will be used for investment package creation.", "amountDifference": "Difference", "correctionReason": "Reason for Amount Correction", "correctionReasonPlaceholder": "Explain why the amount was corrected (e.g., network fees, blockchain discrepancy)...", "correctionReasonHelper": "This will be logged for audit purposes", "invalidAmount": "<PERSON><PERSON><PERSON>", "invalidAmountDesc": "Please enter a valid positive amount", "actions": {"approve": "Approve", "reject": "Reject", "process": "Process", "hold": "Hold"}, "reasons": {"insufficientFunds": "Insufficient Funds", "invalidDetails": "Invalid Details", "securityConcern": "Security Concern", "fraudSuspicion": "<PERSON><PERSON>", "technicalIssue": "Technical Issue"}}, "settings": {"title": "System Settings", "general": "General", "security": "Security", "notifications": "Notifications", "email": "Email", "sms": "SMS", "api": "API", "backup": "Backup", "maintenance": "Maintenance", "systemInfo": "System Information", "serverStatus": "Server Status", "databaseStatus": "Database Status", "cacheStatus": "<PERSON>ache <PERSON>", "queueStatus": "Queue Status", "actions": {"save": "Save", "reset": "Reset", "backup": "Backup", "restore": "Rest<PERSON>", "clearCache": "<PERSON>ache", "restartService": "Restart Service"}}}