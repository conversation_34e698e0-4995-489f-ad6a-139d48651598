import React, { useState } from 'react';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  HStack,
  Text,
  Box,
  Flex,
  useToast,
  Select,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Progress
} from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import useAuth from '../../hooks/useAuth';

interface SimpleWithdrawModalProps {
  isOpen: boolean;
  onClose: () => void;
  availableBalance?: number;
}

const SimpleWithdrawModal: React.FC<SimpleWithdrawModalProps> = ({ isOpen, onClose, availableBalance = 0 }) => {
  const { t } = useTranslation();
  const toast = useToast();
  const { user } = useAuth();
  
  // State variables
  const [selectedCrypto, setSelectedCrypto] = useState('BTC');
  const [amount, setAmount] = useState('');
  const [walletAddress, setWalletAddress] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  
  // Handle form submission
  const handleSubmit = () => {
    // Validate form
    if (!amount || parseFloat(amount) <= 0) {
      toast({
        title: t('withdrawModal.invalidAmount', 'Geçersiz miktar'),
        description: t('withdrawModal.enterValidAmount', 'Lütfen geçerli bir miktar girin.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }
    
    if (parseFloat(amount) > availableBalance) {
      toast({
        title: t('withdrawModal.insufficientBalance', 'Yetersiz bakiye'),
        description: t('withdrawModal.insufficientBalanceDesc', 'Çekmek istediğiniz miktar bakiyenizden fazla.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }
    
    if (!walletAddress) {
      toast({
        title: t('withdrawModal.addressRequired', 'Cüzdan adresi gerekli'),
        description: t('withdrawModal.enterAddress', 'Lütfen geçerli bir cüzdan adresi girin.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }
    
    // Start submission process
    setIsSubmitting(true);
    
    // Simulate progress
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      setUploadProgress(progress);
      if (progress >= 100) {
        clearInterval(interval);
        
        // Simulate API call delay
        setTimeout(() => {
          setIsSubmitting(false);
          setUploadProgress(0);
          
          // Show success message
          toast({
            title: t('withdrawModal.successTitle', 'İşlem başarılı'),
            description: t('withdrawModal.successDescription', 'Para çekme talebiniz alınmıştır. İşleminiz en kısa sürede gerçekleştirilecektir.'),
            status: 'success',
            duration: 5000,
            isClosable: true,
          });
          
          // Reset form and close modal
          setAmount('');
          setWalletAddress('');
          onClose();
        }, 500);
      }
    }, 200);
  };
  
  return (
    <Modal isOpen={isOpen} onClose={onClose} size="md" isCentered>
      <ModalOverlay backdropFilter="blur(5px)" />
      <ModalContent bg="#0B0E11" borderColor="#2B3139" borderWidth="1px">
        <ModalHeader color="#F0B90B" borderBottomWidth="1px" borderColor="#2B3139">
          {t('withdrawModal.title', 'Para Çekme İşlemi')}
        </ModalHeader>
        <ModalCloseButton color="#EAECEF" />
        
        <ModalBody py={6}>
          <VStack spacing={4} align="stretch">
            <Box p={4} bg="#1E2329" borderRadius="md">
              <Text color="#848E9C" fontSize="sm">{t('withdrawModal.availableBalance', 'Kullanılabilir Bakiye')}</Text>
              <Text fontSize="xl" fontWeight="bold" color="#EAECEF">
                ${availableBalance.toFixed(2)} USD
              </Text>
            </Box>
            
            <FormControl isRequired>
              <FormLabel color="#848E9C" fontSize="sm">{t('withdrawModal.cryptoCurrency', 'Kripto Para Birimi')}</FormLabel>
              <Select 
                value={selectedCrypto}
                onChange={(e) => setSelectedCrypto(e.target.value)}
                bg="#0B0E11"
                borderColor="#2B3139"
                color="#EAECEF"
                _hover={{ borderColor: "#F0B90B" }}
              >
                <option value="BTC">Bitcoin (BTC)</option>
                <option value="ETH">Ethereum (ETH)</option>
                <option value="USDT">Tether (USDT)</option>
                <option value="DOGE">Dogecoin (DOGE)</option>
                <option value="XRP">XRP</option>
              </Select>
            </FormControl>
            
            <FormControl isRequired>
              <FormLabel color="#848E9C" fontSize="sm">{t('withdrawModal.amount', 'Çekilecek Miktar (USD)')}</FormLabel>
              <NumberInput 
                value={amount} 
                onChange={setAmount}
                max={availableBalance} 
                min={0.001}
                precision={2}
                w="100%"
              >
                <NumberInputField 
                  placeholder="0.00"
                  bg="#0B0E11"
                  borderColor="#2B3139"
                  color="#EAECEF"
                  _hover={{ borderColor: "#F0B90B" }}
                />
                <NumberInputStepper>
                  <NumberIncrementStepper color="#EAECEF" />
                  <NumberDecrementStepper color="#EAECEF" />
                </NumberInputStepper>
              </NumberInput>
            </FormControl>
            
            <HStack spacing={2} mt={2}>
              <Button 
                size="sm" 
                onClick={() => setAmount((availableBalance * 0.25).toFixed(2))}
                variant="outline"
                borderColor="#2B3139"
                color="#848E9C"
                _hover={{ borderColor: "#F0B90B" }}
              >
                % 25
              </Button>
              <Button 
                size="sm" 
                onClick={() => setAmount((availableBalance * 0.5).toFixed(2))}
                variant="outline"
                borderColor="#2B3139"
                color="#848E9C"
                _hover={{ borderColor: "#F0B90B" }}
              >
                % 50
              </Button>
              <Button 
                size="sm" 
                onClick={() => setAmount((availableBalance * 0.75).toFixed(2))}
                variant="outline"
                borderColor="#2B3139"
                color="#848E9C"
                _hover={{ borderColor: "#F0B90B" }}
              >
                % 75
              </Button>
              <Button 
                size="sm" 
                onClick={() => setAmount(availableBalance.toFixed(2))}
                variant="outline"
                borderColor="#2B3139"
                color="#848E9C"
                _hover={{ borderColor: "#F0B90B" }}
              >
                % 100
              </Button>
            </HStack>
            
            <FormControl isRequired mt={4}>
              <FormLabel color="#848E9C" fontSize="sm">
                {t('withdrawModal.walletAddress', `${selectedCrypto} Cüzdan Adresi`)}
              </FormLabel>
              <Input 
                value={walletAddress}
                onChange={(e) => setWalletAddress(e.target.value)}
                placeholder={t('withdrawModal.walletAddressPlaceholder', 'Cüzdan adresinizi girin')}
                bg="#0B0E11"
                borderColor="#2B3139"
                color="#EAECEF"
                _hover={{ borderColor: "#F0B90B" }}
                fontFamily="monospace"
              />
            </FormControl>
            
            <Text color="#F0B90B" fontSize="xs" mt={2}>
              Minimum çekim miktarı: $0.001
            </Text>
            
            <Button
              bg="#F0B90B"
              color="#0B0E11"
              _hover={{ bg: "#F8D12F" }}
              onClick={handleSubmit}
              isLoading={isSubmitting}
              loadingText={t('common.processing', 'İşleniyor...')}
              w="100%"
              mt={4}
            >
              {t('withdrawModal.confirmButton', 'Para Çek')}
            </Button>
            
            {isSubmitting && (
              <Box mt={4}>
                <Text fontSize="xs" color="#848E9C" mb={2}>
                  {t('withdrawModal.processing', 'İşleminiz gerçekleştiriliyor...')}
                </Text>
                <Progress value={uploadProgress} size="xs" colorScheme="yellow" borderRadius="full" />
              </Box>
            )}
          </VStack>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default SimpleWithdrawModal;
