import { TFunction } from 'react-i18next';

/**
 * i18n Utility Functions
 *
 * Collection of utility functions for internationalization
 */

// Language configuration - English only
export const LANGUAGE_CONFIG = {
  en: {
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    code: 'EN',
    isRTL: false,
    dateFormat: 'MM/DD/YYYY',
    timeFormat: 'h:mm A',
    currency: 'USD',
    currencySymbol: '$',
    numberFormat: {
      decimal: '.',
      thousands: ',',
    },
  },
  de: {
    name: 'German',
    nativeName: 'Deutsch',
    flag: '🇩🇪',
    code: 'DE',
    isRTL: false,
    dateFormat: 'DD.MM.YYYY',
    timeFormat: 'HH:mm',
    currency: 'EUR',
    currencySymbol: '€',
    numberFormat: {
      decimal: ',',
      thousands: '.',
    },
  },
  fr: {
    name: 'French',
    nativeName: 'Français',
    flag: '🇫🇷',
    code: 'FR',
    isRTL: false,
    dateFormat: 'DD/MM/YYYY',
    timeFormat: 'HH:mm',
    currency: 'EUR',
    currencySymbol: '€',
    numberFormat: {
      decimal: ',',
      thousands: ' ',
    },
  },
} as const;

export type SupportedLanguage = keyof typeof LANGUAGE_CONFIG;

/**
 * Get language configuration
 */
export const getLanguageConfig = (language: string) => {
  return LANGUAGE_CONFIG[language as SupportedLanguage] || LANGUAGE_CONFIG.en;
};

/**
 * Format currency with locale-specific formatting
 */
export const formatCurrencyWithLocale = (
  amount: number,
  currency: string,
  language: string
): string => {
  const config = getLanguageConfig(language);

  try {
    return new Intl.NumberFormat(language, {
      style: 'currency',
      currency: currency || config.currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  } catch (error) {
    // Fallback formatting
    const symbol = currency === 'USD' ? '$' : currency === 'TRY' ? '₺' : currency;
    return `${symbol}${amount.toFixed(2)}`;
  }
};

/**
 * Format number with locale-specific formatting
 */
export const formatNumberWithLocale = (
  number: number,
  language: string,
  options?: Intl.NumberFormatOptions
): string => {
  try {
    return new Intl.NumberFormat(language, options).format(number);
  } catch (error) {
    return number.toString();
  }
};

/**
 * Format date with locale-specific formatting
 */
export const formatDateWithLocale = (
  date: Date | string,
  language: string,
  options?: Intl.DateTimeFormatOptions
): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  try {
    return new Intl.DateTimeFormat(language, options).format(dateObj);
  } catch (error) {
    return dateObj.toLocaleDateString();
  }
};

/**
 * Format relative time (e.g., "2 hours ago")
 */
export const formatRelativeTimeWithLocale = (
  date: Date | string,
  language: string,
  t: TFunction
): string => {
  const now = new Date();
  const targetDate = typeof date === 'string' ? new Date(date) : date;
  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return t('common.time.now', 'now');
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return t('common.time.minutesAgo', { count: diffInMinutes }, '{{count}} minutes ago');
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return t('common.time.hoursAgo', { count: diffInHours }, '{{count}} hours ago');
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) {
    return t('common.time.daysAgo', { count: diffInDays }, '{{count}} days ago');
  }

  const diffInWeeks = Math.floor(diffInDays / 7);
  if (diffInWeeks < 4) {
    return t('common.time.weeksAgo', { count: diffInWeeks }, '{{count}} weeks ago');
  }

  const diffInMonths = Math.floor(diffInDays / 30);
  return t('common.time.monthsAgo', { count: diffInMonths }, '{{count}} months ago');
};

/**
 * Get validation messages with proper translations
 */
export const getValidationMessages = (t: TFunction) => ({
  required: (field: string) => t('common.validation.required', { field }, `${field} is required`),
  email: () => t('common.validation.email', 'Please enter a valid email address'),
  password: () => t('common.validation.password', 'Password must be at least 8 characters'),
  passwordMatch: () => t('common.validation.passwordMatch', 'Passwords do not match'),
  minLength: (min: number) => t('common.validation.minLength', { min }, `Minimum length is ${min} characters`),
  maxLength: (max: number) => t('common.validation.maxLength', { max }, `Maximum length is ${max} characters`),
  min: (min: number) => t('common.validation.min', { min }, `Minimum value is ${min}`),
  max: (max: number) => t('common.validation.max', { max }, `Maximum value is ${max}`),
  pattern: () => t('common.validation.pattern', 'Invalid format'),
  numeric: () => t('common.validation.numeric', 'Please enter a valid number'),
  phone: () => t('common.validation.phone', 'Please enter a valid phone number'),
  url: () => t('common.validation.url', 'Please enter a valid URL'),
});

/**
 * Get success messages with proper translations
 */
export const getSuccessMessages = (t: TFunction) => ({
  saved: () => t('common.messages.saved', 'Successfully saved'),
  updated: () => t('common.messages.updated', 'Successfully updated'),
  created: () => t('common.messages.created', 'Successfully created'),
  deleted: () => t('common.messages.deleted', 'Successfully deleted'),
  sent: () => t('common.messages.sent', 'Successfully sent'),
  copied: () => t('common.messages.copied', 'Copied to clipboard'),
});

/**
 * Get error messages with proper translations
 */
export const getErrorMessages = (t: TFunction) => ({
  generic: () => t('common.errors.generic', 'An error occurred'),
  network: () => t('common.errors.network', 'Network error'),
  unauthorized: () => t('common.errors.unauthorized', 'Unauthorized access'),
  forbidden: () => t('common.errors.forbidden', 'Access forbidden'),
  notFound: () => t('common.errors.notFound', 'Not found'),
  serverError: () => t('common.errors.serverError', 'Server error'),
  timeout: () => t('common.errors.timeout', 'Request timeout'),
  validation: () => t('common.errors.validation', 'Validation error'),
});

/**
 * Get crypto-specific translations
 */
export const getCryptoTranslations = (t: TFunction) => ({
  currencies: {
    BTC: t('common.crypto.BTC', 'Bitcoin'),
    ETH: t('common.crypto.ETH', 'Ethereum'),
    USDT: t('common.crypto.USDT', 'Tether'),
    BNB: t('common.crypto.BNB', 'Binance Coin'),
    TRX: t('common.crypto.TRX', 'Tron'),
    SOL: t('common.crypto.SOL', 'Solana'),
  },
  actions: {
    deposit: () => t('common.crypto.deposit', 'Deposit'),
    withdraw: () => t('common.crypto.withdraw', 'Withdraw'),
    transfer: () => t('common.crypto.transfer', 'Transfer'),
    exchange: () => t('common.crypto.exchange', 'Exchange'),
  },
  status: {
    pending: () => t('common.crypto.pending', 'Pending'),
    confirmed: () => t('common.crypto.confirmed', 'Confirmed'),
    failed: () => t('common.crypto.failed', 'Failed'),
    cancelled: () => t('common.crypto.cancelled', 'Cancelled'),
  },
});

/**
 * Get investment-specific translations
 */
export const getInvestmentTranslations = (t: TFunction) => ({
  status: {
    active: () => t('investment.status.active', 'Active'),
    completed: () => t('investment.status.completed', 'Completed'),
    pending: () => t('investment.status.pending', 'Pending'),
    cancelled: () => t('investment.status.cancelled', 'Cancelled'),
    expired: () => t('investment.status.expired', 'Expired'),
  },
  types: {
    fixed: () => t('investment.types.fixed', 'Fixed Term'),
    flexible: () => t('investment.types.flexible', 'Flexible'),
    compound: () => t('investment.types.compound', 'Compound'),
  },
  periods: {
    daily: () => t('investment.periods.daily', 'Daily'),
    weekly: () => t('investment.periods.weekly', 'Weekly'),
    monthly: () => t('investment.periods.monthly', 'Monthly'),
    yearly: () => t('investment.periods.yearly', 'Yearly'),
  },
});

/**
 * Pluralization helper
 */
export const pluralize = (
  t: TFunction,
  key: string,
  count: number,
  options?: any
): string => {
  return t(key, { count, ...options });
};

/**
 * Safe translation with fallback
 */
export const safeTranslate = (
  t: TFunction,
  key: string,
  fallback: string,
  options?: any
): string => {
  try {
    const translation = t(key, options);
    return translation === key ? fallback : translation;
  } catch (error) {
    console.warn(`Translation failed for key: ${key}`, error);
    return fallback;
  }
};

/**
 * Get browser language preference
 */
export const getBrowserLanguage = (): string => {
  const browserLang = navigator.language || navigator.languages?.[0] || 'en';
  const langCode = browserLang.split('-')[0];

  // Return supported language or default to English
  return Object.keys(LANGUAGE_CONFIG).includes(langCode) ? langCode : 'en';
};

/**
 * Check if language is supported
 */
export const isLanguageSupported = (language: string): boolean => {
  return Object.keys(LANGUAGE_CONFIG).includes(language);
};

/**
 * Get language display name
 */
export const getLanguageDisplayName = (language: string, inLanguage?: string): string => {
  const config = getLanguageConfig(language);
  const displayConfig = inLanguage ? getLanguageConfig(inLanguage) : config;

  return inLanguage === language ? config.nativeName : config.name;
};
