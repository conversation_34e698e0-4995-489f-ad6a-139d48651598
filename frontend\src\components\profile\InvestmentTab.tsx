import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Heading,
  Badge,
  Button,
  Icon,
  SimpleGrid,
  Flex,
  useToast,
  Spinner,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Progress,
  Select,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Divider,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Center
} from '@chakra-ui/react';
import {
  FaChartLine,
  FaClock,
  FaFileExport,
  FaCopy,
  FaEye,
  FaDownload,
  FaCoins,
  FaArrowUp,
  FaHistory,
  FaWallet,
  FaPlus,
  FaMinus
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import useAuth from '../../hooks/useAuth';
import { investmentBalanceService, InvestmentBalance } from '../../services/investmentBalanceService';
import { investmentPackageService } from '../../services/api';
import DepositModal from '../modals/DepositModal';
import ThreeStepWithdrawModal from '../modals/ThreeStepWithdrawModal';

interface InvestmentPackage {
  _id: string;
  packageId: string;
  amount: number;
  currency: string;
  status: 'pending' | 'active' | 'completed' | 'withdrawn';
  accumulatedInterest: number;
  totalEarned: number;
  activeDays: number;
  totalDays: number;
  roi: number;
  createdAt: string;
  activatedAt?: string;
  interestRate: number;
  transactionId?: string;
}

interface InterestDistribution {
  _id: string;
  distributionId: string;
  cryptocurrency: string;
  amount: number;
  usdValue: number;
  distributionDate: string;
  type: 'daily' | 'bonus' | 'completion';
  status: 'completed' | 'pending' | 'failed';
  transactionHash: string;
  packageId: {
    packageId: string;
    currency: string;
    amount: number;
  };
}

interface WithdrawalEligibility {
  eligible: boolean;
  currentBalance: number;
  minimumRequired: number;
  availableForWithdrawal: number;
  withdrawalFee: number;
  usdEquivalent: number;
  status: 'eligible' | 'insufficient_balance' | 'cooldown';
}

interface InvestmentTabProps {
  onDepositOpen: () => void;
}

const InvestmentTab: React.FC<InvestmentTabProps> = ({ onDepositOpen }) => {
  const { t } = useTranslation();
  const toast = useToast();
  const { user } = useAuth();
  const { isOpen: isDistributionOpen, onOpen: onDistributionOpen, onClose: onDistributionClose } = useDisclosure();
  const { isOpen: isWithdrawalOpen, onOpen: onWithdrawalOpen, onClose: onWithdrawalClose } = useDisclosure();

  const [investmentPackages, setInvestmentPackages] = useState<InvestmentPackage[]>([]);
  const [distributions, setDistributions] = useState<InterestDistribution[]>([]);
  const [withdrawalEligibility, setWithdrawalEligibility] = useState<WithdrawalEligibility | null>(null);
  const [loading, setLoading] = useState(true);
  const [distributionsLoading, setDistributionsLoading] = useState(false);
  const [sortBy, setSortBy] = useState('date');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedPackage, setSelectedPackage] = useState<InvestmentPackage | null>(null);
  const [investmentBalances, setInvestmentBalances] = useState<InvestmentBalance[]>([]);
  const [balancesLoading, setBalancesLoading] = useState(false);

  // Modal states for deposit and withdraw functionality
  const { isOpen: isDepositModalOpen, onOpen: onDepositModalOpen, onClose: onDepositModalClose } = useDisclosure();
  const { isOpen: isWithdrawModalOpen, onOpen: onWithdrawModalOpen, onClose: onWithdrawModalClose } = useDisclosure();
  const [selectedCrypto, setSelectedCrypto] = useState<string>('USDT');

  // Fetch comprehensive investment data
  const fetchInvestmentData = async () => {
    try {
      setLoading(true);
      const response = await investmentPackageService.getComprehensive();

      if (response.data) {
        setInvestmentPackages(response.data.data.packages || []);
        setDistributions(response.data.data.distributions || []);
        setWithdrawalEligibility(response.data.data.withdrawalEligibility);
      }
    } catch (error) {
      console.error('Error fetching investment data:', error);
      toast({
        title: t('common.error', 'Error'),
        description: t('investments.fetchError', 'Failed to load investment data'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch investment balances
  const fetchInvestmentBalances = async () => {
    try {
      setBalancesLoading(true);
      const balances = await investmentBalanceService.getInvestmentBalances();
      setInvestmentBalances(balances);
    } catch (error) {
      console.error('Error fetching investment balances:', error);
      setInvestmentBalances([]);
    } finally {
      setBalancesLoading(false);
    }
  };

  // Fetch interest distributions
  const fetchDistributions = async (packageId?: string) => {
    try {
      setDistributionsLoading(true);
      const url = packageId
        ? `/api/investment-packages/distributions?packageId=${packageId}`
        : '/api/investment-packages/distributions';

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setDistributions(data.data.distributions || []);
      }
    } catch (error) {
      console.error('Error fetching distributions:', error);
    } finally {
      setDistributionsLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchInvestmentData();
      fetchInvestmentBalances();
    }
  }, [user]);

  // Calculate summary statistics grouped by currency
  const summaryByCurrency = investmentPackages.reduce((acc, pkg) => {
    if (!acc[pkg.currency]) {
      acc[pkg.currency] = {
        totalInvested: 0,
        totalEarned: 0,
        activePackages: 0,
        totalPackages: 0,
        totalROI: 0
      };
    }

    acc[pkg.currency].totalInvested += pkg.amount;
    acc[pkg.currency].totalEarned += pkg.totalEarned;
    acc[pkg.currency].totalPackages += 1;
    acc[pkg.currency].totalROI += pkg.roi;

    if (pkg.status === 'active') {
      acc[pkg.currency].activePackages += 1;
    }

    return acc;
  }, {} as Record<string, {
    totalInvested: number;
    totalEarned: number;
    activePackages: number;
    totalPackages: number;
    totalROI: number;
  }>);

  // Legacy calculations for backward compatibility
  const totalInvested = investmentPackages.reduce((sum, pkg) => sum + pkg.amount, 0);
  const totalEarned = investmentPackages.reduce((sum, pkg) => sum + pkg.totalEarned, 0);
  const activePackages = investmentPackages.filter(pkg => pkg.status === 'active').length;
  const averageROI = investmentPackages.length > 0
    ? investmentPackages.reduce((sum, pkg) => sum + pkg.roi, 0) / investmentPackages.length
    : 0;

  const formatCryptocurrencyAmount = (amount: number, currency: string) => {
    return `${amount.toLocaleString(undefined, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 8
    })} ${currency}`;
  };

  const formatUSDValue = (amount: number) => {
    return `$${amount.toLocaleString(undefined, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })}`;
  };

  const getPackageProgress = (pkg: InvestmentPackage) => {
    return (pkg.activeDays / pkg.totalDays) * 100;
  };

  const getPackageStatusColor = (status: string) => {
    switch (status) {
      case 'active': return '#02C076';
      case 'completed': return '#F0B90B';
      case 'withdrawn': return '#F84960';
      case 'pending': return '#848E9C';
      default: return '#848E9C';
    }
  };

  const calculateDailyInterest = (principal: number, rate: number) => {
    return principal * (rate / 100);
  };

  // Handle deposit button click for investment packages
  const handleDepositClick = (packageCurrency: string) => {
    setSelectedCrypto(packageCurrency);
    onDepositModalOpen();
  };

  // Handle withdraw button click for investment packages
  const handleWithdrawClick = (packageCurrency: string) => {
    setSelectedCrypto(packageCurrency);
    onWithdrawModalOpen();
  };

  if (loading) {
    return (
      <Center py={10}>
        <VStack spacing={4}>
          <Spinner color="#F0B90B" size="xl" thickness="4px" />
          <Text color="#848E9C">
            {t('investments.loading', 'Loading investment data...')}
          </Text>
        </VStack>
      </Center>
    );
  }

  return (
    <Box>
      <Box mb={6}>
        <Heading size="md" color="#EAECEF" mb={2}>
          {t('investments.title', 'Investment Packages')}
        </Heading>
        <Text color="#848E9C" fontSize="sm">
          {t('investments.description', 'Manage your cryptocurrency investment packages and track earnings with 1% daily interest.')}
        </Text>
      </Box>

      <VStack spacing={6} align="stretch">
        {/* Enhanced Investment Summary with Glassmorphism */}
        <Box
          bg="linear-gradient(135deg, rgba(30, 32, 38, 0.95) 0%, rgba(30, 32, 38, 0.85) 100%)"
          backdropFilter="blur(20px)"
          p={{ base: 5, md: 8 }}
          borderRadius="2xl"
          borderWidth="1px"
          borderColor="rgba(240, 185, 11, 0.2)"
          boxShadow="0 8px 32px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(240, 185, 11, 0.1)"
          position="relative"
          overflow="hidden"
          _before={{
            content: '""',
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            height: "1px",
            background: "linear-gradient(90deg, transparent, rgba(240, 185, 11, 0.5), transparent)",
          }}
          transition="all 0.3s ease"
          _hover={{
            borderColor: "rgba(240, 185, 11, 0.4)",
            boxShadow: "0 12px 40px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(240, 185, 11, 0.2)",
            transform: { base: "none", md: "translateY(-2px)" }
          }}
        >
          <Heading
            size={{ base: "sm", md: "md" }}
            color="#FCD535"
            mb={6}
            display="flex"
            alignItems="center"
            fontWeight="600"
            letterSpacing="0.5px"
          >
            <Icon as={FaChartLine} mr={3} boxSize={{ base: 5, md: 6 }} />
            {t('investments.summary', 'Investment Summary by Currency')}
          </Heading>

          {Object.keys(summaryByCurrency).length === 0 ? (
            <Box
              bg="rgba(11, 14, 17, 0.6)"
              p={{ base: 6, md: 8 }}
              borderRadius="xl"
              borderWidth="1px"
              borderColor="rgba(43, 49, 57, 0.8)"
              textAlign="center"
            >
              <Icon as={FaCoins} color="#848E9C" boxSize={12} mb={3} />
              <Text color="#848E9C" fontSize="md" fontWeight="500">
                No investment packages found. Create your first investment to see summary statistics.
              </Text>
            </Box>
          ) : (
            <VStack spacing={6} align="stretch">
              {Object.entries(summaryByCurrency).map(([currency, summary]) => (
                <Box
                  key={currency}
                  bg="rgba(11, 14, 17, 0.4)"
                  p={{ base: 4, md: 6 }}
                  borderRadius="xl"
                  borderWidth="1px"
                  borderColor="rgba(43, 49, 57, 0.6)"
                  backdropFilter="blur(10px)"
                >
                  <Flex
                    direction={{ base: "column", md: "row" }}
                    justify="space-between"
                    align={{ base: "stretch", md: "center" }}
                    mb={4}
                  >
                    <Text color="#FCD535" fontSize={{ base: "lg", md: "xl" }} fontWeight="700">
                      {currency} Portfolio
                    </Text>
                    <Box
                      bg="rgba(2, 192, 118, 0.2)"
                      px={3}
                      py={1}
                      borderRadius="full"
                      borderWidth="1px"
                      borderColor="rgba(2, 192, 118, 0.3)"
                      alignSelf={{ base: "flex-start", md: "center" }}
                    >
                      <Text color="#02C076" fontSize="sm" fontWeight="600">
                        {summary.activePackages} / {summary.totalPackages} Active
                      </Text>
                    </Box>
                  </Flex>

                  <SimpleGrid columns={{ base: 1, sm: 2, lg: 4 }} spacing={{ base: 3, md: 4 }}>
                    <Box textAlign="center">
                      <Text color="#848E9C" fontSize="sm" fontWeight="500" mb={1}>
                        Total Invested
                      </Text>
                      <Text color="#FCD535" fontSize={{ base: "lg", md: "xl" }} fontWeight="700">
                        {formatCryptocurrencyAmount(summary.totalInvested, currency)}
                      </Text>
                    </Box>

                    <Box textAlign="center">
                      <Text color="#848E9C" fontSize="sm" fontWeight="500" mb={1}>
                        Total Earned
                      </Text>
                      <Text color="#02C076" fontSize={{ base: "lg", md: "xl" }} fontWeight="700">
                        {formatCryptocurrencyAmount(summary.totalEarned, currency)}
                      </Text>
                    </Box>

                    <Box textAlign="center">
                      <Text color="#848E9C" fontSize="sm" fontWeight="500" mb={1}>
                        Active Packages
                      </Text>
                      <Text color="#FCD535" fontSize={{ base: "lg", md: "xl" }} fontWeight="700">
                        {summary.activePackages}
                      </Text>
                    </Box>

                    <Box textAlign="center">
                      <Text color="#848E9C" fontSize="sm" fontWeight="500" mb={1}>
                        Average ROI
                      </Text>
                      <Text color="#FCD535" fontSize={{ base: "lg", md: "xl" }} fontWeight="700">
                        {summary.totalPackages > 0 ? (summary.totalROI / summary.totalPackages).toFixed(2) : '0.00'}%
                      </Text>
                    </Box>
                  </SimpleGrid>
                </Box>
              ))}
            </VStack>
          )}
        </Box>

        {/* Enhanced Withdrawal Eligibility - Redirects to Currency-Specific Section */}
        {withdrawalEligibility && (
          <Box
            bg="linear-gradient(135deg, rgba(30, 32, 38, 0.95) 0%, rgba(30, 32, 38, 0.85) 100%)"
            backdropFilter="blur(20px)"
            p={{ base: 5, md: 8 }}
            borderRadius="2xl"
            borderWidth="1px"
            borderColor="rgba(2, 192, 118, 0.3)"
            boxShadow="0 8px 32px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(240, 185, 11, 0.1)"
            position="relative"
            overflow="hidden"
            _before={{
              content: '""',
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              height: "1px",
              background: "linear-gradient(90deg, transparent, rgba(2, 192, 118, 0.5), transparent)",
            }}
            transition="all 0.3s ease"
            _hover={{
              borderColor: "rgba(2, 192, 118, 0.5)",
              boxShadow: "0 12px 40px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(240, 185, 11, 0.2)",
              transform: { base: "none", md: "translateY(-2px)" }
            }}
          >
            <Flex
              direction={{ base: "column", md: "row" }}
              justify="space-between"
              align={{ base: "stretch", md: "center" }}
              mb={6}
              gap={4}
            >
              <Heading
                size={{ base: "sm", md: "md" }}
                color="#FCD535"
                display="flex"
                alignItems="center"
                fontWeight="600"
                letterSpacing="0.5px"
              >
                <Icon as={FaWallet} mr={3} boxSize={{ base: 5, md: 6 }} />
                {t('investments.withdrawalEligibility', 'Withdrawal Information')}
              </Heading>
              <Box
                bg="rgba(2, 192, 118, 0.2)"
                backdropFilter="blur(10px)"
                px={4}
                py={2}
                borderRadius="full"
                borderWidth="1px"
                borderColor="rgba(2, 192, 118, 0.4)"
                alignSelf={{ base: "flex-start", md: "center" }}
                transition="all 0.3s ease"
                _hover={{
                  bg: "rgba(2, 192, 118, 0.3)",
                  transform: { base: "none", md: "scale(1.05)" }
                }}
              >
                <Text
                  color="#02C076"
                  fontSize={{ base: "sm", md: "md" }}
                  fontWeight="600"
                  textAlign="center"
                >
                  {t('investments.byCurrency', 'By Currency')}
                </Text>
              </Box>
            </Flex>

            <Box
              bg="rgba(11, 14, 17, 0.6)"
              p={{ base: 6, md: 8 }}
              borderRadius="xl"
              borderWidth="1px"
              borderColor="rgba(43, 49, 57, 0.8)"
              textAlign="center"
            >
              <Icon as={FaCoins} color="#02C076" boxSize={12} mb={4} />
              <Text color="#EAECEF" fontSize={{ base: "lg", md: "xl" }} fontWeight="600" mb={2}>
                Native Cryptocurrency Withdrawals
              </Text>
              <Text color="#848E9C" fontSize="md" maxW="600px" mx="auto" lineHeight="1.6">
                Withdrawal amounts are displayed in their native cryptocurrency units below.
                Each currency has its own minimum withdrawal requirements and available balances.
              </Text>
            </Box>

            {/* Enhanced Investment Package Balances */}
            {investmentBalances.length > 0 && (
              <Box
                bg="linear-gradient(135deg, rgba(2, 192, 118, 0.1) 0%, rgba(2, 192, 118, 0.05) 100%)"
                backdropFilter="blur(10px)"
                p={{ base: 4, md: 6 }}
                borderRadius="xl"
                borderWidth="1px"
                borderColor="rgba(2, 192, 118, 0.2)"
                mb={6}
                position="relative"
                overflow="hidden"
                _before={{
                  content: '""',
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  height: "1px",
                  background: "linear-gradient(90deg, transparent, rgba(2, 192, 118, 0.4), transparent)",
                }}
                transition="all 0.3s ease"
                _hover={{
                  borderColor: "rgba(2, 192, 118, 0.3)",
                  bg: "linear-gradient(135deg, rgba(2, 192, 118, 0.15) 0%, rgba(2, 192, 118, 0.08) 100%)",
                }}
              >
                <Heading
                  size={{ base: "sm", md: "md" }}
                  color="#02C076"
                  fontWeight="600"
                  mb={4}
                  display="flex"
                  alignItems="center"
                  letterSpacing="0.5px"
                >
                  <Icon as={FaCoins} mr={3} boxSize={{ base: 5, md: 6 }} />
                  Available Investment Earnings by Currency
                </Heading>
                <SimpleGrid columns={{ base: 1, sm: 2, lg: 3 }} spacing={{ base: 3, md: 4 }}>
                  {investmentBalances
                    .filter(balance => balance.availableForWithdrawal > 0)
                    .map((balance, index) => (
                      <Box
                        key={index}
                        bg="rgba(11, 14, 17, 0.7)"
                        p={{ base: 4, md: 5 }}
                        borderRadius="xl"
                        borderWidth="1px"
                        borderColor="rgba(43, 49, 57, 0.8)"
                        backdropFilter="blur(10px)"
                        transition="all 0.3s ease"
                        _hover={{
                          borderColor: "rgba(2, 192, 118, 0.4)",
                          bg: "rgba(11, 14, 17, 0.9)",
                          transform: { base: "none", md: "translateY(-2px)" },
                          boxShadow: "0 8px 25px rgba(0, 0, 0, 0.3)"
                        }}
                      >
                        <HStack justify="space-between" mb={3}>
                          <Text color="#FCD535" fontWeight="700" fontSize={{ base: "md", md: "lg" }}>
                            {balance.currency}
                          </Text>
                          <Box
                            bg="rgba(2, 192, 118, 0.2)"
                            px={3}
                            py={1}
                            borderRadius="full"
                            borderWidth="1px"
                            borderColor="rgba(2, 192, 118, 0.3)"
                          >
                            <Text color="#02C076" fontSize="xs" fontWeight="600">
                              {balance.activePackages} packages
                            </Text>
                          </Box>
                        </HStack>
                        <VStack align="start" spacing={2}>
                          <HStack justify="space-between" w="100%">
                            <Text color="#848E9C" fontSize="sm" fontWeight="500">Available:</Text>
                            <Text color="#02C076" fontWeight="700" fontSize="sm">
                              {formatCryptocurrencyAmount(balance.availableForWithdrawal, balance.currency)}
                            </Text>
                          </HStack>
                          <HStack justify="space-between" w="100%">
                            <Text color="#848E9C" fontSize="sm" fontWeight="500">Total Earned:</Text>
                            <Text color="#EAECEF" fontSize="sm" fontWeight="600">
                              {formatCryptocurrencyAmount(balance.totalEarnings, balance.currency)}
                            </Text>
                          </HStack>
                          {balance.totalWithdrawn > 0 && (
                            <HStack justify="space-between" w="100%">
                              <Text color="#848E9C" fontSize="sm" fontWeight="500">Withdrawn:</Text>
                              <Text color="#F84960" fontSize="sm" fontWeight="600">
                                {formatCryptocurrencyAmount(balance.totalWithdrawn, balance.currency)}
                              </Text>
                            </HStack>
                          )}
                        </VStack>
                      </Box>
                    ))
                  }
                </SimpleGrid>
                {investmentBalances.filter(balance => balance.availableForWithdrawal > 0).length === 0 && (
                  <Box
                    bg="rgba(11, 14, 17, 0.6)"
                    p={6}
                    borderRadius="xl"
                    borderWidth="1px"
                    borderColor="rgba(43, 49, 57, 0.8)"
                    textAlign="center"
                  >
                    <Icon as={FaCoins} color="#848E9C" boxSize={12} mb={3} />
                    <Text color="#848E9C" fontSize="md" fontWeight="500">
                      No investment earnings available for withdrawal yet.
                    </Text>
                  </Box>
                )}
              </Box>
            )}

            <VStack spacing={{ base: 4, md: 5 }} align="stretch">
              <Button
                leftIcon={<Icon as={FaCoins} boxSize={{ base: 5, md: 6 }} />}
                bg="linear-gradient(135deg, #FCD535 0%, #F0B90B 100%)"
                color="#0B0E11"
                size={{ base: "lg", md: "xl" }}
                minH={{ base: "50px", md: "56px" }}
                fontSize={{ base: "16px", md: "18px" }}
                fontWeight="600"
                borderRadius="xl"
                boxShadow="0 4px 15px rgba(240, 185, 11, 0.3)"
                onClick={onDepositOpen}
                transition="all 0.3s ease"
                _hover={{
                  bg: "linear-gradient(135deg, #F8D12F 0%, #FCD535 100%)",
                  boxShadow: "0 6px 20px rgba(240, 185, 11, 0.4)",
                  transform: { base: "none", md: "translateY(-2px)" }
                }}
                _active={{
                  transform: "scale(0.98)"
                }}
              >
                {t('investments.createPackage', 'Create New Investment')}
              </Button>

              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={{ base: 3, md: 4 }}>
                <Button
                  leftIcon={<Icon as={FaArrowUp} boxSize={{ base: 4, md: 5 }} />}
                  variant="outline"
                  borderWidth="2px"
                  borderColor={withdrawalEligibility.eligible ? "rgba(2, 192, 118, 0.6)" : "rgba(248, 73, 96, 0.6)"}
                  color={withdrawalEligibility.eligible ? "#02C076" : "#F84960"}
                  bg={withdrawalEligibility.eligible ? "rgba(2, 192, 118, 0.1)" : "rgba(248, 73, 96, 0.1)"}
                  size={{ base: "lg", md: "xl" }}
                  minH={{ base: "48px", md: "52px" }}
                  fontSize={{ base: "15px", md: "16px" }}
                  fontWeight="600"
                  borderRadius="xl"
                  isDisabled={!withdrawalEligibility.eligible}
                  onClick={onWithdrawalOpen}
                  transition="all 0.3s ease"
                  _hover={{
                    bg: withdrawalEligibility.eligible ? "rgba(2, 192, 118, 0.2)" : "rgba(248, 73, 96, 0.2)",
                    borderColor: withdrawalEligibility.eligible ? "rgba(2, 192, 118, 0.8)" : "rgba(248, 73, 96, 0.8)",
                    boxShadow: withdrawalEligibility.eligible ? "0 4px 15px rgba(2, 192, 118, 0.3)" : "0 4px 15px rgba(248, 73, 96, 0.3)",
                    transform: { base: "none", md: "translateY(-1px)" }
                  }}
                  _disabled={{
                    opacity: 0.5,
                    cursor: "not-allowed",
                    _hover: {
                      bg: "rgba(248, 73, 96, 0.1)",
                      borderColor: "rgba(248, 73, 96, 0.6)",
                      transform: "none",
                      boxShadow: "none"
                    }
                  }}
                  _active={{
                    transform: "scale(0.98)"
                  }}
                >
                  {t('investments.withdraw', 'Withdraw Earnings')}
                </Button>

                <Button
                  leftIcon={<Icon as={FaHistory} boxSize={{ base: 4, md: 5 }} />}
                  variant="outline"
                  borderWidth="2px"
                  borderColor="rgba(240, 185, 11, 0.6)"
                  color="#FCD535"
                  bg="rgba(240, 185, 11, 0.1)"
                  size={{ base: "lg", md: "xl" }}
                  minH={{ base: "48px", md: "52px" }}
                  fontSize={{ base: "15px", md: "16px" }}
                  fontWeight="600"
                  borderRadius="xl"
                  onClick={onDistributionOpen}
                  transition="all 0.3s ease"
                  _hover={{
                    bg: "rgba(240, 185, 11, 0.2)",
                    borderColor: "rgba(240, 185, 11, 0.8)",
                    boxShadow: "0 4px 15px rgba(240, 185, 11, 0.3)",
                    transform: { base: "none", md: "translateY(-1px)" }
                  }}
                  _active={{
                    transform: "scale(0.98)"
                  }}
                >
                  {t('investments.viewHistory', 'View History')}
                </Button>
              </SimpleGrid>
            </VStack>
          </Box>
        )}

        {/* Enhanced Active Investment Packages */}
        <Box
          bg="linear-gradient(135deg, rgba(30, 32, 38, 0.95) 0%, rgba(30, 32, 38, 0.85) 100%)"
          backdropFilter="blur(20px)"
          p={{ base: 5, md: 8 }}
          borderRadius="2xl"
          borderWidth="1px"
          borderColor="rgba(240, 185, 11, 0.2)"
          boxShadow="0 8px 32px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(240, 185, 11, 0.1)"
          position="relative"
          overflow="hidden"
          _before={{
            content: '""',
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            height: "1px",
            background: "linear-gradient(90deg, transparent, rgba(240, 185, 11, 0.5), transparent)",
          }}
          transition="all 0.3s ease"
          _hover={{
            borderColor: "rgba(240, 185, 11, 0.4)",
            boxShadow: "0 12px 40px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(240, 185, 11, 0.2)",
          }}
        >
          <Flex
            direction={{ base: "column", md: "row" }}
            justify="space-between"
            align={{ base: "stretch", md: "center" }}
            mb={6}
            gap={4}
          >
            <Heading
              size={{ base: "sm", md: "md" }}
              color="#FCD535"
              display="flex"
              alignItems="center"
              fontWeight="600"
              letterSpacing="0.5px"
            >
              <Icon as={FaCoins} mr={3} boxSize={{ base: 5, md: 6 }} />
              {t('investments.activePackages', 'Active Investment Packages')}
            </Heading>
            <VStack spacing={3} align={{ base: "stretch", md: "flex-end" }}>
              <Box
                bg="rgba(2, 192, 118, 0.2)"
                backdropFilter="blur(10px)"
                px={4}
                py={2}
                borderRadius="full"
                borderWidth="1px"
                borderColor="rgba(2, 192, 118, 0.4)"
                alignSelf={{ base: "flex-start", md: "center" }}
                transition="all 0.3s ease"
                _hover={{
                  bg: "rgba(2, 192, 118, 0.3)",
                  transform: { base: "none", md: "scale(1.05)" }
                }}
              >
                <Text color="#02C076" fontSize={{ base: "sm", md: "md" }} fontWeight="600" textAlign="center">
                  {activePackages} {t('investments.active', 'Active')}
                </Text>
              </Box>
              <Select
                size={{ base: "md", md: "sm" }}
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                bg="rgba(11, 14, 17, 0.8)"
                borderColor="rgba(43, 49, 57, 0.8)"
                color="#EAECEF"
                w={{ base: "100%", md: "180px" }}
                minH={{ base: "44px", md: "auto" }}
                fontSize={{ base: "16px", md: "14px" }}
                borderRadius="lg"
                backdropFilter="blur(10px)"
                transition="all 0.3s ease"
                _hover={{
                  borderColor: "rgba(240, 185, 11, 0.4)",
                  bg: "rgba(11, 14, 17, 0.9)"
                }}
                _focus={{
                  borderColor: "rgba(240, 185, 11, 0.6)",
                  boxShadow: "0 0 0 1px rgba(240, 185, 11, 0.3)"
                }}
              >
                <option value="date">{t('investments.sortByDate', 'Sort by Date')}</option>
                <option value="amount">{t('investments.sortByAmount', 'Sort by Amount')}</option>
                <option value="roi">{t('investments.sortByROI', 'Sort by ROI')}</option>
              </Select>
            </VStack>
          </Flex>

          {investmentPackages.length === 0 ? (
            <Box
              bg="rgba(11, 14, 17, 0.6)"
              p={{ base: 8, md: 12 }}
              borderRadius="2xl"
              borderWidth="1px"
              borderColor="rgba(43, 49, 57, 0.8)"
              textAlign="center"
              backdropFilter="blur(10px)"
            >
              <VStack spacing={6}>
                <Box
                  bg="rgba(132, 142, 156, 0.1)"
                  p={6}
                  borderRadius="full"
                  borderWidth="1px"
                  borderColor="rgba(132, 142, 156, 0.2)"
                >
                  <Icon as={FaCoins} color="#848E9C" boxSize={{ base: 12, md: 16 }} />
                </Box>
                <VStack spacing={3}>
                  <Text color="#EAECEF" fontSize={{ base: "lg", md: "xl" }} fontWeight="600">
                    {t('investments.noPackages', 'No investment packages found')}
                  </Text>
                  <Text color="#848E9C" fontSize={{ base: "md", md: "lg" }} textAlign="center" maxW="400px">
                    {t('investments.createFirstPackage', 'Create your first investment package to start earning 1% daily interest on your cryptocurrency.')}
                  </Text>
                </VStack>
                <Button
                  leftIcon={<Icon as={FaCoins} boxSize={5} />}
                  bg="linear-gradient(135deg, #FCD535 0%, #F0B90B 100%)"
                  color="#0B0E11"
                  size="lg"
                  minH="50px"
                  fontSize="16px"
                  fontWeight="600"
                  borderRadius="xl"
                  boxShadow="0 4px 15px rgba(240, 185, 11, 0.3)"
                  onClick={onDepositOpen}
                  transition="all 0.3s ease"
                  _hover={{
                    bg: "linear-gradient(135deg, #F8D12F 0%, #FCD535 100%)",
                    boxShadow: "0 6px 20px rgba(240, 185, 11, 0.4)",
                    transform: "translateY(-2px)"
                  }}
                  _active={{
                    transform: "scale(0.98)"
                  }}
                >
                  {t('investments.createPackage', 'Create New Investment')}
                </Button>
              </VStack>
            </Box>
          ) : (
            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={{ base: 4, md: 6 }}>
              {investmentPackages
                .filter(pkg => filterStatus === 'all' || pkg.status === filterStatus)
                .sort((a, b) => {
                  switch (sortBy) {
                    case 'amount':
                      return b.amount - a.amount;
                    case 'roi':
                      return b.roi - a.roi;
                    case 'date':
                    default:
                      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
                  }
                })
                .map((pkg) => {
                  const progress = getPackageProgress(pkg);
                  const dailyEarnings = calculateDailyInterest(pkg.amount, pkg.interestRate * 100);
                  const totalValue = pkg.amount + pkg.accumulatedInterest;

                  return (
                    <Box
                      key={pkg._id}
                      bg="linear-gradient(135deg, rgba(11, 14, 17, 0.95) 0%, rgba(11, 14, 17, 0.85) 100%)"
                      backdropFilter="blur(15px)"
                      p={{ base: 5, md: 6 }}
                      borderRadius="2xl"
                      borderWidth="1px"
                      borderColor="rgba(43, 49, 57, 0.8)"
                      boxShadow="0 4px 20px rgba(0, 0, 0, 0.2)"
                      position="relative"
                      overflow="hidden"
                      cursor="pointer"
                      onClick={() => setSelectedPackage(pkg)}
                      transition="all 0.3s ease"
                      _before={{
                        content: '""',
                        position: "absolute",
                        top: 0,
                        left: 0,
                        right: 0,
                        height: "2px",
                        background: pkg.status === 'active' ?
                                   "linear-gradient(90deg, transparent, rgba(2, 192, 118, 0.6), transparent)" :
                                   pkg.status === 'completed' ?
                                   "linear-gradient(90deg, transparent, rgba(240, 185, 11, 0.6), transparent)" :
                                   "linear-gradient(90deg, transparent, rgba(248, 73, 96, 0.6), transparent)",
                      }}
                      _hover={{
                        borderColor: "rgba(240, 185, 11, 0.6)",
                        boxShadow: "0 8px 30px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(240, 185, 11, 0.2)",
                        transform: { base: "none", md: "translateY(-4px)" }
                      }}
                    >
                      {/* Enhanced Package Header */}
                      <Flex justify="space-between" align="center" mb={4}>
                        <VStack align="start" spacing={1}>
                          <HStack spacing={2}>
                            <Box
                              bg="rgba(240, 185, 11, 0.2)"
                              p={2}
                              borderRadius="lg"
                              borderWidth="1px"
                              borderColor="rgba(240, 185, 11, 0.3)"
                            >
                              <Text color="#FCD535" fontSize={{ base: "sm", md: "md" }} fontWeight="700">
                                {pkg.currency}
                              </Text>
                            </Box>
                          </HStack>
                          <Text color="#848E9C" fontSize="xs" fontFamily="mono">
                            ID: {pkg.packageId?.slice(0, 12)}...
                          </Text>
                        </VStack>
                        <Box
                          bg={pkg.status === 'active' ? 'rgba(2, 192, 118, 0.2)' :
                              pkg.status === 'completed' ? 'rgba(240, 185, 11, 0.2)' : 'rgba(248, 73, 96, 0.2)'}
                          backdropFilter="blur(10px)"
                          px={3}
                          py={2}
                          borderRadius="full"
                          borderWidth="1px"
                          borderColor={pkg.status === 'active' ? 'rgba(2, 192, 118, 0.4)' :
                                      pkg.status === 'completed' ? 'rgba(240, 185, 11, 0.4)' : 'rgba(248, 73, 96, 0.4)'}
                          transition="all 0.3s ease"
                          _hover={{
                            bg: pkg.status === 'active' ? 'rgba(2, 192, 118, 0.3)' :
                                pkg.status === 'completed' ? 'rgba(240, 185, 11, 0.3)' : 'rgba(248, 73, 96, 0.3)',
                          }}
                        >
                          <Text
                            color={pkg.status === 'active' ? '#02C076' :
                                  pkg.status === 'completed' ? '#FCD535' : '#F84960'}
                            fontSize="xs"
                            fontWeight="600"
                            textTransform="capitalize"
                          >
                            {pkg.status}
                          </Text>
                        </Box>
                      </Flex>

                      {/* Enhanced Financial Metrics */}
                      <SimpleGrid columns={1} spacing={4} mb={4}>
                        <Box
                          bg="rgba(11, 14, 17, 0.6)"
                          p={4}
                          borderRadius="xl"
                          borderWidth="1px"
                          borderColor="rgba(43, 49, 57, 0.6)"
                          backdropFilter="blur(5px)"
                        >
                          <VStack align="start" spacing={2}>
                            <Text color="#848E9C" fontSize="sm" fontWeight="500">
                              {t('investments.principalAmount', 'Principal Amount')}
                            </Text>
                            <Text color="#EAECEF" fontSize={{ base: "lg", md: "xl" }} fontWeight="700">
                              {formatCryptocurrencyAmount(pkg.amount, pkg.currency)}
                            </Text>
                          </VStack>
                        </Box>

                        <Box
                          bg="rgba(2, 192, 118, 0.1)"
                          p={4}
                          borderRadius="xl"
                          borderWidth="1px"
                          borderColor="rgba(2, 192, 118, 0.2)"
                          backdropFilter="blur(5px)"
                        >
                          <VStack align="start" spacing={2}>
                            <Text color="#848E9C" fontSize="sm" fontWeight="500">
                              {t('investments.accumulatedInterest', 'Accumulated Interest')}
                            </Text>
                            <Text color="#02C076" fontSize={{ base: "md", md: "lg" }} fontWeight="700">
                              {formatCryptocurrencyAmount(pkg.accumulatedInterest, pkg.currency)}
                            </Text>
                          </VStack>
                        </Box>

                        <Box
                          bg="rgba(240, 185, 11, 0.1)"
                          p={4}
                          borderRadius="xl"
                          borderWidth="1px"
                          borderColor="rgba(240, 185, 11, 0.2)"
                          backdropFilter="blur(5px)"
                        >
                          <VStack align="start" spacing={2}>
                            <Text color="#848E9C" fontSize="sm" fontWeight="500">
                              {t('investments.totalValue', 'Total Value')}
                            </Text>
                            <Text color="#FCD535" fontSize={{ base: "lg", md: "xl" }} fontWeight="700">
                              {formatCryptocurrencyAmount(totalValue, pkg.currency)}
                            </Text>
                          </VStack>
                        </Box>
                      </SimpleGrid>

                      {/* Enhanced Performance Metrics */}
                      <SimpleGrid columns={2} spacing={3} mb={4}>
                        <Box
                          bg="rgba(11, 14, 17, 0.4)"
                          p={3}
                          borderRadius="lg"
                          borderWidth="1px"
                          borderColor="rgba(43, 49, 57, 0.6)"
                        >
                          <VStack align="start" spacing={1}>
                            <Text color="#848E9C" fontSize="xs" fontWeight="500">
                              {t('investments.dailyRate', 'Daily Rate')}
                            </Text>
                            <Text color="#FCD535" fontSize="sm" fontWeight="700">
                              1.00%
                            </Text>
                          </VStack>
                        </Box>
                        <Box
                          bg="rgba(11, 14, 17, 0.4)"
                          p={3}
                          borderRadius="lg"
                          borderWidth="1px"
                          borderColor="rgba(43, 49, 57, 0.6)"
                        >
                          <VStack align="start" spacing={1}>
                            <Text color="#848E9C" fontSize="xs" fontWeight="500">
                              {t('investments.roi', 'ROI')}
                            </Text>
                            <Text color="#02C076" fontSize="sm" fontWeight="700">
                              +{pkg.roi.toFixed(2)}%
                            </Text>
                          </VStack>
                        </Box>
                        <Box
                          bg="rgba(11, 14, 17, 0.4)"
                          p={3}
                          borderRadius="lg"
                          borderWidth="1px"
                          borderColor="rgba(43, 49, 57, 0.6)"
                        >
                          <VStack align="start" spacing={1}>
                            <Text color="#848E9C" fontSize="xs" fontWeight="500">
                              {t('investments.dailyEarnings', 'Daily Earnings')}
                            </Text>
                            <Text color="#02C076" fontSize="sm" fontWeight="700">
                              {formatCryptocurrencyAmount(dailyEarnings, pkg.currency)}
                            </Text>
                          </VStack>
                        </Box>
                        <Box
                          bg="rgba(11, 14, 17, 0.4)"
                          p={3}
                          borderRadius="lg"
                          borderWidth="1px"
                          borderColor="rgba(43, 49, 57, 0.6)"
                        >
                          <VStack align="start" spacing={1}>
                            <Text color="#848E9C" fontSize="xs" fontWeight="500">
                              {t('investments.daysActive', 'Days Active')}
                            </Text>
                            <Text color="#EAECEF" fontSize="sm" fontWeight="700">
                              {pkg.activeDays}/{pkg.totalDays}
                            </Text>
                          </VStack>
                        </Box>
                      </SimpleGrid>

                      {/* Enhanced Progress Bar */}
                      <Box
                        bg="rgba(11, 14, 17, 0.6)"
                        p={4}
                        borderRadius="xl"
                        borderWidth="1px"
                        borderColor="rgba(43, 49, 57, 0.6)"
                        mb={4}
                      >
                        <VStack spacing={3}>
                          <HStack justify="space-between" w="100%">
                            <Text color="#848E9C" fontSize="sm" fontWeight="500">
                              {t('investments.progress', 'Progress')}
                            </Text>
                            <Text color="#FCD535" fontSize="sm" fontWeight="700">
                              {progress.toFixed(1)}%
                            </Text>
                          </HStack>
                          <Box w="100%" position="relative">
                            <Progress
                              value={progress}
                              size="md"
                              bg="rgba(43, 49, 57, 0.8)"
                              borderRadius="full"
                              w="100%"
                              sx={{
                                '& > div': {
                                  background: 'linear-gradient(90deg, #FCD535 0%, #F0B90B 100%)',
                                  borderRadius: 'full',
                                  boxShadow: '0 0 10px rgba(240, 185, 11, 0.3)'
                                }
                              }}
                            />
                          </Box>
                          <Text color="#848E9C" fontSize="xs" textAlign="center">
                            {t('investments.complete', 'complete')}
                          </Text>
                        </VStack>
                      </Box>

                      {/* Enhanced Package Actions with Deposit/Withdraw */}
                      <HStack spacing={{ base: 2, md: 3 }} justify="center">
                        {/* View Details Button */}
                        <Button
                          size="sm"
                          bg="rgba(240, 185, 11, 0.2)"
                          color="#FCD535"
                          borderWidth="1px"
                          borderColor="rgba(240, 185, 11, 0.3)"
                          borderRadius="lg"
                          minH={{ base: "36px", md: "40px" }}
                          minW={{ base: "36px", md: "40px" }}
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedPackage(pkg);
                          }}
                          transition="all 0.3s ease"
                          _hover={{
                            bg: "rgba(240, 185, 11, 0.3)",
                            borderColor: "rgba(240, 185, 11, 0.5)",
                            transform: "scale(1.05)"
                          }}
                          _active={{
                            transform: "scale(0.95)"
                          }}
                        >
                          <Icon as={FaEye} boxSize={{ base: 3, md: 4 }} />
                        </Button>

                        {/* Deposit Button */}
                        <Button
                          size="sm"
                          bg="linear-gradient(135deg, #FCD535 0%, #F0B90B 100%)"
                          color="#0B0E11"
                          borderWidth="1px"
                          borderColor="rgba(240, 185, 11, 0.3)"
                          borderRadius="lg"
                          minH={{ base: "36px", md: "40px" }}
                          minW={{ base: "36px", md: "40px" }}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDepositClick(pkg.currency);
                          }}
                          transition="all 0.3s ease"
                          _hover={{
                            bg: "linear-gradient(135deg, #F8D12F 0%, #FCD535 100%)",
                            transform: "scale(1.05)",
                            boxShadow: "0 4px 15px rgba(240, 185, 11, 0.3)"
                          }}
                          _active={{
                            transform: "scale(0.95)"
                          }}
                        >
                          <Icon as={FaPlus} boxSize={{ base: 3, md: 4 }} />
                        </Button>

                        {/* Withdraw Button */}
                        <Button
                          size="sm"
                          bg="rgba(248, 73, 96, 0.2)"
                          color="#F84960"
                          borderWidth="1px"
                          borderColor="rgba(248, 73, 96, 0.3)"
                          borderRadius="lg"
                          minH={{ base: "36px", md: "40px" }}
                          minW={{ base: "36px", md: "40px" }}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleWithdrawClick(pkg.currency);
                          }}
                          transition="all 0.3s ease"
                          _hover={{
                            bg: "rgba(248, 73, 96, 0.3)",
                            borderColor: "rgba(248, 73, 96, 0.5)",
                            transform: "scale(1.05)"
                          }}
                          _active={{
                            transform: "scale(0.95)"
                          }}
                        >
                          <Icon as={FaMinus} boxSize={{ base: 3, md: 4 }} />
                        </Button>

                        {/* History Button (only for active packages) */}
                        {pkg.status === 'active' && (
                          <Button
                            size="sm"
                            bg="rgba(2, 192, 118, 0.2)"
                            color="#02C076"
                            borderWidth="1px"
                            borderColor="rgba(2, 192, 118, 0.3)"
                            borderRadius="lg"
                            minH={{ base: "36px", md: "40px" }}
                            minW={{ base: "36px", md: "40px" }}
                            onClick={(e) => {
                              e.stopPropagation();
                              fetchDistributions(pkg._id);
                              onDistributionOpen();
                            }}
                            transition="all 0.3s ease"
                            _hover={{
                              bg: "rgba(2, 192, 118, 0.3)",
                              borderColor: "rgba(2, 192, 118, 0.5)",
                              transform: "scale(1.05)"
                            }}
                            _active={{
                              transform: "scale(0.95)"
                            }}
                          >
                            <Icon as={FaHistory} boxSize={{ base: 3, md: 4 }} />
                          </Button>
                        )}
                      </HStack>
                    </Box>
                  );
                })}
            </SimpleGrid>
          )}
        </Box>

        {/* Interest Distribution History Modal */}
        <Modal isOpen={isDistributionOpen} onClose={onDistributionClose} size="6xl">
          <ModalOverlay bg="blackAlpha.800" />
          <ModalContent bg="#1E2026" borderColor="#2B3139" borderWidth="1px">
            <ModalHeader color="#F0B90B" display="flex" alignItems="center">
              <Icon as={FaHistory} mr={2} />
              {t('investments.distributionHistory', 'Interest Distribution History')}
            </ModalHeader>
            <ModalCloseButton color="#848E9C" />
            <ModalBody pb={6}>
              {distributionsLoading ? (
                <Center py={10}>
                  <Spinner color="#F0B90B" size="lg" />
                </Center>
              ) : distributions.length === 0 ? (
                <VStack spacing={4} py={8}>
                  <Icon as={FaClock} color="#848E9C" boxSize={12} />
                  <Text color="#848E9C">
                    {t('investments.noDistributions', 'No interest distributions yet')}
                  </Text>
                  <Text color="#848E9C" fontSize="sm" textAlign="center">
                    {t('investments.distributionsWillAppear', 'Interest distributions will appear here after 03:00 UTC+3 daily')}
                  </Text>
                </VStack>
              ) : (
                <TableContainer>
                  <Table variant="simple" size="sm">
                    <Thead>
                      <Tr>
                        <Th color="#848E9C" borderColor="#2B3139">
                          {t('investments.distributionTable.date', 'Date')}
                        </Th>
                        <Th color="#848E9C" borderColor="#2B3139">
                          {t('investments.distributionTable.amount', 'Amount')}
                        </Th>
                        <Th color="#848E9C" borderColor="#2B3139">
                          {t('investments.distributionTable.type', 'Type')}
                        </Th>
                        <Th color="#848E9C" borderColor="#2B3139">
                          {t('investments.distributionTable.status', 'Status')}
                        </Th>
                        <Th color="#848E9C" borderColor="#2B3139">
                          {t('investments.distributionTable.txHash', 'Transaction')}
                        </Th>
                      </Tr>
                    </Thead>
                    <Tbody>
                      {distributions.map((distribution) => (
                        <Tr key={distribution._id} _hover={{ bg: "#2B3139" }}>
                          <Td borderColor="#2B3139">
                            <VStack align="start" spacing={0}>
                              <Text color="#EAECEF" fontSize="sm">
                                {new Date(distribution.distributionDate).toLocaleDateString()}
                              </Text>
                              <Text color="#848E9C" fontSize="xs">
                                {new Date(distribution.distributionDate).toLocaleTimeString()}
                              </Text>
                            </VStack>
                          </Td>
                          <Td borderColor="#2B3139">
                            <Text color="#02C076" fontSize="sm" fontWeight="bold">
                              {formatCryptocurrencyAmount(distribution.amount, distribution.cryptocurrency)}
                            </Text>
                          </Td>
                          <Td borderColor="#2B3139">
                            <Badge
                              bg={distribution.type === 'daily' ? '#F0B90B22' : '#02C07622'}
                              color={distribution.type === 'daily' ? '#F0B90B' : '#02C076'}
                              px={2}
                              py={1}
                              borderRadius="full"
                              fontSize="xs"
                              textTransform="capitalize"
                            >
                              {distribution.type}
                            </Badge>
                          </Td>
                          <Td borderColor="#2B3139">
                            <Badge
                              bg={distribution.status === 'completed' ? '#02C076' : '#F0B90B'}
                              color="white"
                              px={2}
                              py={1}
                              borderRadius="full"
                              fontSize="xs"
                              textTransform="capitalize"
                            >
                              {distribution.status}
                            </Badge>
                          </Td>
                          <Td borderColor="#2B3139">
                            <HStack spacing={2}>
                              <Text fontSize="xs" color="#848E9C" maxW="100px" isTruncated>
                                {distribution.transactionHash.slice(0, 10)}...
                              </Text>
                              <Button
                                size="xs"
                                variant="ghost"
                                color="#848E9C"
                                _hover={{ color: "#F0B90B" }}
                                onClick={() => {
                                  navigator.clipboard.writeText(distribution.transactionHash);
                                  toast({
                                    title: t('common.copied', 'Transaction hash copied!'),
                                    status: "success",
                                    duration: 2000,
                                    isClosable: true,
                                  });
                                }}
                              >
                                <Icon as={FaCopy} />
                              </Button>
                            </HStack>
                          </Td>
                        </Tr>
                      ))}
                    </Tbody>
                  </Table>
                </TableContainer>
              )}
            </ModalBody>
          </ModalContent>
        </Modal>

        {/* Deposit Modal */}
        <DepositModal
          isOpen={isDepositModalOpen}
          onClose={onDepositModalClose}
          defaultAsset={selectedCrypto}
          onSuccess={() => {
            fetchInvestmentData();
            fetchInvestmentBalances();
            toast({
              title: t('investments.depositSuccess', 'Deposit Successful'),
              description: t('investments.depositSuccessDesc', 'Your deposit has been submitted successfully.'),
              status: 'success',
              duration: 5000,
              isClosable: true,
            });
          }}
        />

        {/* Withdraw Modal */}
        <ThreeStepWithdrawModal
          isOpen={isWithdrawModalOpen}
          onClose={onWithdrawModalClose}
          initialCrypto={selectedCrypto}
          initialWithdrawalType="interest"
        />
      </VStack>
    </Box>
  );
};

export default InvestmentTab;
