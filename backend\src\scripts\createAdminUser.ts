import mongoose from 'mongoose';
import dotenv from 'dotenv';
import User from '../models/userModel';
import { db } from '../config/database';
import readline from 'readline';

// Load environment variables
dotenv.config();

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Prompt for user input
const prompt = (question: string): Promise<string> => {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
};

// Validate email
const isValidEmail = (email: string): boolean => {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
};

// Validate password
const isStrongPassword = (password: string): boolean => {
  return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/.test(password);
};

// Create admin user
const createAdminUser = async () => {
  try {
    await db.connect();
    console.log('MongoDB connected');

    // Get user input
    const email = await prompt('Enter admin email: ');
    if (!isValidEmail(email)) {
      console.error('Invalid email format');
      process.exit(1);
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      console.log(`User with email ${email} already exists`);
      const updateExisting = await prompt('Do you want to update this user to admin? (y/n): ');
      
      if (updateExisting.toLowerCase() === 'y') {
        existingUser.isAdmin = true;
        await existingUser.save();
        console.log(`User ${email} has been updated to admin`);
      } else {
        console.log('Operation cancelled');
      }
      
      rl.close();
      process.exit(0);
    }

    // Get user details
    const firstName = await prompt('Enter first name: ');
    const lastName = await prompt('Enter last name: ');
    let password = await prompt('Enter password (min 8 chars, must include uppercase, lowercase, number, special char): ');
    
    while (!isStrongPassword(password)) {
      console.log('Password is not strong enough. It must contain at least 8 characters, including uppercase, lowercase, number, and special character.');
      password = await prompt('Enter password: ');
    }

    // Create admin user
    const user = await User.create({
      firstName,
      lastName,
      email,
      password,
      isAdmin: true
    });

    console.log(`Admin user created successfully`);
    console.log(`Email: ${email}`);
    console.log(`Name: ${firstName} ${lastName}`);

    rl.close();
    process.exit(0);
  } catch (error) {
    console.error('Error creating admin user:', error);
    rl.close();
    process.exit(1);
  }
};

createAdminUser();
