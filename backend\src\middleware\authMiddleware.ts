import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import User from '../models/userModel';

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: any;
    }
  }
}

export const protect = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  // In development mode, bypass authentication only if no token is provided
  if (process.env.NODE_ENV === 'development') {
    // Check if token exists in cookies or headers
    const hasToken = (req.cookies && req.cookies.token) ||
                    (req.headers.authorization && req.headers.authorization.startsWith('Bearer'));

    // If no token is provided in development mode, use a mock user
    if (!hasToken) {
      console.log('[DEV MODE] No token provided, using mock user');
      // Set a mock admin user with a fixed ID for consistency
      const fixedDevUserId = '6821c94cd4856eb7460326ad';
      req.user = {
        _id: fixedDevUserId,
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        isAdmin: true
      };
      return next();
    }

    // If token is provided, continue with normal authentication
    console.log('[DEV MODE] Token provided, proceeding with normal authentication');
  }

  let token: string | undefined;

  // Log request details for debugging
  console.log('Request URL:', req.originalUrl);
  console.log('Request method:', req.method);
  console.log('Request headers:', JSON.stringify(req.headers, null, 2));
  console.log('Request cookies:', JSON.stringify(req.cookies, null, 2));

  // Check if token exists in cookies first (preferred method)
  if (req.cookies && req.cookies.token) {
    token = req.cookies.token;
    console.log('Token found in cookies');
  }
  // Fallback to Authorization header for backward compatibility
  else if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
    console.log('Token found in Authorization header');
  }

  if (token) {
    try {
      // Log token for debugging (only show prefix for security)
      console.log('Token received:', token ? token.substring(0, 10) + '...' : 'none');

      // Verify token
      const decoded: any = jwt.verify(token, process.env.JWT_SECRET || 'fallback_secret');

      // Log decoded token for debugging
      console.log('Decoded token:', decoded ? { id: decoded.id, iat: decoded.iat, exp: decoded.exp } : 'none');

      // Get user from the token
      const user = await User.findById(decoded.id).select('-password');

      // Log user for debugging
      console.log('User found:', user ? { _id: user._id, email: user.email, isAdmin: user.isAdmin } : 'none');

      if (!user) {
        console.error('User not found for token');
        // Clear the invalid cookie
        res.clearCookie('token');
        res.clearCookie('adminToken');
        res.status(401).json({ message: 'Not authorized, user not found' });
        return;
      }

      // Set user in request object
      req.user = user;

      // Always ensure the admin cookie is set correctly based on user.isAdmin status
      if (user.isAdmin) {
        // Set admin cookie if user is admin
        res.cookie('adminToken', 'true', {
          httpOnly: false, // Allow JavaScript access for UI state
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          maxAge: 30 * 24 * 60 * 60 * 1000,
          path: '/'
        });
      } else {
        // Clear admin cookie if user is not admin
        res.clearCookie('adminToken');
      }

      return next();
    } catch (error: any) {
      console.error('Token verification error:', error);
      console.error('JWT Secret length:', (process.env.JWT_SECRET || 'fallback_secret').length);

      // Clear the invalid cookies
      res.clearCookie('token');
      res.clearCookie('adminToken');

      res.status(401).json({
        message: 'Not authorized, token failed',
        error: error.message || 'Unknown error',
        tokenInfo: token ? {
          length: token.length,
          prefix: token.substring(0, 10) + '...'
        } : 'No token'
      });
      return;
    }
  }

  console.error('No token provided');
  res.status(401).json({ message: 'Not authorized, no token' });
  return;
};

// Admin middleware
export const admin = (req: Request, res: Response, next: NextFunction): void => {
  // In development mode, bypass admin check
  if (process.env.NODE_ENV === 'development') {
    console.log('[DEV MODE] Bypassing admin check');
    return next();
  }

  if (!req.user) {
    res.status(401).json({
      message: 'Not authorized, no user found',
      code: 'NO_USER'
    });
    return;
  }

  if (req.user.isAdmin) {
    next();
  } else {
    console.log(`User ${req.user._id} attempted to access admin route but is not an admin`);
    res.status(403).json({
      message: 'Not authorized as an admin',
      code: 'NOT_ADMIN'
    });
  }
};
