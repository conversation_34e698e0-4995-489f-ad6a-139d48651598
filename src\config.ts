// API URL from environment variables
export const API_URL = import.meta.env.VITE_API_URL || 'https://api.shpnfinance.com/api';

// Contract Address
export const CONTRACT_ADDRESS = import.meta.env.VITE_CONTRACT_ADDRESS || '******************************************';

// Infura ID for Web3
export const INFURA_ID = import.meta.env.VITE_INFURA_ID || '********************************';

// Storage encryption key
export const STORAGE_KEY = import.meta.env.VITE_STORAGE_KEY || 'crypto_yield_hub_dev_key';

// Feature flags
export const ENABLE_TESTNET = import.meta.env.VITE_ENABLE_TESTNET === 'true';
export const ENABLE_FAUCET = import.meta.env.VITE_ENABLE_FAUCET === 'true';
export const ENABLE_ANALYTICS = import.meta.env.VITE_ENABLE_ANALYTICS === 'true';

// WebSocket URL
export const WS_URL = import.meta.env.VITE_WS_URL || 'ws://localhost:5000';

// Application settings
export const APP_NAME = 'Shipping Finance';
export const APP_DESCRIPTION = 'Secure Crypto Investment Platform';
