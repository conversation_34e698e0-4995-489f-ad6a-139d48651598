import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import json
import os
import time
import random
import threading
import sys
from datetime import datetime, timedelta

# Uygulama versiyonu
APP_VERSION = "2.0.0"

# Hata ayıklama modu
DEBUG = False

class CryptoBasrikoApp:
    def __init__(self, root):
        self.root = root
        self.root.title(f"CryptoBasriko Otomasyon v{APP_VERSION}")
        self.root.geometry("1200x700")
        self.root.minsize(1000, 600)
        self.root.configure(bg="#0B0E11")

        # Uygulama ikonu
        try:
            icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "assets", "icon.ico")
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except Exception as e:
            if DEBUG:
                print(f"İkon yüklenemedi: {e}")

        # Uygulama verileri
        self.user = None
        self.portfolio = {}
        self.crypto_prices = {
            "BTC": 60000.0,
            "ETH": 3000.0,
            "BNB": 500.0,
            "SOL": 150.0,
            "ADA": 1.2,
            "USDT": 1.0,
            "XRP": 0.6,
            "DOT": 15.0,
            "DOGE": 0.08,
            "SHIB": 0.00001
        }
        self.commission_rate = 0.05  # %5
        self.interest_rates = {
            "BTC": 0.03,  # %3
            "ETH": 0.04,  # %4
            "BNB": 0.05,  # %5
            "SOL": 0.06,  # %6
            "ADA": 0.07,  # %7
            "USDT": 0.08,  # %8
            "XRP": 0.05,  # %5
            "DOT": 0.06,  # %6
            "DOGE": 0.04,  # %4
            "SHIB": 0.09,  # %9
            "default": 0.05  # %5
        }

        # Uygulama durumu
        self.is_refreshing = False
        self.auto_refresh = False
        self.auto_refresh_interval = 60  # saniye
        self.refresh_thread = None

        # Veri dosyaları
        self.app_dir = self._get_app_directory()
        self.data_dir = os.path.join(self.app_dir, "data")
        os.makedirs(self.data_dir, exist_ok=True)
        self.users_file = os.path.join(self.data_dir, "users.json")
        self.portfolios_file = os.path.join(self.data_dir, "portfolios.json")
        self.settings_file = os.path.join(self.data_dir, "settings.json")

        # Ayarları yükle
        self.load_settings()

        # Kullanıcı ve portföy verilerini yükle
        self.load_data()

        # Uygulama kapatıldığında
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Giriş ekranını göster
        self.show_login_screen()

    def _get_app_directory(self):
        """Uygulama verilerinin saklanacağı dizini belirle"""
        if getattr(sys, 'frozen', False):
            # PyInstaller ile paketlenmiş uygulama
            app_dir = os.path.dirname(sys.executable)
        else:
            # Normal Python scripti
            app_dir = os.path.dirname(os.path.abspath(__file__))

        return app_dir

    def load_settings(self):
        """Uygulama ayarlarını yükle"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r') as f:
                    settings = json.load(f)

                # Ayarları uygula
                self.commission_rate = settings.get("commission_rate", 0.01)
                self.interest_rates = settings.get("interest_rates", self.interest_rates)
                self.auto_refresh = settings.get("auto_refresh", False)
                self.auto_refresh_interval = settings.get("auto_refresh_interval", 60)
            else:
                # Varsayılan ayarları kaydet
                self.save_settings()
        except Exception as e:
            if DEBUG:
                print(f"Ayarlar yüklenirken hata: {e}")

    def save_settings(self):
        """Uygulama ayarlarını kaydet"""
        try:
            settings = {
                "commission_rate": self.commission_rate,
                "interest_rates": self.interest_rates,
                "auto_refresh": self.auto_refresh,
                "auto_refresh_interval": self.auto_refresh_interval
            }

            with open(self.settings_file, 'w') as f:
                json.dump(settings, f, indent=4)
        except Exception as e:
            if DEBUG:
                print(f"Ayarlar kaydedilirken hata: {e}")

    def on_closing(self):
        """Uygulama kapatılırken"""
        # Otomatik yenileme varsa durdur
        if self.refresh_thread and self.refresh_thread.is_alive():
            self.auto_refresh = False
            self.refresh_thread.join(1)

        # Ayarları kaydet
        self.save_settings()

        # Uygulamayı kapat
        self.root.destroy()

    def load_data(self):
        # Kullanıcı verilerini yükle
        if os.path.exists(self.users_file):
            with open(self.users_file, 'r') as f:
                self.users = json.load(f)
        else:
            # Demo kullanıcı oluştur
            self.users = {
                "demo": {
                    "id": "demo",
                    "username": "demo",
                    "password": "demo123",
                    "email": "<EMAIL>",
                    "firstName": "Demo",
                    "lastName": "User",
                    "walletAddress": "******************************************",
                    "created_at": datetime.now().isoformat()
                }
            }
            self.save_users()

        # Portföy verilerini yükle
        if os.path.exists(self.portfolios_file):
            with open(self.portfolios_file, 'r') as f:
                self.portfolios = json.load(f)
        else:
            # Demo portföy oluştur
            self.portfolios = {
                "demo": {
                    "assets": [
                        {
                            "symbol": "BTC",
                            "balance": 0.5,
                            "commissionBalance": 0.005,
                            "interestBalance": 0,
                            "mode": "commission"
                        },
                        {
                            "symbol": "ETH",
                            "balance": 5,
                            "commissionBalance": 0.05,
                            "interestBalance": 0,
                            "mode": "commission"
                        },
                        {
                            "symbol": "USDT",
                            "balance": 10000,
                            "commissionBalance": 100,
                            "interestBalance": 0,
                            "mode": "commission"
                        }
                    ],
                    "total_commission_earned": 105.05,
                    "total_interest_earned": 0,
                    "last_updated": datetime.now().isoformat()
                }
            }
            self.save_portfolios()

    def save_users(self):
        with open(self.users_file, 'w') as f:
            json.dump(self.users, f, indent=4)

    def save_portfolios(self):
        with open(self.portfolios_file, 'w') as f:
            json.dump(self.portfolios, f, indent=4)

    def show_login_screen(self):
        # Mevcut widget'ları temizle
        for widget in self.root.winfo_children():
            widget.destroy()

        # Ana frame
        main_frame = tk.Frame(self.root, bg="#0B0E11")
        main_frame.pack(expand=True, fill="both", padx=20, pady=20)

        # Logo ve başlık
        logo_frame = tk.Frame(main_frame, bg="#0B0E11")
        logo_frame.pack(pady=20)

        logo_box = tk.Frame(logo_frame, bg="#F0B90B", width=40, height=40)
        logo_box.pack(side="left", padx=5)

        logo_inner = tk.Frame(logo_box, bg="#0B0E11", width=30, height=30)
        logo_inner.place(relx=0.5, rely=0.5, anchor="center")

        title_label = tk.Label(logo_frame, text="CryptoBasriko", font=("Arial", 24, "bold"), fg="#F0B90B", bg="#0B0E11")
        title_label.pack(side="left", padx=5)

        # Giriş formu
        login_frame = tk.Frame(main_frame, bg="#1E2329", padx=30, pady=30, bd=1, relief="solid")
        login_frame.pack(pady=20)

        # Kullanıcı adı
        username_label = tk.Label(login_frame, text="Kullanıcı Adı", font=("Arial", 10), fg="#848E9C", bg="#1E2329")
        username_label.pack(anchor="w", pady=(0, 5))

        username_entry = tk.Entry(login_frame, font=("Arial", 12), bg="#0B0E11", fg="#EAECEF",
                                 insertbackground="#EAECEF", width=30)
        username_entry.pack(pady=(0, 15), ipady=8)
        username_entry.insert(0, "demo")

        # Şifre
        password_label = tk.Label(login_frame, text="Şifre", font=("Arial", 10), fg="#848E9C", bg="#1E2329")
        password_label.pack(anchor="w", pady=(0, 5))

        password_entry = tk.Entry(login_frame, font=("Arial", 12), bg="#0B0E11", fg="#EAECEF",
                                 insertbackground="#EAECEF", width=30, show="*")
        password_entry.pack(pady=(0, 20), ipady=8)
        password_entry.insert(0, "demo123")

        # Giriş butonu
        login_button = tk.Button(login_frame, text="Giriş Yap", font=("Arial", 12, "bold"),
                                bg="#F0B90B", fg="#0B0E11", activebackground="#F8D12F",
                                activeforeground="#0B0E11", width=25, height=2,
                                command=lambda: self.login(username_entry.get(), password_entry.get()))
        login_button.pack(pady=10)

    def login(self, username, password):
        if username in self.users and self.users[username]["password"] == password:
            self.user = self.users[username]
            self.portfolio = self.portfolios.get(username, {
                "assets": [],
                "total_commission_earned": 0,
                "total_interest_earned": 0,
                "last_updated": datetime.now().isoformat()
            })
            self.show_dashboard()
        else:
            messagebox.showerror("Hata", "Kullanıcı adı veya şifre hatalı!")

    def show_dashboard(self):
        # Mevcut widget'ları temizle
        for widget in self.root.winfo_children():
            widget.destroy()

        # Ana frame
        main_frame = tk.Frame(self.root, bg="#0B0E11")
        main_frame.pack(expand=True, fill="both")

        # Üst menü
        top_frame = tk.Frame(main_frame, bg="#1E2329", height=60)
        top_frame.pack(fill="x")

        logo_frame = tk.Frame(top_frame, bg="#1E2329")
        logo_frame.pack(side="left", padx=20, pady=10)

        logo_box = tk.Frame(logo_frame, bg="#F0B90B", width=30, height=30)
        logo_box.pack(side="left", padx=5)

        logo_inner = tk.Frame(logo_box, bg="#1E2329", width=20, height=20)
        logo_inner.place(relx=0.5, rely=0.5, anchor="center")

        title_label = tk.Label(logo_frame, text="CryptoBasriko", font=("Arial", 16, "bold"), fg="#F0B90B", bg="#1E2329")
        title_label.pack(side="left", padx=5)

        # Kullanıcı bilgisi
        user_frame = tk.Frame(top_frame, bg="#1E2329")
        user_frame.pack(side="right", padx=20, pady=10)

        settings_button = tk.Button(user_frame, text="Ayarlar", font=("Arial", 10),
                                  bg="#2B3139", fg="#EAECEF", activebackground="#3B4149",
                                  activeforeground="#EAECEF", command=self.show_settings_dialog)
        settings_button.pack(side="left", padx=5)

        user_label = tk.Label(user_frame, text=f"{self.user['firstName']} {self.user['lastName']}",
                             font=("Arial", 12), fg="#EAECEF", bg="#1E2329")
        user_label.pack(side="left", padx=10)

        logout_button = tk.Button(user_frame, text="Çıkış", font=("Arial", 10),
                                 bg="#2B3139", fg="#EAECEF", activebackground="#3B4149",
                                 activeforeground="#EAECEF", command=self.show_login_screen)
        logout_button.pack(side="left", padx=5)

        # Ana içerik
        content_frame = tk.Frame(main_frame, bg="#0B0E11")
        content_frame.pack(expand=True, fill="both", padx=20, pady=20)

        # Cezbedici Banner
        banner_frame = tk.Frame(content_frame, bg="#0B0E11", bd=1, relief="solid", highlightbackground="#F0B90B", highlightthickness=1)
        banner_frame.pack(fill="x", pady=(0, 20))

        banner_content = tk.Frame(banner_frame, bg="#0B0E11", padx=20, pady=15)
        banner_content.pack(fill="both", expand=True)

        tk.Label(banner_content, text="Anında %5 Komisyon Kazanın!", font=("Arial", 16, "bold"),
                fg="#F0B90B", bg="#0B0E11").pack(anchor="w")

        tk.Label(banner_content, text="Bitcoin yatırımlarınızdan hemen %5 komisyon kazanmaya başlayın. Güvenli, hızlı ve tamamen şeffaf.",
                font=("Arial", 10), fg="#EAECEF", bg="#0B0E11").pack(anchor="w", pady=5)

        tk.Label(banner_content, text='"Kripto dünyasında güvenilir yatırımın adresi CryptoBasriko - Paranız Güvende, Kazancınız Garantide!"',
                font=("Arial", 9, "italic"), fg="#848E9C", bg="#0B0E11").pack(anchor="w", pady=5)

        deposit_banner_button = tk.Button(banner_content, text="Hemen Yatırım Yap", font=("Arial", 10, "bold"),
                                        bg="#F0B90B", fg="#0B0E11", activebackground="#F8D12F",
                                        activeforeground="#0B0E11", padx=15, pady=5,
                                        command=self.show_deposit_dialog)
        deposit_banner_button.pack(anchor="e")

        # Özet kartları
        summary_frame = tk.Frame(content_frame, bg="#0B0E11")
        summary_frame.pack(fill="x", pady=10)

        # Toplam Değer
        total_value = 0
        for asset in self.portfolio.get("assets", []):
            price = self.crypto_prices.get(asset["symbol"], 0)
            total_value += asset["balance"] * price

        card1 = tk.Frame(summary_frame, bg="#1E2329", padx=20, pady=15, bd=1, relief="solid")
        card1.pack(side="left", expand=True, fill="both", padx=5)

        tk.Label(card1, text="Toplam Portföy Değeri", font=("Arial", 10), fg="#848E9C", bg="#1E2329").pack(anchor="w")
        tk.Label(card1, text=f"${total_value:,.2f}", font=("Arial", 18, "bold"), fg="#EAECEF", bg="#1E2329").pack(anchor="w", pady=5)

        # Toplam Komisyon
        card2 = tk.Frame(summary_frame, bg="#1E2329", padx=20, pady=15, bd=1, relief="solid")
        card2.pack(side="left", expand=True, fill="both", padx=5)

        tk.Label(card2, text="Toplam Kazanılan Komisyon", font=("Arial", 10), fg="#848E9C", bg="#1E2329").pack(anchor="w")
        tk.Label(card2, text=f"${self.portfolio.get('total_commission_earned', 0):,.2f}",
                font=("Arial", 18, "bold"), fg="#0ECB81", bg="#1E2329").pack(anchor="w", pady=5)

        # Toplam Faiz
        card3 = tk.Frame(summary_frame, bg="#1E2329", padx=20, pady=15, bd=1, relief="solid")
        card3.pack(side="left", expand=True, fill="both", padx=5)

        tk.Label(card3, text="Toplam Kazanılan Faiz", font=("Arial", 10), fg="#848E9C", bg="#1E2329").pack(anchor="w")
        tk.Label(card3, text=f"${self.portfolio.get('total_interest_earned', 0):,.2f}",
                font=("Arial", 18, "bold"), fg="#F0B90B", bg="#1E2329").pack(anchor="w", pady=5)

        # İşlem butonları
        action_frame = tk.Frame(content_frame, bg="#0B0E11")
        action_frame.pack(fill="x", pady=20)

        deposit_button = tk.Button(action_frame, text="Para Yatır", font=("Arial", 12),
                                  bg="#0ECB81", fg="white", activebackground="#0CA875",
                                  activeforeground="white", padx=15, pady=5,
                                  command=self.show_deposit_dialog)
        deposit_button.pack(side="left", padx=5)

        withdraw_button = tk.Button(action_frame, text="Para Çek", font=("Arial", 12),
                                   bg="#F6465D", fg="white", activebackground="#E03E54",
                                   activeforeground="white", padx=15, pady=5,
                                   command=self.show_withdraw_dialog)
        withdraw_button.pack(side="left", padx=5)

        mode_button = tk.Button(action_frame, text="Mod Değiştir", font=("Arial", 12),
                               bg="#F0B90B", fg="#0B0E11", activebackground="#F8D12F",
                               activeforeground="#0B0E11", padx=15, pady=5,
                               command=self.show_mode_dialog)
        mode_button.pack(side="left", padx=5)

        refresh_button = tk.Button(action_frame, text="Yenile", font=("Arial", 12),
                                  bg="#2B3139", fg="#EAECEF", activebackground="#3B4149",
                                  activeforeground="#EAECEF", padx=15, pady=5,
                                  command=lambda: self.refresh_data(True))
        refresh_button.pack(side="right", padx=5)

        auto_refresh_text = "Otomatik Yenileme Kapat" if self.auto_refresh else "Otomatik Yenileme Aç"
        auto_refresh_button = tk.Button(action_frame, text=auto_refresh_text, font=("Arial", 12),
                                      bg="#2B3139", fg="#EAECEF", activebackground="#3B4149",
                                      activeforeground="#EAECEF", padx=15, pady=5,
                                      command=self.toggle_auto_refresh)
        auto_refresh_button.pack(side="right", padx=5)

        # Varlıklar tablosu
        assets_frame = tk.Frame(content_frame, bg="#1E2329", bd=1, relief="solid")
        assets_frame.pack(expand=True, fill="both", pady=10)

        tk.Label(assets_frame, text="Varlıklarım", font=("Arial", 14, "bold"), fg="#EAECEF", bg="#1E2329").pack(anchor="w", padx=20, pady=10)

        # Tablo başlıkları
        headers_frame = tk.Frame(assets_frame, bg="#0B0E11")
        headers_frame.pack(fill="x", padx=20, pady=5)

        headers = ["Varlık", "Bakiye", "USD Değeri", "Mod", "Komisyon", "Faiz", "İşlemler"]
        widths = [100, 150, 150, 100, 150, 150, 150]

        for i, header in enumerate(headers):
            tk.Label(headers_frame, text=header, font=("Arial", 10, "bold"), fg="#848E9C", bg="#0B0E11",
                    width=widths[i]//10).pack(side="left", padx=2)

        # Varlık listesi
        assets_list_frame = tk.Frame(assets_frame, bg="#1E2329")
        assets_list_frame.pack(expand=True, fill="both", padx=20, pady=5)

        # Kaydırma çubuğu
        canvas = tk.Canvas(assets_list_frame, bg="#1E2329", highlightthickness=0)
        scrollbar = ttk.Scrollbar(assets_list_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#1E2329")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(
                scrollregion=canvas.bbox("all")
            )
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Varlıkları listele
        for asset in self.portfolio.get("assets", []):
            asset_frame = tk.Frame(scrollable_frame, bg="#1E2329", pady=10)
            asset_frame.pack(fill="x")

            symbol = asset["symbol"]
            balance = asset["balance"]
            price = self.crypto_prices.get(symbol, 0)
            value = balance * price
            mode = asset["mode"]
            commission = asset.get("commissionBalance", 0)
            interest = asset.get("interestBalance", 0)

            tk.Label(asset_frame, text=symbol, font=("Arial", 12, "bold"), fg="#EAECEF", bg="#1E2329",
                    width=widths[0]//10).pack(side="left", padx=2)

            tk.Label(asset_frame, text=f"{balance:,.8f}", font=("Arial", 12), fg="#EAECEF", bg="#1E2329",
                    width=widths[1]//10).pack(side="left", padx=2)

            tk.Label(asset_frame, text=f"${value:,.2f}", font=("Arial", 12), fg="#EAECEF", bg="#1E2329",
                    width=widths[2]//10).pack(side="left", padx=2)

            mode_label = tk.Label(asset_frame, text=mode.capitalize(), font=("Arial", 12), bg="#1E2329",
                                 width=widths[3]//10)
            mode_label.pack(side="left", padx=2)

            if mode == "commission":
                mode_label.config(fg="#F0B90B")
            else:
                mode_label.config(fg="#0ECB81")

            tk.Label(asset_frame, text=f"{commission:,.8f} {symbol}", font=("Arial", 12), fg="#EAECEF", bg="#1E2329",
                    width=widths[4]//10).pack(side="left", padx=2)

            tk.Label(asset_frame, text=f"{interest:,.8f} {symbol}", font=("Arial", 12), fg="#EAECEF", bg="#1E2329",
                    width=widths[5]//10).pack(side="left", padx=2)

            actions_frame = tk.Frame(asset_frame, bg="#1E2329")
            actions_frame.pack(side="left", padx=2)

            tk.Button(actions_frame, text="İşlem", font=("Arial", 10), bg="#F0B90B", fg="#0B0E11",
                     activebackground="#F8D12F", activeforeground="#0B0E11", padx=10,
                     command=lambda s=symbol: self.show_trade_dialog(s)).pack(side="left", padx=2)

        # Alt kısım - Kullanıcı Yorumları ve Profesyonel Mesajlar
        bottom_frame = tk.Frame(content_frame, bg="#0B0E11")
        bottom_frame.pack(fill="x", pady=(20, 0))

        # Kullanıcı Yorumları Başlığı
        testimonials_header = tk.Frame(bottom_frame, bg="#1E2329", padx=15, pady=10)
        testimonials_header.pack(fill="x", pady=(0, 10))

        tk.Label(testimonials_header, text="Kullanıcılarımız Ne Diyor?", font=("Arial", 14, "bold"),
                fg="#F0B90B", bg="#1E2329").pack(anchor="w")

        # Kullanıcı Yorumları
        testimonials_frame = tk.Frame(bottom_frame, bg="#0B0E11")
        testimonials_frame.pack(fill="x")

        testimonials = [
            {"name": "Ahmet Y.", "comment": "CryptoBasriko ile yatırımlarımdan %5 ek kazanç sağlıyorum. Komisyonlar her zaman zamanında ödeniyor.", "membership": "8 aydır üye"},
            {"name": "Ayşe K.", "comment": "Diğer platformları denedim ama hiçbiri CryptoBasriko kadar güvenilir ve kullanıcı dostu değildi.", "membership": "1 yıldır üye"},
            {"name": "Mehmet S.", "comment": "Müşteri hizmetleri harika! Bir sorunum olduğunda anında yanıt alıyorum. Kesinlikle tavsiye ederim.", "membership": "6 aydır üye"}
        ]

        for i, testimonial in enumerate(testimonials):
            testimonial_frame = tk.Frame(testimonials_frame, bg="#1E2329", padx=15, pady=15, bd=1, relief="solid")
            testimonial_frame.pack(side="left", expand=True, fill="both", padx=5)

            tk.Label(testimonial_frame, text=f"\"{testimonial['comment']}\"", font=("Arial", 9, "italic"),
                    fg="#EAECEF", bg="#1E2329", wraplength=250, justify="left").pack(anchor="w", pady=(0, 10))

            tk.Label(testimonial_frame, text=f"{testimonial['name']} - {testimonial['membership']}", font=("Arial", 8, "bold"),
                    fg="#F0B90B", bg="#1E2329").pack(anchor="w")

        # Güven Göstergeleri
        trust_frame = tk.Frame(bottom_frame, bg="#0B0E11", pady=20)
        trust_frame.pack(fill="x")

        indicators = [
            {"icon": "👥", "value": "10,000+", "label": "Aktif Kullanıcı"},
            {"icon": "💰", "value": "$5M+", "label": "İşlem Hacmi"},
            {"icon": "🔒", "value": "%100", "label": "Güvenli İşlem"},
            {"icon": "⭐", "value": "4.9/5", "label": "Kullanıcı Puanı"}
        ]

        for indicator in indicators:
            indicator_frame = tk.Frame(trust_frame, bg="#0B0E11")
            indicator_frame.pack(side="left", expand=True)

            tk.Label(indicator_frame, text=indicator["icon"], font=("Arial", 20),
                    fg="#F0B90B", bg="#0B0E11").pack(anchor="center")

            tk.Label(indicator_frame, text=indicator["value"], font=("Arial", 14, "bold"),
                    fg="#EAECEF", bg="#0B0E11").pack(anchor="center")

            tk.Label(indicator_frame, text=indicator["label"], font=("Arial", 9),
                    fg="#848E9C", bg="#0B0E11").pack(anchor="center")

        # Profesyonel Mesaj
        message_frame = tk.Frame(bottom_frame, bg="#0B0E11", padx=20, pady=15)
        message_frame.pack(fill="x", pady=(10, 0))

        message = random.choice([
            "Kripto dünyasında güvenilir yatırımın adresi CryptoBasriko - Paranız Güvende, Kazancınız Garantide!",
            "Yatırımlarınızdan anında %5 komisyon kazanın - CryptoBasriko farkıyla!",
            "10.000+ mutlu kullanıcı, 5 milyon dolar+ işlem hacmi - Güvenin adresi CryptoBasriko!",
            "7/24 müşteri desteği ve %100 şeffaf işlemler - CryptoBasriko ile kazanmaya başlayın!",
            "En yüksek komisyon oranı, en güvenilir platform - CryptoBasriko ile kazançlarınızı katlayın!"
        ])

        tk.Label(message_frame, text=message, font=("Arial", 10, "italic"),
                fg="#F0B90B", bg="#0B0E11", wraplength=800, justify="center").pack(anchor="center")

    def show_deposit_dialog(self):
        # Varlık seçimi
        symbols = [asset["symbol"] for asset in self.portfolio.get("assets", [])]
        if not symbols:
            symbols = ["BTC", "ETH", "USDT"]

        symbol = simpledialog.askstring("Para Yatır", "Hangi kripto para birimini yatırmak istiyorsunuz?",
                                       initialvalue=symbols[0])

        if not symbol:
            return

        # Miktar girişi
        amount_str = simpledialog.askstring("Para Yatır", f"Ne kadar {symbol} yatırmak istiyorsunuz?")

        if not amount_str:
            return

        try:
            amount = float(amount_str)
            if amount <= 0:
                messagebox.showerror("Hata", "Geçerli bir miktar giriniz!")
                return
        except ValueError:
            messagebox.showerror("Hata", "Geçerli bir miktar giriniz!")
            return

        # Mod seçimi
        mode = simpledialog.askstring("Para Yatır", "Mod seçin (commission/interest):",
                                     initialvalue="commission")

        if mode not in ["commission", "interest"]:
            mode = "commission"

        # Komisyon hesapla
        commission = amount * self.commission_rate if mode == "commission" else 0
        commission_usd = commission * self.crypto_prices.get(symbol, 0)

        # Onay mesajı göster
        confirm = messagebox.askyesno(
            "Komisyon Bilgisi",
            f"Yatıracağınız {amount} {symbol} için:\n\n"
            f"Kazanacağınız Komisyon: {commission:.8f} {symbol} (${commission_usd:.2f})\n"
            f"Komisyon Oranı: %{self.commission_rate * 100}\n\n"
            f"İşlemi onaylıyor musunuz?"
        )

        if not confirm:
            return

        # Varlık güncelle
        asset_exists = False
        for asset in self.portfolio.get("assets", []):
            if asset["symbol"] == symbol:
                asset_exists = True
                asset["balance"] += amount
                asset["mode"] = mode

                # Komisyon ekle
                if mode == "commission":
                    asset["commissionBalance"] = asset.get("commissionBalance", 0) + commission
                    self.portfolio["total_commission_earned"] = self.portfolio.get("total_commission_earned", 0) + commission_usd

                break

        if not asset_exists:
            new_asset = {
                "symbol": symbol,
                "balance": amount,
                "commissionBalance": commission,
                "interestBalance": 0,
                "mode": mode
            }

            if "assets" not in self.portfolio:
                self.portfolio["assets"] = []

            self.portfolio["assets"].append(new_asset)

            if mode == "commission":
                self.portfolio["total_commission_earned"] = self.portfolio.get("total_commission_earned", 0) + commission_usd

        self.portfolio["last_updated"] = datetime.now().isoformat()
        self.save_portfolios()

        # Başarı mesajı
        messagebox.showinfo(
            "İşlem Başarılı",
            f"{amount} {symbol} başarıyla yatırıldı!\n\n"
            f"Kazandığınız Komisyon: {commission:.8f} {symbol} (${commission_usd:.2f})\n\n"
            f"Toplam {symbol} Varlığınız: {amount:.8f} {symbol}\n\n"
            f"CryptoBasriko'yu tercih ettiğiniz için teşekkür ederiz!"
        )

        self.show_dashboard()

    def show_withdraw_dialog(self):
        # Varlık seçimi
        symbols = [asset["symbol"] for asset in self.portfolio.get("assets", [])]
        if not symbols:
            messagebox.showerror("Hata", "Çekilecek varlık bulunamadı!")
            return

        symbol = simpledialog.askstring("Para Çek", "Hangi kripto para birimini çekmek istiyorsunuz?",
                                       initialvalue=symbols[0])

        if not symbol:
            return

        # Varlık kontrolü
        asset_found = False
        asset_balance = 0

        for asset in self.portfolio.get("assets", []):
            if asset["symbol"] == symbol:
                asset_found = True
                asset_balance = asset["balance"]
                break

        if not asset_found:
            messagebox.showerror("Hata", f"{symbol} varlığınız bulunmamaktadır!")
            return

        # Miktar girişi
        amount_str = simpledialog.askstring("Para Çek", f"Ne kadar {symbol} çekmek istiyorsunuz? (Maks: {asset_balance})")

        if not amount_str:
            return

        try:
            amount = float(amount_str)
            if amount <= 0:
                messagebox.showerror("Hata", "Geçerli bir miktar giriniz!")
                return

            if amount > asset_balance:
                messagebox.showerror("Hata", f"Yetersiz bakiye! Maks: {asset_balance} {symbol}")
                return
        except ValueError:
            messagebox.showerror("Hata", "Geçerli bir miktar giriniz!")
            return

        # Varlık güncelle
        for asset in self.portfolio.get("assets", []):
            if asset["symbol"] == symbol:
                asset["balance"] -= amount
                break

        self.portfolio["last_updated"] = datetime.now().isoformat()
        self.save_portfolios()

        messagebox.showinfo("Başarılı", f"{amount} {symbol} çekildi!")
        self.show_dashboard()

    def show_mode_dialog(self):
        # Varlık seçimi
        symbols = [asset["symbol"] for asset in self.portfolio.get("assets", [])]
        if not symbols:
            messagebox.showerror("Hata", "Mod değiştirilecek varlık bulunamadı!")
            return

        symbol = simpledialog.askstring("Mod Değiştir", "Hangi kripto para biriminin modunu değiştirmek istiyorsunuz?",
                                       initialvalue=symbols[0])

        if not symbol:
            return

        # Varlık kontrolü
        asset_found = False
        current_mode = ""

        for asset in self.portfolio.get("assets", []):
            if asset["symbol"] == symbol:
                asset_found = True
                current_mode = asset["mode"]
                break

        if not asset_found:
            messagebox.showerror("Hata", f"{symbol} varlığınız bulunmamaktadır!")
            return

        # Yeni mod seçimi
        new_mode = "interest" if current_mode == "commission" else "commission"

        confirm = messagebox.askyesno("Mod Değiştir",
                                     f"{symbol} için modu {current_mode} -> {new_mode} olarak değiştirmek istiyor musunuz?")

        if not confirm:
            return

        # Varlık güncelle
        for asset in self.portfolio.get("assets", []):
            if asset["symbol"] == symbol:
                asset["mode"] = new_mode
                break

        self.portfolio["last_updated"] = datetime.now().isoformat()
        self.save_portfolios()

        messagebox.showinfo("Başarılı", f"{symbol} için mod {new_mode} olarak değiştirildi!")
        self.show_dashboard()

    def show_trade_dialog(self, symbol):
        messagebox.showinfo("Bilgi", f"{symbol} için işlem özelliği henüz eklenmedi.")

    def show_settings_dialog(self):
        """Ayarlar penceresini göster"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title("Ayarlar")
        settings_window.geometry("600x500")
        settings_window.configure(bg="#0B0E11")
        settings_window.resizable(False, False)
        settings_window.transient(self.root)
        settings_window.grab_set()

        # Ana frame
        main_frame = tk.Frame(settings_window, bg="#0B0E11", padx=20, pady=20)
        main_frame.pack(expand=True, fill="both")

        # Başlık
        title_label = tk.Label(main_frame, text="Uygulama Ayarları", font=("Arial", 16, "bold"),
                              fg="#F0B90B", bg="#0B0E11")
        title_label.pack(anchor="w", pady=(0, 20))

        # Ayarlar frame
        settings_frame = tk.Frame(main_frame, bg="#1E2329", padx=20, pady=20, bd=1, relief="solid")
        settings_frame.pack(fill="both", expand=True)

        # Komisyon oranı
        commission_frame = tk.Frame(settings_frame, bg="#1E2329", pady=10)
        commission_frame.pack(fill="x")

        tk.Label(commission_frame, text="Komisyon Oranı (%)", font=("Arial", 12),
                fg="#EAECEF", bg="#1E2329").pack(side="left")

        commission_entry = tk.Entry(commission_frame, font=("Arial", 12), bg="#0B0E11", fg="#EAECEF",
                                   insertbackground="#EAECEF", width=10)
        commission_entry.pack(side="right")
        commission_entry.insert(0, str(self.commission_rate * 100))

        # Otomatik yenileme
        auto_refresh_frame = tk.Frame(settings_frame, bg="#1E2329", pady=10)
        auto_refresh_frame.pack(fill="x")

        tk.Label(auto_refresh_frame, text="Otomatik Yenileme", font=("Arial", 12),
                fg="#EAECEF", bg="#1E2329").pack(side="left")

        auto_refresh_var = tk.BooleanVar(value=self.auto_refresh)
        auto_refresh_check = tk.Checkbutton(auto_refresh_frame, variable=auto_refresh_var,
                                          bg="#1E2329", activebackground="#1E2329",
                                          selectcolor="#0B0E11", highlightthickness=0)
        auto_refresh_check.pack(side="right")

        # Yenileme aralığı
        refresh_interval_frame = tk.Frame(settings_frame, bg="#1E2329", pady=10)
        refresh_interval_frame.pack(fill="x")

        tk.Label(refresh_interval_frame, text="Yenileme Aralığı (saniye)", font=("Arial", 12),
                fg="#EAECEF", bg="#1E2329").pack(side="left")

        refresh_interval_entry = tk.Entry(refresh_interval_frame, font=("Arial", 12), bg="#0B0E11",
                                        fg="#EAECEF", insertbackground="#EAECEF", width=10)
        refresh_interval_entry.pack(side="right")
        refresh_interval_entry.insert(0, str(self.auto_refresh_interval))

        # Faiz oranları başlığı
        tk.Label(settings_frame, text="Faiz Oranları (%)", font=("Arial", 14, "bold"),
                fg="#F0B90B", bg="#1E2329").pack(anchor="w", pady=(20, 10))

        # Faiz oranları frame
        interest_frame = tk.Frame(settings_frame, bg="#1E2329")
        interest_frame.pack(fill="both", expand=True)

        # Faiz oranları için scrollable frame
        canvas = tk.Canvas(interest_frame, bg="#1E2329", highlightthickness=0)
        scrollbar = ttk.Scrollbar(interest_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#1E2329")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(
                scrollregion=canvas.bbox("all")
            )
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Faiz oranları girişleri
        interest_entries = {}
        row = 0
        col = 0

        for symbol, rate in self.interest_rates.items():
            if symbol == "default":
                continue

            frame = tk.Frame(scrollable_frame, bg="#1E2329", padx=5, pady=5)
            frame.grid(row=row, column=col, sticky="ew")

            tk.Label(frame, text=f"{symbol}:", font=("Arial", 10),
                    fg="#EAECEF", bg="#1E2329", width=6).pack(side="left")

            entry = tk.Entry(frame, font=("Arial", 10), bg="#0B0E11", fg="#EAECEF",
                           insertbackground="#EAECEF", width=6)
            entry.pack(side="left")
            entry.insert(0, str(rate * 100))

            interest_entries[symbol] = entry

            col += 1
            if col > 2:
                col = 0
                row += 1

        # Varsayılan faiz oranı
        default_frame = tk.Frame(settings_frame, bg="#1E2329", pady=10)
        default_frame.pack(fill="x", pady=(10, 0))

        tk.Label(default_frame, text="Varsayılan Faiz Oranı (%)", font=("Arial", 12),
                fg="#EAECEF", bg="#1E2329").pack(side="left")

        default_interest_entry = tk.Entry(default_frame, font=("Arial", 12), bg="#0B0E11",
                                        fg="#EAECEF", insertbackground="#EAECEF", width=10)
        default_interest_entry.pack(side="right")
        default_interest_entry.insert(0, str(self.interest_rates.get("default", 0.05) * 100))

        # Butonlar
        button_frame = tk.Frame(main_frame, bg="#0B0E11", pady=20)
        button_frame.pack(fill="x")

        save_button = tk.Button(button_frame, text="Kaydet", font=("Arial", 12, "bold"),
                               bg="#F0B90B", fg="#0B0E11", activebackground="#F8D12F",
                               activeforeground="#0B0E11", padx=20, pady=5,
                               command=lambda: self.save_settings_dialog(
                                   settings_window,
                                   commission_entry.get(),
                                   auto_refresh_var.get(),
                                   refresh_interval_entry.get(),
                                   interest_entries,
                                   default_interest_entry.get()
                               ))
        save_button.pack(side="right", padx=5)

        cancel_button = tk.Button(button_frame, text="İptal", font=("Arial", 12),
                                 bg="#2B3139", fg="#EAECEF", activebackground="#3B4149",
                                 activeforeground="#EAECEF", padx=20, pady=5,
                                 command=settings_window.destroy)
        cancel_button.pack(side="right", padx=5)

    def save_settings_dialog(self, window, commission_str, auto_refresh, refresh_interval_str,
                           interest_entries, default_interest_str):
        """Ayarları kaydet"""
        try:
            # Komisyon oranı
            commission_rate = float(commission_str) / 100
            if commission_rate < 0 or commission_rate > 1:
                messagebox.showerror("Hata", "Komisyon oranı 0-100 arasında olmalıdır!")
                return

            # Yenileme aralığı
            refresh_interval = int(refresh_interval_str)
            if refresh_interval < 10:
                messagebox.showerror("Hata", "Yenileme aralığı en az 10 saniye olmalıdır!")
                return

            # Varsayılan faiz oranı
            default_interest = float(default_interest_str) / 100
            if default_interest < 0 or default_interest > 1:
                messagebox.showerror("Hata", "Faiz oranı 0-100 arasında olmalıdır!")
                return

            # Faiz oranları
            interest_rates = {"default": default_interest}
            for symbol, entry in interest_entries.items():
                try:
                    rate = float(entry.get()) / 100
                    if rate < 0 or rate > 1:
                        messagebox.showerror("Hata", f"{symbol} faiz oranı 0-100 arasında olmalıdır!")
                        return
                    interest_rates[symbol] = rate
                except ValueError:
                    messagebox.showerror("Hata", f"{symbol} faiz oranı geçerli bir sayı değil!")
                    return

            # Ayarları güncelle
            self.commission_rate = commission_rate
            self.auto_refresh = auto_refresh
            self.auto_refresh_interval = refresh_interval
            self.interest_rates = interest_rates

            # Ayarları kaydet
            self.save_settings()

            # Otomatik yenileme durumunu kontrol et
            if self.auto_refresh:
                self.start_auto_refresh()

            # Pencereyi kapat
            window.destroy()

            # Başarı mesajı
            messagebox.showinfo("Başarılı", "Ayarlar kaydedildi!")

            # Dashboard'u güncelle
            self.show_dashboard()
        except ValueError as e:
            messagebox.showerror("Hata", f"Geçersiz değer: {str(e)}")
        except Exception as e:
            if DEBUG:
                print(f"Ayarlar kaydedilirken hata: {e}")
            messagebox.showerror("Hata", f"Ayarlar kaydedilirken bir hata oluştu: {str(e)}")

    def refresh_data(self, show_message=True):
        """Verileri güncelle"""
        if self.is_refreshing:
            return

        self.is_refreshing = True

        try:
            # Fiyatları güncelle (demo için rastgele değişim)
            for symbol in self.crypto_prices:
                change = random.uniform(-0.02, 0.02)  # ±%2 değişim
                self.crypto_prices[symbol] *= (1 + change)

            # Faiz hesapla (günlük)
            for asset in self.portfolio.get("assets", []):
                if asset["mode"] == "interest":
                    symbol = asset["symbol"]
                    balance = asset["balance"]

                    # Günlük faiz oranı
                    daily_rate = self.interest_rates.get(symbol, self.interest_rates["default"]) / 365

                    # Faiz hesapla
                    interest = balance * daily_rate

                    # Faiz ekle
                    asset["interestBalance"] = asset.get("interestBalance", 0) + interest
                    self.portfolio["total_interest_earned"] = self.portfolio.get("total_interest_earned", 0) + interest

            self.portfolio["last_updated"] = datetime.now().isoformat()
            self.save_portfolios()

            # Dashboard'u güncelle
            self.show_dashboard()

            if show_message:
                messagebox.showinfo("Bilgi", "Veriler güncellendi!")
        except Exception as e:
            if DEBUG:
                print(f"Veri güncellenirken hata: {e}")
            if show_message:
                messagebox.showerror("Hata", f"Veriler güncellenirken bir hata oluştu: {e}")
        finally:
            self.is_refreshing = False

    def toggle_auto_refresh(self):
        """Otomatik yenilemeyi aç/kapat"""
        self.auto_refresh = not self.auto_refresh

        if self.auto_refresh:
            # Otomatik yenileme başlat
            self.start_auto_refresh()
            messagebox.showinfo("Bilgi", f"Otomatik yenileme açıldı. Her {self.auto_refresh_interval} saniyede bir güncellenecek.")
        else:
            messagebox.showinfo("Bilgi", "Otomatik yenileme kapatıldı.")

        # Ayarları kaydet
        self.save_settings()

    def start_auto_refresh(self):
        """Otomatik yenileme thread'ini başlat"""
        if self.refresh_thread and self.refresh_thread.is_alive():
            return

        self.refresh_thread = threading.Thread(target=self.auto_refresh_task)
        self.refresh_thread.daemon = True
        self.refresh_thread.start()

    def auto_refresh_task(self):
        """Otomatik yenileme görevi"""
        while self.auto_refresh:
            # Verileri güncelle (mesaj gösterme)
            self.refresh_data(show_message=False)

            # Bekle
            for _ in range(self.auto_refresh_interval):
                if not self.auto_refresh:
                    break
                time.sleep(1)

# Kullanıcı Yorumları - Uygulama başlangıcında gösterilecek
USER_TESTIMONIALS = [
    {
        "name": "Ahmet Y.",
        "comment": "CryptoBasriko ile yatırımlarımdan %5 ek kazanç sağlıyorum. Komisyonlar her zaman zamanında ödeniyor.",
        "membership": "8 aydır üye"
    },
    {
        "name": "Ayşe K.",
        "comment": "Diğer platformları denedim ama hiçbiri CryptoBasriko kadar güvenilir ve kullanıcı dostu değildi.",
        "membership": "1 yıldır üye"
    },
    {
        "name": "Mehmet S.",
        "comment": "Müşteri hizmetleri harika! Bir sorunum olduğunda anında yanıt alıyorum. Kesinlikle tavsiye ederim.",
        "membership": "6 aydır üye"
    },
    {
        "name": "Zeynep T.",
        "comment": "Bitcoin yatırımlarımdan %5 komisyon kazanmak harika bir fırsat. CryptoBasriko'yu herkese öneriyorum.",
        "membership": "3 aydır üye"
    },
    {
        "name": "Ali R.",
        "comment": "Platformun kullanımı çok kolay ve şeffaf. Kazançlarımı anında görebiliyorum.",
        "membership": "5 aydır üye"
    }
]

# Motivasyonel Mesajlar
MOTIVATIONAL_MESSAGES = [
    "Kripto dünyasında güvenilir yatırımın adresi CryptoBasriko - Paranız Güvende, Kazancınız Garantide!",
    "Yatırımlarınızdan anında %5 komisyon kazanın - CryptoBasriko farkıyla!",
    "10.000+ mutlu kullanıcı, 5 milyon dolar+ işlem hacmi - Güvenin adresi CryptoBasriko!",
    "7/24 müşteri desteği ve %100 şeffaf işlemler - CryptoBasriko ile kazanmaya başlayın!",
    "En yüksek komisyon oranı, en güvenilir platform - CryptoBasriko ile kazançlarınızı katlayın!"
]

def show_random_testimonial():
    """Rastgele bir kullanıcı yorumu göster"""
    testimonial = random.choice(USER_TESTIMONIALS)
    messagebox.showinfo(
        f"Kullanıcı Yorumu - {testimonial['name']}",
        f"\"{testimonial['comment']}\"\n\n{testimonial['name']} - {testimonial['membership']}"
    )

def show_welcome_message():
    """Hoş geldiniz mesajı göster"""
    message = random.choice(MOTIVATIONAL_MESSAGES)
    messagebox.showinfo(
        "CryptoBasriko'ya Hoş Geldiniz!",
        f"{message}\n\nYeni sürüm v{APP_VERSION} ile daha fazla kazanç fırsatı sizi bekliyor!"
    )

if __name__ == "__main__":
    root = tk.Tk()
    app = CryptoBasrikoApp(root)

    # Hoş geldiniz mesajını göster
    root.after(1000, show_welcome_message)

    # 30 saniye sonra rastgele bir kullanıcı yorumu göster
    root.after(30000, show_random_testimonial)

    root.mainloop()
