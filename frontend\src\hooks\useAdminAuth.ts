import { useState, useEffect, useCallback } from 'react';
import useAuth from './useAuth';
import axios from 'axios';
import { API_URL } from '../config';

/**
 * Custom hook for admin authentication
 * This hook provides a more robust way to handle admin authentication
 * It verifies admin status on mount and provides methods to check admin status
 */
const useAdminAuth = () => {
  const { user } = useAuth();
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null);
  const [isVerifying, setIsVerifying] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastVerified, setLastVerified] = useState<number>(0);

  // API base URL is imported from config at the top of the file

  /**
   * Verify admin status with the server
   * @param forceCheck Force a check with the server even if we have a cached result
   */
  const verifyAdminStatus = useCallback(async (forceCheck = false): Promise<boolean> => {
    try {
      // If no user is logged in, return false
      if (!user) {
        console.log('No user logged in, cannot be admin');
        setIsAdmin(false);
        return false;
      }

      // If user has isAdmin property already set to true and we're not forcing a check, use that
      if (user.isAdmin && !forceCheck) {
        console.log('User is already marked as admin in context');
        setIsAdmin(true);
        return true;
      }

      // Check if admin token exists in localStorage and we're not forcing a check
      const adminToken = localStorage.getItem('adminToken');
      const adminCookie = document.cookie.includes('adminToken=true');

      if (!forceCheck && (adminToken === 'true' || adminCookie)) {
        console.log('Admin token found in localStorage or cookies');
        setIsAdmin(true);
        return true;
      }

      // Otherwise, verify with the server
      try {
        console.log('Verifying admin status with server...');

        // Configure axios for this request
        axios.defaults.withCredentials = true;

        // Make the request with credentials
        const response = await axios.get(`${API_URL}/admin/check`, {
          withCredentials: true,
          headers: {
            'Cache-Control': 'no-cache',
            'X-Admin-Check': 'true' // Custom header for debugging
          }
        });

        console.log('Admin check response:', response.status, response.data);

        // If we get a successful response, the user is an admin
        if (response.status === 200) {
          console.log('Server confirmed user is admin');

          // Store admin flag in localStorage
          localStorage.setItem('adminToken', 'true');

          // Update user object in localStorage with admin status
          if (user && !user.isAdmin) {
            const updatedUser = { ...user, isAdmin: true };
            localStorage.setItem('user', JSON.stringify(updatedUser));
            console.log('Updated user object in localStorage with admin status');
          }

          setIsAdmin(true);
          setLastVerified(Date.now());
          return true;
        }

        console.log('Server did not confirm admin status');
        setIsAdmin(false);
        return false;
      } catch (error: any) {
        console.error("Admin check failed:", error.response?.status, error.response?.data);

        // If the error is 401 or 403, the user is definitely not an admin
        if (error.response?.status === 401 || error.response?.status === 403) {
          console.log('Server explicitly denied admin access');
          // Remove any admin tokens
          localStorage.removeItem('adminToken');
          setIsAdmin(false);
        }

        return false;
      }
    } catch (error) {
      console.error("Admin status check error:", error);
      setIsAdmin(false);
      return false;
    }
  }, [user, API_URL]);

  // Verify admin status on mount and when user changes
  useEffect(() => {
    const checkAdminStatus = async () => {
      try {
        setIsVerifying(true);
        setError(null);

        // Check if we need to verify with the server
        // If it's been less than 5 minutes since our last verification, use cached result
        const now = Date.now();
        const timeSinceLastVerified = now - lastVerified;
        const needsVerification = timeSinceLastVerified > 5 * 60 * 1000; // 5 minutes

        await verifyAdminStatus(needsVerification);
      } catch (err: any) {
        console.error('Error checking admin status:', err);
        setError(err.message || 'Failed to verify admin status');
        setIsAdmin(false);
      } finally {
        setIsVerifying(false);
      }
    };

    checkAdminStatus();
  }, [user, verifyAdminStatus, lastVerified]);

  return {
    isAdmin: isAdmin === null ? !!user?.isAdmin : isAdmin, // Fallback to user.isAdmin if not yet verified
    isVerifying,
    error,
    verifyAdminStatus
  };
};

export default useAdminAuth;
