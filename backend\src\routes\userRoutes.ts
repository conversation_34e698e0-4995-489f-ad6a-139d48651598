import express from 'express';
import {
  register,
  login,
  logout,
  getProfile,
  updateProfile,
  enableTwoFactor,
  verifyKyc,
  getReferrals
} from '../controllers/userController';
import { getWebSocketToken } from '../controllers/websocketController'; // Sử dụng cho Socket.IO
import {
  getReferralInfo,
  applyReferralCode
} from '../controllers/referralController';
import { protect } from '../middleware/authMiddleware';
import { cacheMiddleware, clearCache } from '../middleware/cacheMiddleware';
import { wrapController } from '../utils/routeWrapper';

const router = express.Router();

// Public routes
router.post('/signup', wrapController(register));
router.post('/register', wrapController(register)); // Keep for backward compatibility
router.post('/login', wrapController(login));
router.post('/logout', wrapController(logout));
router.get('/ws-token', protect, wrapController(getWebSocketToken));

// Protected routes
router.get('/profile', protect, cacheMiddleware({ keyPrefix: 'api:user:profile:' }), wrapController(getProfile));
router.put('/profile', protect, clearCache('user:profile:'), wrapController(updateProfile));
router.post('/enable-2fa', protect, clearCache('user:profile:'), wrapController(enableTwoFactor));
router.post('/verify-kyc', protect, clearCache('user:profile:'), wrapController(verifyKyc));

// Referral routes
router.get('/referrals', protect, cacheMiddleware({ keyPrefix: 'api:user:referrals:' }), wrapController(getReferrals));
router.get('/referral-info', protect, wrapController(getReferralInfo));
router.post('/apply-referral', protect, wrapController(applyReferralCode));

export default router;
