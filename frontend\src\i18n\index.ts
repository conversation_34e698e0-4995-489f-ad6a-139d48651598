import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';

// Import translation files - English, German, French only
import commonEN from '../locales/en/common.json';
import authEN from '../locales/en/auth.json';
import dashboardEN from '../locales/en/dashboard.json';
import adminEN from '../locales/en/admin.json';
import investmentEN from '../locales/en/investment.json';
import notificationsEN from '../locales/en/notifications.json';
import walletEN from '../locales/en/wallet.json';
import homeEN from '../locales/en/home.json';

import commonDE from '../locales/de/common.json';
import authDE from '../locales/de/auth.json';
import dashboardDE from '../locales/de/dashboard.json';
import adminDE from '../locales/de/admin.json';
import investmentDE from '../locales/de/investment.json';
import notificationsDE from '../locales/de/notifications.json';
import walletDE from '../locales/de/wallet.json';
import homeDE from '../locales/de/home.json';

import commonFR from '../locales/fr/common.json';
import authFR from '../locales/fr/auth.json';
import dashboardFR from '../locales/fr/dashboard.json';
import adminFR from '../locales/fr/admin.json';
import investmentFR from '../locales/fr/investment.json';
import notificationsFR from '../locales/fr/notifications.json';
import walletFR from '../locales/fr/wallet.json';
import homeFR from '../locales/fr/home.json';

// Define resources - English, German, French only
const resources = {
  en: {
    common: commonEN,
    auth: authEN,
    dashboard: dashboardEN,
    admin: adminEN,
    investment: investmentEN,
    notifications: notificationsEN,
    wallet: walletEN,
    home: homeEN,
  },
  de: {
    common: commonDE,
    auth: authDE,
    dashboard: dashboardDE,
    admin: adminDE,
    investment: investmentDE,
    notifications: notificationsDE,
    wallet: walletDE,
    home: homeDE,
  },
  fr: {
    common: commonFR,
    auth: authFR,
    dashboard: dashboardFR,
    admin: adminFR,
    investment: investmentFR,
    notifications: notificationsFR,
    wallet: walletFR,
    home: homeFR,
  },
};

// Language detection options
const detectionOptions = {
  // Order of language detection methods
  order: [
    'localStorage',
    'sessionStorage',
    'navigator',
    'htmlTag',
    'path',
    'subdomain',
  ],

  // Keys to look for in localStorage/sessionStorage
  lookupLocalStorage: 'cryptoyield_language',
  lookupSessionStorage: 'cryptoyield_language',

  // Cache user language
  caches: ['localStorage'],

  // Exclude certain paths from detection
  excludeCacheFor: ['cimode'],

  // Check all fallback languages
  checkWhitelist: true,
};

// Initialize i18next
i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    // Resources
    resources,

    // Default language
    fallbackLng: 'en',

    // Default namespace
    defaultNS: 'common',

    // Available languages - English, German, French only
    supportedLngs: ['en', 'de', 'fr'],

    // Language detection
    detection: detectionOptions,

    // Debug mode (disable in production)
    debug: process.env.NODE_ENV === 'development',

    // Interpolation options
    interpolation: {
      escapeValue: false, // React already escapes values
      formatSeparator: ',',
      format: (value, format, lng) => {
        if (format === 'uppercase') return value.toUpperCase();
        if (format === 'lowercase') return value.toLowerCase();
        if (format === 'currency') {
          return new Intl.NumberFormat(lng, {
            style: 'currency',
            currency: lng === 'tr' ? 'TRY' : 'USD',
          }).format(value);
        }
        if (format === 'number') {
          return new Intl.NumberFormat(lng).format(value);
        }
        if (format === 'date') {
          return new Intl.DateTimeFormat(lng).format(new Date(value));
        }
        if (format === 'datetime') {
          return new Intl.DateTimeFormat(lng, {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
          }).format(new Date(value));
        }
        return value;
      },
    },

    // React options
    react: {
      useSuspense: false,
      bindI18n: 'languageChanged',
      bindI18nStore: '',
      transEmptyNodeValue: '',
      transSupportBasicHtmlNodes: true,
      transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'em', 'span'],
    },

    // Backend options (for future HTTP loading)
    backend: {
      loadPath: '/locales/{{lng}}/{{ns}}.json',
      addPath: '/locales/add/{{lng}}/{{ns}}',
    },

    // Namespace separation
    ns: ['common', 'auth', 'dashboard', 'admin', 'investment', 'notifications', 'wallet', 'home'],

    // Key separator
    keySeparator: '.',

    // Namespace separator
    nsSeparator: ':',

    // Pluralization
    pluralSeparator: '_',

    // Context separator
    contextSeparator: '_',

    // Return objects
    returnObjects: false,

    // Return empty string for missing keys
    returnEmptyString: false,

    // Return null for missing keys
    returnNull: false,

    // Join arrays
    joinArrays: false,

    // Post processing
    postProcess: false,

    // Pre processing
    preload: ['tr', 'en'],

    // Clean code
    cleanCode: true,

    // Load all namespaces
    load: 'languageOnly',

    // Lowercase language codes
    lowerCaseLng: true,

    // Save missing translations
    saveMissing: process.env.NODE_ENV === 'development',

    // Missing key handler
    missingKeyHandler: (lng, ns, key, fallbackValue) => {
      if (process.env.NODE_ENV === 'development') {
        console.warn(`Missing translation key: ${ns}:${key} for language: ${lng}`);
      }
    },

    // Parse missing key handler
    parseMissingKeyHandler: (key) => {
      if (process.env.NODE_ENV === 'development') {
        console.warn(`Missing translation key: ${key}`);
      }
      return key;
    },
  });

// Export language utilities
export const changeLanguage = (lng: string) => {
  return i18n.changeLanguage(lng);
};

export const getCurrentLanguage = () => {
  return i18n.language;
};

export const getSupportedLanguages = () => {
  return i18n.options.supportedLngs?.filter(lng => lng !== 'cimode') || ['tr', 'en'];
};

export const isRTL = (lng?: string) => {
  const language = lng || i18n.language;
  return ['ar', 'he', 'fa', 'ur'].includes(language);
};

export const getLanguageDirection = (lng?: string) => {
  return isRTL(lng) ? 'rtl' : 'ltr';
};

export const formatCurrency = (amount: number, lng?: string) => {
  const language = lng || i18n.language;
  return new Intl.NumberFormat(language, {
    style: 'currency',
    currency: language === 'tr' ? 'TRY' : 'USD',
  }).format(amount);
};

export const formatNumber = (number: number, lng?: string) => {
  const language = lng || i18n.language;
  return new Intl.NumberFormat(language).format(number);
};

export const formatDate = (date: Date | string, lng?: string, options?: Intl.DateTimeFormatOptions) => {
  const language = lng || i18n.language;
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  };
  return new Intl.DateTimeFormat(language, { ...defaultOptions, ...options }).format(new Date(date));
};

export const formatDateTime = (date: Date | string, lng?: string) => {
  const language = lng || i18n.language;
  return new Intl.DateTimeFormat(language, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(date));
};

export default i18n;
