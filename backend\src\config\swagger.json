{"openapi": "3.0.0", "info": {"title": "Shipping Finance API", "description": "Shipping Finance platformu için resmi API dokümantasyonu", "version": "1.0.0", "contact": {"name": "API Destek", "email": "<EMAIL>", "url": "https://developers.shippingFinance.com"}}, "servers": [{"url": "https://api.shippingFinance.com/v1", "description": "Production API"}, {"url": "https://staging-api.shippingFinance.com/v1", "description": "Staging API"}, {"url": "http://localhost:3000/api/v1", "description": "Local Development"}], "tags": [{"name": "<PERSON><PERSON>", "description": "Kimlik doğrulama işlemleri"}, {"name": "User", "description": "Kullanıcı profil işlemleri"}, {"name": "Investment", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> işlemleri"}, {"name": "Transaction", "description": "İşlem geçmişi ve para transferleri"}, {"name": "Referral", "description": "Referans sistemi <PERSON>i"}, {"name": "Analytics", "description": "Analitik ve raporlama işlemleri"}, {"name": "Corporate", "description": "Kurumsal müşteri işlemleri"}, {"name": "Webhook", "description": "Webhook entegrasyonları"}], "paths": {"/auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Yeni kullanıcı kaydı", "description": "Yeni bir kullanıcı hesabı oluşturur", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}}}, "responses": {"201": {"description": "Kullanıcı başarıyla oluşturuldu", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResponse"}}}}, "400": {"description": "Geçersiz istek", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Kullanıcı girişi", "description": "Kullanıcı kimlik bilgileriyle giriş yapar", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "Başarılı giriş", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthResponse"}}}}, "401": {"description": "Kimlik doğrulama başarısız", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}, "apiKey": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "X-API-KEY"}}, "schemas": {"RegisterRequest": {"type": "object", "required": ["email", "password", "firstName", "lastName"], "properties": {"email": {"type": "string", "format": "email", "description": "Kullanıcı e-posta adresi"}, "password": {"type": "string", "format": "password", "description": "Kullanıcı şifresi"}, "firstName": {"type": "string", "description": "Kullanıcının adı"}, "lastName": {"type": "string", "description": "Kullanıcının soyadı"}, "referralCode": {"type": "string", "description": "Referans kodu (opsiyonel)"}}}, "LoginRequest": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string", "format": "email", "description": "Kullanıcı e-posta adresi"}, "password": {"type": "string", "format": "password", "description": "Kullanıcı şifresi"}, "twoFactorCode": {"type": "string", "description": "İki faktörlü kimlik doğrulama kodu (eğer etkinse)"}}}, "AuthResponse": {"type": "object", "properties": {"token": {"type": "string", "description": "JWT kimlik doğrulama token'ı"}, "user": {"$ref": "#/components/schemas/User"}}}, "User": {"type": "object", "properties": {"id": {"type": "string", "description": "Kullanıcı ID"}, "email": {"type": "string", "format": "email", "description": "Kullanıcı e-posta adresi"}, "firstName": {"type": "string", "description": "Kullanıcının adı"}, "lastName": {"type": "string", "description": "Kullanıcının soyadı"}, "twoFactorEnabled": {"type": "boolean", "description": "İki faktörlü kimlik doğrulama etkin mi?"}, "kycVerified": {"type": "boolean", "description": "KYC doğrulaması tamamlandı mı?"}, "referralCode": {"type": "string", "description": "Kullanıcının referans kodu"}, "createdAt": {"type": "string", "format": "date-time", "description": "<PERSON><PERSON><PERSON> ta<PERSON>hi"}}}, "Error": {"type": "object", "properties": {"code": {"type": "string", "description": "<PERSON>a kodu"}, "message": {"type": "string", "description": "<PERSON>a mesajı"}}}}}}