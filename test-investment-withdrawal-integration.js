#!/usr/bin/env node

/**
 * Test script to verify investment earnings are properly integrated with withdrawal system
 * This script tests the critical API endpoints that the withdrawal interface depends on
 */

const axios = require('axios');

// Configuration
const BASE_URL = 'https://api.shpnfinance.com/api';
const TEST_USER_TOKEN = process.env.TEST_USER_TOKEN; // You'll need to provide this

// Test configuration
const config = {
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${TEST_USER_TOKEN}`,
    'Cookie': process.env.TEST_USER_COOKIE // If using cookie auth
  },
  withCredentials: true
};

async function testInvestmentWithdrawalIntegration() {
  console.log('🔍 Testing Investment-Withdrawal System Integration...\n');

  try {
    // Test 1: Check if investment package routes are accessible
    console.log('1. Testing investment package balances endpoint...');
    try {
      const response = await axios.get(`${BASE_URL}/investment-packages/balances`, config);
      console.log('✅ Investment balances endpoint working');
      console.log('📊 Available balances:', response.data.data?.length || 0, 'currencies');
      
      if (response.data.data && response.data.data.length > 0) {
        response.data.data.forEach(balance => {
          console.log(`   - ${balance.currency}: ${balance.availableForWithdrawal} available for withdrawal`);
        });
      }
    } catch (error) {
      console.log('❌ Investment balances endpoint failed:', error.response?.status, error.response?.data?.message);
    }

    console.log('\n2. Testing specific currency balance endpoint...');
    try {
      const response = await axios.get(`${BASE_URL}/investment-packages/balances/USDT`, config);
      console.log('✅ USDT balance endpoint working');
      console.log('📊 USDT balance:', response.data.data);
    } catch (error) {
      console.log('❌ USDT balance endpoint failed:', error.response?.status, error.response?.data?.message);
    }

    console.log('\n3. Testing withdrawal eligibility endpoint...');
    try {
      const response = await axios.post(`${BASE_URL}/investment-packages/withdrawal-eligibility`, {
        currency: 'USDT',
        amount: 50
      }, config);
      console.log('✅ Withdrawal eligibility endpoint working');
      console.log('📊 Eligibility check:', response.data.data);
    } catch (error) {
      console.log('❌ Withdrawal eligibility endpoint failed:', error.response?.status, error.response?.data?.message);
    }

    console.log('\n4. Testing investment packages endpoint...');
    try {
      const response = await axios.get(`${BASE_URL}/investment-packages/packages`, config);
      console.log('✅ Investment packages endpoint working');
      console.log('📊 Active packages:', response.data.data?.packages?.length || 0);
    } catch (error) {
      console.log('❌ Investment packages endpoint failed:', error.response?.status, error.response?.data?.message);
    }

    console.log('\n5. Testing comprehensive investment data endpoint...');
    try {
      const response = await axios.get(`${BASE_URL}/investment-packages/comprehensive`, config);
      console.log('✅ Comprehensive data endpoint working');
      console.log('📊 Comprehensive data structure:', {
        packages: response.data.data?.packages?.length || 0,
        distributions: response.data.data?.distributions?.length || 0,
        balances: response.data.data?.balances?.length || 0
      });
    } catch (error) {
      console.log('❌ Comprehensive data endpoint failed:', error.response?.status, error.response?.data?.message);
    }

    console.log('\n6. Testing wallet balance endpoint (for comparison)...');
    try {
      const response = await axios.get(`${BASE_URL}/wallets/balance`, config);
      console.log('✅ Wallet balance endpoint working');
      console.log('📊 Wallet assets:', response.data.data?.assets?.length || 0);
    } catch (error) {
      console.log('❌ Wallet balance endpoint failed:', error.response?.status, error.response?.data?.message);
    }

    console.log('\n🎯 Integration Test Summary:');
    console.log('=====================================');
    console.log('The withdrawal interface should now be able to:');
    console.log('✓ Fetch investment earnings from /investment-packages/balances');
    console.log('✓ Check withdrawal eligibility from /investment-packages/withdrawal-eligibility');
    console.log('✓ Display available earnings in the withdrawal modal');
    console.log('✓ Validate withdrawal amounts against investment earnings');
    console.log('\n📝 Next Steps:');
    console.log('1. Login to the application');
    console.log('2. Navigate to Profile page');
    console.log('3. Open withdrawal modal');
    console.log('4. Verify that investment earnings are displayed');
    console.log('5. Test withdrawal functionality with investment earnings');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
if (require.main === module) {
  testInvestmentWithdrawalIntegration();
}

module.exports = { testInvestmentWithdrawalIntegration };
