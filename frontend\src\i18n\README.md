# Internationalization (i18n) System

Bu proje, React i18next kullanarak kapsamlı bir çok dilli destek sistemi içerir.

## 🌍 Desteklenen Diller

- **Türkçe (tr)** - Ana dil
- **İngilizce (en)** - İkincil dil

## 📁 Dosya Yapı<PERSON>ı

```
src/
├── i18n/
│   ├── index.ts              # Ana i18n konfigürasyonu
│   └── README.md             # Bu dosya
├── locales/
│   ├── tr/                   # Türkçe çeviriler
│   │   ├── common.json       # Genel çeviriler
│   │   ├── auth.json         # Kimlik doğrulama
│   │   ├── dashboard.json    # Dashboard
│   │   ├── admin.json        # Admin paneli
│   │   ├── investment.json   # Yatırım sistemi
│   │   ├── notifications.json # Bildirimler
│   │   └── wallet.json       # Cüzdan
│   └── en/                   # İngilizce çeviriler
│       ├── common.json
│       ├── auth.json
│       ├── dashboard.json
│       ├── admin.json
│       ├── investment.json
│       ├── notifications.json
│       └── wallet.json
├── hooks/
│   └── useI18n.ts           # i18n hook'ları
├── components/
│   ├── LanguageSwitcher/    # Dil değiştirici
│   ├── I18nProvider.tsx     # i18n provider
│   └── I18nTestComponent.tsx # Test bileşeni
├── context/
│   └── I18nContext.tsx      # i18n context
└── utils/
    └── i18nUtils.ts         # Yardımcı fonksiyonlar
```

## 🚀 Kullanım

### Temel Kullanım

```tsx
import { useTranslation } from 'react-i18next';

const MyComponent = () => {
  const { t } = useTranslation('common');
  
  return (
    <div>
      <h1>{t('welcome')}</h1>
      <p>{t('description')}</p>
    </div>
  );
};
```

### Namespace ile Kullanım

```tsx
import { useTranslation } from 'react-i18next';

const AuthComponent = () => {
  const { t } = useTranslation('auth');
  
  return (
    <form>
      <label>{t('login.email')}</label>
      <label>{t('login.password')}</label>
      <button>{t('login.submit')}</button>
    </form>
  );
};
```

### Özel Hook'lar

```tsx
import { useI18n, useCommonTranslation, useAuthTranslation } from '../hooks/useI18n';

const MyComponent = () => {
  const { currentLanguage, setLanguage, formatCurrency } = useI18n();
  const { t: tCommon } = useCommonTranslation();
  const { t: tAuth } = useAuthTranslation();
  
  return (
    <div>
      <p>Current: {currentLanguage}</p>
      <p>{tCommon('welcome')}</p>
      <p>{tAuth('login.title')}</p>
      <p>{formatCurrency(1234.56, 'USD')}</p>
    </div>
  );
};
```

### Dil Değiştirici

```tsx
import LanguageSwitcher from '../components/LanguageSwitcher';

const Header = () => {
  return (
    <header>
      <LanguageSwitcher variant="menu" size="md" />
    </header>
  );
};
```

## 🔧 Özellikler

### 1. Namespace Desteği
- `common` - Genel çeviriler
- `auth` - Kimlik doğrulama
- `dashboard` - Dashboard
- `admin` - Admin paneli
- `investment` - Yatırım sistemi
- `notifications` - Bildirimler
- `wallet` - Cüzdan

### 2. Formatlama Fonksiyonları
- Para birimi formatlama
- Sayı formatlama
- Tarih formatlama
- Göreceli zaman formatlama

### 3. Dil Değiştirici Varyantları
- `menu` - Dropdown menü
- `button` - Buton grubu
- `compact` - Kompakt buton

### 4. Otomatik Dil Algılama
- Tarayıcı dili
- localStorage tercihi
- URL parametresi
- Varsayılan dil

### 5. RTL Desteği
- Arapça, İbranice vb. diller için hazır
- Otomatik yön değişimi

## 📝 Çeviri Dosyası Yapısı

### common.json
```json
{
  "welcome": "Hoş geldiniz",
  "buttons": {
    "save": "Kaydet",
    "cancel": "İptal",
    "submit": "Gönder"
  },
  "navigation": {
    "home": "Ana Sayfa",
    "dashboard": "Dashboard",
    "profile": "Profil"
  }
}
```

### Interpolasyon
```json
{
  "welcome": "Hoş geldiniz, {{name}}!",
  "itemCount": "{{count}} öğe bulundu",
  "validation": {
    "minLength": "En az {{min}} karakter olmalı"
  }
}
```

### Çoğul Formlar
```json
{
  "item_one": "{{count}} öğe",
  "item_other": "{{count}} öğe"
}
```

## 🎯 En İyi Uygulamalar

### 1. Anahtar Adlandırma
- Nokta notasyonu kullanın: `auth.login.title`
- Anlamlı isimler verin
- Tutarlı yapı kullanın

### 2. Namespace Organizasyonu
- İlgili çevirileri gruplandırın
- Çok büyük dosyalardan kaçının
- Mantıklı bölümler oluşturun

### 3. Fallback Değerler
```tsx
const { t } = useTranslation();
const text = t('key', 'Fallback text');
```

### 4. Tip Güvenliği
```tsx
// TypeScript için tip tanımları
interface TranslationKeys {
  'common.welcome': string;
  'auth.login.title': string;
}
```

## 🧪 Test

Test sayfasına erişim: `/i18n-test`

Bu sayfa tüm i18n özelliklerini test etmenizi sağlar:
- Çeviri kontrolü
- Formatlama testleri
- Dil değiştirme
- Namespace testleri

## 🔄 Yeni Dil Ekleme

1. `src/locales/` altında yeni dil klasörü oluşturun
2. Tüm JSON dosyalarını kopyalayın ve çevirin
3. `src/i18n/index.ts` dosyasında dili ekleyin
4. `LanguageSwitcher` bileşeninde konfigürasyonu güncelleyin

## 🐛 Sorun Giderme

### Çeviri Görünmüyor
- Anahtar doğru mu?
- Namespace doğru mu?
- JSON dosyası geçerli mi?

### Dil Değişmiyor
- localStorage temizleyin
- Tarayıcı önbelleğini temizleyin
- Konsol hatalarını kontrol edin

### Formatlama Çalışmıyor
- Locale desteği var mı?
- Intl API destekleniyor mu?
- Fallback değerler tanımlı mı?

## 📚 Kaynaklar

- [React i18next Dokümantasyonu](https://react.i18next.com/)
- [i18next Dokümantasyonu](https://www.i18next.com/)
- [ICU Message Format](https://unicode-org.github.io/icu/userguide/format_parse/messages/)
