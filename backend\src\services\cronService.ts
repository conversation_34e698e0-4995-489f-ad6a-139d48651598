import * as cron from 'node-cron';
import InvestmentPackage from '../models/investmentPackageModel';
import AuditTrail from '../models/auditTrailModel';
import autoInvestmentService from './autoInvestmentService';
import depositMonitorService from './depositMonitorService';
import timeService from './timeService';
// import { logger } from '../utils/logger';

class CronService {
  private jobs: Map<string, cron.ScheduledTask> = new Map();

  /**
   * Initialize all cron jobs
   */
  initialize(): void {
    this.setupDailyInterestCalculation();
    this.setupPackageActivation();
    this.setupSystemMaintenance();
    this.setupAutoInvestmentProcessing();
    this.setupDepositMonitoring();

    console.log('🕐 Cron service initialized with all jobs');
  }

  /**
   * Daily interest calculation at 03:00 Turkey time
   * Cron: "0 3 * * *" (every day at 03:00)
   */
  private setupDailyInterestCalculation(): void {
    const job = cron.schedule('0 3 * * *', async () => {
      try {
        console.info('🔄 Starting daily interest calculation...');

        const startTime = Date.now();
        const turkeyTime = timeService.getTurkeyTime();

        // Get all active packages
        const activePackages = await InvestmentPackage.getActivePackages();

        if (activePackages.length === 0) {
          console.info('📊 No active packages found for interest calculation');
          return;
        }

        let totalCalculated = 0;
        let totalInterest = 0;
        const errors: any[] = [];

        // Process each package
        for (const pkg of activePackages) {
          try {
            // Calculate daily interest
            const dailyInterest = pkg.calculateDailyInterest();

            if (dailyInterest > 0) {
              // Update package
              pkg.totalEarned += dailyInterest;
              pkg.dailyInterest = dailyInterest;
              pkg.activeDays += 1;
              pkg.lastCalculatedAt = turkeyTime;

              await pkg.save();

              // Create audit trail
              await AuditTrail.createAuditLog({
                userId: pkg.userId,
                action: 'INTEREST_CALCULATED',
                packageId: pkg._id as any,
                amount: dailyInterest,
                currency: pkg.currency,
                details: {
                  activeDays: pkg.activeDays,
                  totalEarned: pkg.totalEarned,
                  interestRate: pkg.interestRate,
                  compoundEnabled: pkg.compoundEnabled,
                  calculationTime: turkeyTime
                }
              });

              totalCalculated++;
              totalInterest += dailyInterest;

              console.debug(`💰 Interest calculated for package ${pkg._id}: ${dailyInterest} ${pkg.currency}`);
            }
          } catch (error: any) {
            console.error(`❌ Error calculating interest for package ${pkg._id}:`, error);
            errors.push({
              packageId: pkg._id,
              error: error.message
            });
          }
        }

        const duration = Date.now() - startTime;

        console.info(`✅ Daily interest calculation completed:`, {
          totalPackages: activePackages.length,
          calculatedPackages: totalCalculated,
          totalInterest: totalInterest.toFixed(6),
          errors: errors.length,
          duration: `${duration}ms`,
          turkeyTime: timeService.formatTurkeyTime(turkeyTime)
        });

        // Log errors if any
        if (errors.length > 0) {
          console.error('❌ Interest calculation errors:', errors);
        }

      } catch (error: any) {
        console.error('❌ Daily interest calculation failed:', error);
      }
    }, {
      timezone: 'Europe/Istanbul'
    });

    this.jobs.set('dailyInterestCalculation', job);
    console.info('📅 Daily interest calculation job scheduled for 03:00 Turkey time');
  }

  /**
   * Package activation check (every 5 minutes)
   * Activates packages that should be activated
   */
  private setupPackageActivation(): void {
    const job = cron.schedule('*/5 * * * *', async () => {
      try {
        const turkeyTime = timeService.getTurkeyTime();

        // Find packages that should be activated
        const pendingPackages = await InvestmentPackage.find({
          status: 'pending',
          activatedAt: { $lte: turkeyTime }
        });

        if (pendingPackages.length === 0) {
          return; // No packages to activate
        }

        console.info(`🔄 Activating ${pendingPackages.length} pending packages...`);

        let activated = 0;
        for (const pkg of pendingPackages) {
          try {
            pkg.status = 'active';
            pkg.lastCalculatedAt = turkeyTime;
            await pkg.save();

            // Create audit trail
            await AuditTrail.createAuditLog({
              userId: pkg.userId,
              action: 'INVESTMENT_ACTIVATED',
              packageId: pkg._id as any,
              amount: pkg.amount,
              currency: pkg.currency,
              details: {
                activatedAt: pkg.activatedAt,
                interestRate: pkg.interestRate,
                compoundEnabled: pkg.compoundEnabled
              }
            });

            activated++;
            console.debug(`✅ Package ${pkg._id} activated`);
          } catch (error: any) {
            console.error(`❌ Error activating package ${pkg._id}:`, error);
          }
        }

        if (activated > 0) {
          console.info(`✅ Successfully activated ${activated} packages`);
        }

      } catch (error: any) {
        console.error('❌ Package activation check failed:', error);
      }
    }, {
      timezone: 'Europe/Istanbul'
    });

    this.jobs.set('packageActivation', job);
    console.info('📅 Package activation job scheduled (every 5 minutes)');
  }

  /**
   * System maintenance tasks (daily at 02:00)
   */
  private setupSystemMaintenance(): void {
    const job = cron.schedule('0 2 * * *', async () => {
      try {
        console.info('🔧 Starting system maintenance tasks...');

        const startTime = Date.now();

        // Clean old audit trails (keep last 90 days)
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - 90);

        const deletedAudits = await AuditTrail.deleteMany({
          timestamp: { $lt: cutoffDate }
        });

        // Update package statistics
        const packageStats = await InvestmentPackage.aggregate([
          {
            $group: {
              _id: '$status',
              count: { $sum: 1 },
              totalAmount: { $sum: '$amount' },
              totalEarned: { $sum: '$totalEarned' }
            }
          }
        ]);

        // Clear crypto API cache
        const cryptoApiService = require('./cryptoApiService').default;
        cryptoApiService.clearCache();

        const duration = Date.now() - startTime;

        console.info(`✅ System maintenance completed:`, {
          deletedAuditTrails: deletedAudits.deletedCount,
          packageStats,
          duration: `${duration}ms`
        });

      } catch (error: any) {
        console.error('❌ System maintenance failed:', error);
      }
    }, {
      timezone: 'Europe/Istanbul'
    });

    this.jobs.set('systemMaintenance', job);
    console.info('📅 System maintenance job scheduled for 02:00 Turkey time');
  }

  /**
   * Auto-investment processing every 5 minutes
   * Cron: every 5 minutes
   */
  private setupAutoInvestmentProcessing(): void {
    const job = cron.schedule('*/5 * * * *', async () => {
      try {
        console.info('🤖 Starting auto-investment processing...');
        const startTime = Date.now();

        await autoInvestmentService.processConfirmedDeposits();

        const duration = Date.now() - startTime;
        console.info(`✅ Auto-investment processing completed in ${duration}ms`);

      } catch (error: any) {
        console.error('❌ Auto-investment processing failed:', error);
      }
    }, {
      timezone: 'Europe/Istanbul'
    });

    this.jobs.set('autoInvestmentProcessing', job);
    console.info('📅 Auto-investment processing job scheduled for every 5 minutes');
  }

  /**
   * Deposit monitoring startup
   * Starts monitoring when cron service initializes
   */
  private setupDepositMonitoring(): void {
    // Start deposit monitoring immediately
    setTimeout(async () => {
      try {
        console.info('🔍 Starting deposit monitoring service...');
        await depositMonitorService.startMonitoring();
        console.info('✅ Deposit monitoring service started');
      } catch (error: any) {
        console.error('❌ Failed to start deposit monitoring:', error);
      }
    }, 5000); // Wait 5 seconds after startup

    console.info('📅 Deposit monitoring startup scheduled');
  }

  /**
   * Manual interest calculation (for testing/admin use)
   */
  async calculateInterestManually(packageId?: string): Promise<{
    success: boolean;
    processed: number;
    totalInterest: number;
    errors: any[];
  }> {
    try {
      console.info('🔄 Manual interest calculation started...');

      const query = packageId ? { _id: packageId, status: 'active' } : { status: 'active' };
      const packages = await InvestmentPackage.find(query);

      let processed = 0;
      let totalInterest = 0;
      const errors: any[] = [];

      for (const pkg of packages) {
        try {
          const dailyInterest = pkg.calculateDailyInterest();

          if (dailyInterest > 0) {
            pkg.totalEarned += dailyInterest;
            pkg.dailyInterest = dailyInterest;
            pkg.activeDays += 1;
            pkg.lastCalculatedAt = timeService.getTurkeyTime();

            await pkg.save();

            await AuditTrail.createAuditLog({
              userId: pkg.userId,
              action: 'INTEREST_CALCULATED',
              packageId: pkg._id as any,
              amount: dailyInterest,
              currency: pkg.currency,
              details: {
                manual: true,
                activeDays: pkg.activeDays,
                totalEarned: pkg.totalEarned
              }
            });

            processed++;
            totalInterest += dailyInterest;
          }
        } catch (error: any) {
          errors.push({
            packageId: pkg._id,
            error: error.message
          });
        }
      }

      console.info(`✅ Manual interest calculation completed: ${processed} packages processed`);

      return {
        success: true,
        processed,
        totalInterest,
        errors
      };

    } catch (error: any) {
      console.error('❌ Manual interest calculation failed:', error);
      return {
        success: false,
        processed: 0,
        totalInterest: 0,
        errors: [{ error: error.message }]
      };
    }
  }

  /**
   * Get job status
   */
  getJobStatus(): { [jobName: string]: { running: boolean; nextRun?: Date } } {
    const status: { [jobName: string]: { running: boolean; nextRun?: Date } } = {};

    this.jobs.forEach((job, name) => {
      status[name] = {
        running: (job as any).running || false,
        nextRun: (job as any).nextDate ? (job as any).nextDate().toDate() : undefined
      };
    });

    return status;
  }

  /**
   * Stop all cron jobs
   */
  stopAll(): void {
    this.jobs.forEach((job, name) => {
      job.stop();
      console.info(`🛑 Stopped cron job: ${name}`);
    });

    this.jobs.clear();
    console.info('🛑 All cron jobs stopped');
  }

  /**
   * Start all cron jobs
   */
  startAll(): void {
    this.jobs.forEach((job, name) => {
      job.start();
      console.info(`▶️ Started cron job: ${name}`);
    });

    console.info('▶️ All cron jobs started');
  }
}

export default new CronService();
