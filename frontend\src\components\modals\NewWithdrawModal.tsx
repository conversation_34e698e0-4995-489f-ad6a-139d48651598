import React, { useState } from 'react';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  HStack,
  Text,
  Box,
  Flex,
  useToast,
  Icon,
  Divider,
  Progress,
  Stepper,
  Step,
  StepIndicator,
  StepStatus,
  StepIcon,
  StepNumber,
  StepTitle,
  StepDescription,
  StepSeparator,
  useSteps
} from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { FaInfoCircle, FaExclamationTriangle, FaWallet, FaShieldAlt } from 'react-icons/fa';
import useAuth from '../../hooks/useAuth';

interface NewWithdrawModalProps {
  isOpen: boolean;
  onClose: () => void;
  availableBalance?: number;
  commissionBalance?: number;
  interestBalance?: number;
  depositDate?: string;
}

const steps = [
  { title: 'Amount', description: 'Withdrawal amount' },
  { title: 'Address', description: 'Wallet address' },
  { title: 'Confirm', description: 'Transaction confirmation' },
]

const NewWithdrawModal: React.FC<NewWithdrawModalProps> = ({
  isOpen,
  onClose,
  availableBalance = 0,
  commissionBalance = 0,
  interestBalance = 0,
  depositDate = '2023-05-01'
}) => {
  const { t } = useTranslation();
  const toast = useToast();
  const { user } = useAuth();

  // Stepper state
  const { activeStep, setActiveStep } = useSteps({
    index: 0,
    count: steps.length,
  });

  // State variables
  const [selectedCrypto, setSelectedCrypto] = useState('BTC');
  const [amount, setAmount] = useState('');
  const [walletAddress, setWalletAddress] = useState('');
  const [memo, setMemo] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // Constants
  const withdrawalFee = selectedCrypto === 'BTC' ? 0.0005 :
                        selectedCrypto === 'ETH' ? 0.01 :
                        selectedCrypto === 'USDT' ? 2.00 :
                        selectedCrypto === 'BNB' ? 0.05 : 0.5;
  const minWithdrawal = selectedCrypto === 'BTC' ? 0.001 :
                        selectedCrypto === 'ETH' ? 0.05 :
                        selectedCrypto === 'USDT' ? 50.00 :
                        selectedCrypto === 'BNB' ? 0.1 : 10;
  const processingTime = '1-24 hours';

  // Handle form submission
  const handleSubmit = () => {
    // Validate form
    if (!amount || parseFloat(amount) <= 0) {
      toast({
        title: t('withdrawModal.invalidAmount', 'Invalid amount'),
        description: t('withdrawModal.enterValidAmount', 'Please enter a valid amount.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    if (parseFloat(amount) > availableBalance) {
      toast({
        title: t('withdrawModal.insufficientBalance', 'Insufficient balance'),
        description: t('withdrawModal.insufficientBalanceDesc', 'The amount you want to withdraw exceeds your balance.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    if (parseFloat(amount) < minWithdrawal) {
      toast({
        title: t('withdrawModal.belowMinimum', 'Below minimum withdrawal amount'),
        description: `Minimum withdrawal amount: ${minWithdrawal.toFixed(selectedCrypto === 'BTC' ? 8 : selectedCrypto === 'USDT' ? 2 : 4)} ${selectedCrypto}`,
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    if (!walletAddress) {
      toast({
        title: t('withdrawModal.addressRequired', 'Wallet address required'),
        description: t('withdrawModal.enterAddress', 'Please enter a valid wallet address.'),
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    // Start submission process
    setIsSubmitting(true);

    // Simulate progress
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      setUploadProgress(progress);
      if (progress >= 100) {
        clearInterval(interval);

        // Simulate API call delay
        setTimeout(() => {
          setIsSubmitting(false);
          setUploadProgress(0);

          // Create a new transaction record
          const newTransaction = {
            id: `WDR-${Date.now()}`,
            type: 'withdrawal',
            amount: parseFloat(amount),
            currency: selectedCrypto,
            date: new Date().toISOString(),
            status: 'pending',
            txHash: `0x${Math.random().toString(16).substring(2, 34)}`,
            walletAddress: walletAddress,
            memo: memo || undefined
          };

          // Save to localStorage
          try {
            const existingTransactions = localStorage.getItem('transactions');
            let transactions = existingTransactions ? JSON.parse(existingTransactions) : [];
            transactions.push(newTransaction);
            localStorage.setItem('transactions', JSON.stringify(transactions));

            // Dispatch custom event to notify other components
            const event = new CustomEvent('transactionUpdated', { detail: newTransaction });
            window.dispatchEvent(event);
            console.log('Transaction updated event dispatched:', newTransaction);

            // Also trigger a localStorage event for components listening to storage changes
            localStorage.setItem('lastTransactionUpdate', new Date().toISOString());
          } catch (error) {
            console.error('Error saving transaction:', error);
          }

          // Show success message
          toast({
            title: t('withdrawModal.successTitle', 'Transaction Successful'),
            description: t('withdrawModal.successDescription', 'Your withdrawal request has been received. Your transaction will be processed as soon as possible.'),
            status: 'success',
            duration: 5000,
            isClosable: true,
          });

          // Reset form and close modal
          setAmount('');
          setWalletAddress('');
          setMemo('');
          setActiveStep(0);
          onClose();
        }, 500);
      }
    }, 200);
  };

  // Next step in the wizard
  const nextStep = () => {
    if (activeStep === 0) {
      // Validate first step
      if (!amount || parseFloat(amount) <= 0) {
        toast({
          title: t('withdrawModal.invalidAmount', 'Invalid Amount'),
          description: t('withdrawModal.enterValidAmount', 'Please enter a valid amount.'),
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      if (parseFloat(amount) > availableBalance) {
        toast({
          title: t('withdrawModal.insufficientBalance', 'Insufficient Balance'),
          description: t('withdrawModal.insufficientBalanceDesc', 'The amount you want to withdraw exceeds your balance.'),
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      if (parseFloat(amount) < minWithdrawal) {
        toast({
          title: t('withdrawModal.belowMinimum', 'Below Minimum Withdrawal Amount'),
          description: `Minimum withdrawal amount: ${minWithdrawal.toFixed(selectedCrypto === 'BTC' ? 8 : selectedCrypto === 'USDT' ? 2 : 4)} ${selectedCrypto}`,
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }
    } else if (activeStep === 1) {
      // Validate second step
      if (!walletAddress) {
        toast({
          title: t('withdrawModal.addressRequired', 'Wallet Address Required'),
          description: t('withdrawModal.enterAddress', 'Please enter a valid wallet address.'),
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }
    }

    setActiveStep(activeStep + 1);
  };

  // Previous step in the wizard
  const prevStep = () => {
    setActiveStep(activeStep - 1);
  };

  // Render step content
  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <VStack spacing={4} align="stretch">
            <Text color="#F0B90B" fontSize="xl" fontWeight="bold" mb={2}>
              Withdrawal Details
            </Text>

            <FormControl>
              <FormLabel color="#848E9C" fontSize="sm">Withdrawal Amount</FormLabel>
              <Input
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="e.g. 500"
                bg="#0B0E11"
                borderColor="#2B3139"
                color="#EAECEF"
                _hover={{ borderColor: "#F0B90B" }}
              />
            </FormControl>

            <FormControl>
              <FormLabel color="#848E9C" fontSize="sm">Cryptocurrency</FormLabel>
              <HStack spacing={2} mt={2} flexWrap="wrap">
                <Button
                  size="md"
                  bg={selectedCrypto === 'BTC' ? "#F0B90B" : "transparent"}
                  color={selectedCrypto === 'BTC' ? "#0B0E11" : "#EAECEF"}
                  borderWidth="1px"
                  borderColor={selectedCrypto === 'BTC' ? "#F0B90B" : "#2B3139"}
                  borderRadius="full"
                  _hover={{ borderColor: "#F0B90B" }}
                  px={4}
                  onClick={() => setSelectedCrypto('BTC')}
                >
                  Bitcoin
                </Button>
                <Button
                  size="md"
                  bg={selectedCrypto === 'USDT' ? "#F0B90B" : "transparent"}
                  color={selectedCrypto === 'USDT' ? "#0B0E11" : "#EAECEF"}
                  borderWidth="1px"
                  borderColor={selectedCrypto === 'USDT' ? "#F0B90B" : "#2B3139"}
                  borderRadius="full"
                  _hover={{ borderColor: "#F0B90B" }}
                  px={4}
                  onClick={() => setSelectedCrypto('USDT')}
                >
                  USDT (Tether)
                </Button>
                <Button
                  size="md"
                  bg={selectedCrypto === 'ETH' ? "#F0B90B" : "transparent"}
                  color={selectedCrypto === 'ETH' ? "#0B0E11" : "#EAECEF"}
                  borderWidth="1px"
                  borderColor={selectedCrypto === 'ETH' ? "#F0B90B" : "#2B3139"}
                  borderRadius="full"
                  _hover={{ borderColor: "#F0B90B" }}
                  px={4}
                  onClick={() => setSelectedCrypto('ETH')}
                >
                  Ethereum
                </Button>
                <Button
                  size="md"
                  bg={selectedCrypto === 'BNB' ? "#F0B90B" : "transparent"}
                  color={selectedCrypto === 'BNB' ? "#0B0E11" : "#EAECEF"}
                  borderWidth="1px"
                  borderColor={selectedCrypto === 'BNB' ? "#F0B90B" : "#2B3139"}
                  borderRadius="full"
                  _hover={{ borderColor: "#F0B90B" }}
                  px={4}
                  onClick={() => setSelectedCrypto('BNB')}
                >
                  Binance Coin
                </Button>
                <Button
                  size="md"
                  bg={selectedCrypto === 'XRP' ? "#F0B90B" : "transparent"}
                  color={selectedCrypto === 'XRP' ? "#0B0E11" : "#EAECEF"}
                  borderWidth="1px"
                  borderColor={selectedCrypto === 'XRP' ? "#F0B90B" : "#2B3139"}
                  borderRadius="full"
                  _hover={{ borderColor: "#F0B90B" }}
                  px={4}
                  onClick={() => setSelectedCrypto('XRP')}
                >
                  XRP
                </Button>
              </HStack>
            </FormControl>

            <Box p={4} bg="#1E2329" borderRadius="md" mt={2}>
              <Flex align="center" mb={2}>
                <Icon as={FaInfoCircle} color="#F0B90B" mr={2} />
                <Text color="#F0B90B" fontWeight="bold">Withdrawal Details</Text>
              </Flex>

              <Divider borderColor="#2B3139" mb={3} />

              <VStack spacing={2} align="stretch">
                <Flex justify="space-between">
                  <Text color="#848E9C" fontSize="sm">Available Balance:</Text>
                  <Text color="#EAECEF" fontWeight="bold" fontSize="sm">{availableBalance.toFixed(selectedCrypto === 'BTC' ? 8 : selectedCrypto === 'USDT' ? 2 : 4)} {selectedCrypto}</Text>
                </Flex>
                <Flex justify="space-between">
                  <Text color="#848E9C" fontSize="sm">Withdrawal Fee:</Text>
                  <Text color="#EAECEF" fontWeight="bold" fontSize="sm">{withdrawalFee.toFixed(selectedCrypto === 'BTC' ? 8 : selectedCrypto === 'USDT' ? 2 : 4)} {selectedCrypto}</Text>
                </Flex>
                <Flex justify="space-between">
                  <Text color="#848E9C" fontSize="sm">Minimum Withdrawal:</Text>
                  <Text color="#EAECEF" fontWeight="bold" fontSize="sm">{minWithdrawal.toFixed(selectedCrypto === 'BTC' ? 8 : selectedCrypto === 'USDT' ? 2 : 4)} {selectedCrypto}</Text>
                </Flex>
                <Flex justify="space-between">
                  <Text color="#848E9C" fontSize="sm">Processing Time:</Text>
                  <Text color="#EAECEF" fontWeight="bold" fontSize="sm">{processingTime}</Text>
                </Flex>
              </VStack>

              <Text color="#848E9C" fontSize="xs" mt={3}>
                * Withdrawal requests are processed after security verification.
              </Text>
            </Box>

            <Flex justify="flex-end" mt={6}>
              <Button
                bg="#F0B90B"
                color="#0B0E11"
                _hover={{ bg: "#F8D12F" }}
                onClick={nextStep}
                isDisabled={!amount || parseFloat(amount) <= 0 || parseFloat(amount) > availableBalance || parseFloat(amount) < minWithdrawal}
                w="100%"
              >
                Next
              </Button>
            </Flex>
          </VStack>
        );

      case 1:
        return (
          <VStack spacing={4} align="stretch">
            <Text color="#F0B90B" fontSize="xl" fontWeight="bold" mb={2}>
              Withdrawal Address
            </Text>

            <FormControl>
              <FormLabel color="#848E9C" fontSize="sm">Wallet Address</FormLabel>
              <Input
                value={walletAddress}
                onChange={(e) => setWalletAddress(e.target.value)}
                placeholder="Enter your cryptocurrency wallet address"
                bg="#0B0E11"
                borderColor="#2B3139"
                color="#EAECEF"
                _hover={{ borderColor: "#F0B90B" }}
              />
              <Text color="#848E9C" fontSize="xs" mt={1}>
                Address accuracy is important
              </Text>
            </FormControl>

            <FormControl>
              <FormLabel color="#848E9C" fontSize="sm">Memo / Tag (If required)</FormLabel>
              <Input
                value={memo}
                onChange={(e) => setMemo(e.target.value)}
                placeholder="Enter Memo or Tag information"
                bg="#0B0E11"
                borderColor="#2B3139"
                color="#EAECEF"
                _hover={{ borderColor: "#F0B90B" }}
              />
              <Text color="#848E9C" fontSize="xs" mt={1}>
                * Required for some cryptocurrencies (e.g. XRP)
              </Text>
            </FormControl>

            <Flex justify="space-between" mt={6}>
              <Button
                variant="outline"
                onClick={prevStep}
                borderColor="#2B3139"
                color="#EAECEF"
                _hover={{ borderColor: "#F0B90B" }}
                flex="1"
                mr={2}
              >
                Back
              </Button>
              <Button
                bg="#F0B90B"
                color="#0B0E11"
                _hover={{ bg: "#F8D12F" }}
                onClick={nextStep}
                isDisabled={!walletAddress}
                flex="1"
                ml={2}
              >
                Next
              </Button>
            </Flex>
          </VStack>
        );

      case 2:
        return (
          <VStack spacing={4} align="stretch">
            <Text color="#F0B90B" fontSize="xl" fontWeight="bold" mb={2}>
              Security Verification
            </Text>

            <Box p={4} bg="#1E2329" borderRadius="md">
              <Text color="#848E9C" fontSize="sm" mb={2}>Transaction Confirmation</Text>

              <VStack spacing={3} align="stretch">
                <Flex justify="space-between">
                  <Text color="#848E9C" fontSize="sm">Cryptocurrency:</Text>
                  <Text color="#EAECEF" fontWeight="bold" fontSize="sm">{selectedCrypto}</Text>
                </Flex>
                <Flex justify="space-between">
                  <Text color="#848E9C" fontSize="sm">Amount:</Text>
                  <Text color="#EAECEF" fontWeight="bold" fontSize="sm">{parseFloat(amount).toFixed(selectedCrypto === 'BTC' ? 8 : selectedCrypto === 'USDT' ? 2 : 4)} {selectedCrypto}</Text>
                </Flex>
                <Flex justify="space-between">
                  <Text color="#848E9C" fontSize="sm">Withdrawal Fee:</Text>
                  <Text color="#EAECEF" fontWeight="bold" fontSize="sm">{withdrawalFee.toFixed(selectedCrypto === 'BTC' ? 8 : selectedCrypto === 'USDT' ? 2 : 4)} {selectedCrypto}</Text>
                </Flex>
                <Flex justify="space-between">
                  <Text color="#848E9C" fontSize="sm">Net Amount:</Text>
                  <Text color="#0ECB81" fontWeight="bold" fontSize="sm">
                    {(parseFloat(amount) - withdrawalFee).toFixed(selectedCrypto === 'BTC' ? 8 : selectedCrypto === 'USDT' ? 2 : 4)} {selectedCrypto}
                  </Text>
                </Flex>
                <Divider borderColor="#2B3139" my={1} />
                <Flex justify="space-between">
                  <Text color="#848E9C" fontSize="sm">Wallet Address:</Text>
                  <Text color="#EAECEF" fontWeight="bold" fontFamily="monospace" fontSize="xs" maxW="180px" isTruncated>
                    {walletAddress}
                  </Text>
                </Flex>
                {memo && (
                  <Flex justify="space-between">
                    <Text color="#848E9C" fontSize="sm">Memo / Tag:</Text>
                    <Text color="#EAECEF" fontWeight="bold" fontSize="sm">{memo}</Text>
                  </Flex>
                )}
              </VStack>
            </Box>

            <FormControl>
              <FormLabel color="#848E9C" fontSize="sm">Email Verification Code</FormLabel>
              <Input
                placeholder="Enter the code sent to your email address"
                bg="#0B0E11"
                borderColor="#2B3139"
                color="#EAECEF"
                _hover={{ borderColor: "#F0B90B" }}
              />
            </FormControl>

            <Text color="#F0B90B" fontSize="xs" mt={2}>
              Withdrawal cannot be cancelled after confirmation. Please make sure all information is correct.
            </Text>

            <Flex justify="space-between" mt={6}>
              <Button
                variant="outline"
                onClick={prevStep}
                borderColor="#2B3139"
                color="#EAECEF"
                _hover={{ borderColor: "#F0B90B" }}
                flex="1"
                mr={2}
              >
                Back
              </Button>
              <Button
                bg="#F0B90B"
                color="#0B0E11"
                _hover={{ bg: "#F8D12F" }}
                onClick={handleSubmit}
                isLoading={isSubmitting}
                loadingText="Processing..."
                flex="1"
                ml={2}
              >
                Confirm Withdrawal
              </Button>
            </Flex>

            {isSubmitting && (
              <Box mt={4}>
                <Text fontSize="xs" color="#848E9C" mb={2}>
                  Processing your transaction...
                </Text>
                <Progress value={uploadProgress} size="xs" colorScheme="yellow" borderRadius="full" />
              </Box>
            )}
          </VStack>
        );

      default:
        return null;
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="md" isCentered>
      <ModalOverlay backdropFilter="blur(5px)" />
      <ModalContent bg="#0B0E11" borderColor="#2B3139" borderWidth="1px">
        <ModalHeader color="#F0B90B" borderBottomWidth="1px" borderColor="#2B3139">
          {t('withdrawModal.title', 'Withdraw Funds')}
        </ModalHeader>
        <ModalCloseButton color="#EAECEF" />

        <ModalBody py={6}>
          <Stepper index={activeStep} colorScheme="yellow" mb={8}>
            {steps.map((step, index) => (
              <Step key={index}>
                <StepIndicator>
                  <StepStatus
                    complete={<StepIcon />}
                    incomplete={<StepNumber />}
                    active={<StepNumber />}
                  />
                </StepIndicator>
                <Box flexShrink='0'>
                  <StepTitle color={activeStep === index ? "#F0B90B" : "#848E9C"}>{step.title}</StepTitle>
                  <StepDescription color="#848E9C">{step.description}</StepDescription>
                </Box>
                <StepSeparator />
              </Step>
            ))}
          </Stepper>

          {renderStepContent()}
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default NewWithdrawModal;
