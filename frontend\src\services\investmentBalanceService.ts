import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'https://api.shpnfinance.com/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_URL,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json'
  }
});

export interface InvestmentBalance {
  currency: string;
  totalEarnings: number;
  availableForWithdrawal: number;
  totalWithdrawn: number;
  activePackages: number;
  lastEarningDate?: string;
}

export interface WithdrawalEligibility {
  isEligible: boolean;
  availableBalance: number;
  minimumRequired: number;
  timeLockStatus: {
    isLocked: boolean;
    nextUnlockTime?: string;
    message?: string;
  };
  currency: string;
}

export interface InvestmentPackage {
  _id: string;
  userId: string;
  amount: number;
  currency: string;
  status: 'active' | 'completed' | 'cancelled';
  totalEarned: number;
  totalWithdrawn: number;
  dailyRate: number;
  createdAt: string;
  lastEarningDate?: string;
  lastWithdrawalDate?: string;
}

class InvestmentBalanceService {
  /**
   * Get investment balances for all currencies
   */
  async getInvestmentBalances(): Promise<InvestmentBalance[]> {
    try {
      const response = await api.get('/investment-packages/balances');
      return response.data.data || [];
    } catch (error) {
      console.error('Error fetching investment balances:', error);
      throw error;
    }
  }

  /**
   * Get investment balance for a specific currency
   */
  async getInvestmentBalance(currency: string): Promise<InvestmentBalance | null> {
    try {
      const response = await api.get(`/investment-packages/balances/${currency.toUpperCase()}`);
      return response.data.data || null;
    } catch (error) {
      console.error(`Error fetching investment balance for ${currency}:`, error);
      throw error;
    }
  }

  /**
   * Check withdrawal eligibility for a specific amount and currency
   */
  async checkWithdrawalEligibility(currency: string, amount: number): Promise<WithdrawalEligibility> {
    try {
      const response = await api.post('/investment-packages/withdrawal-eligibility', {
        currency: currency.toUpperCase(),
        amount
      });
      return response.data.data;
    } catch (error) {
      console.error('Error checking withdrawal eligibility:', error);
      throw error;
    }
  }

  /**
   * Get user's active investment packages
   */
  async getActiveInvestmentPackages(): Promise<InvestmentPackage[]> {
    try {
      const response = await api.get('/investment-packages/packages');
      return response.data.data || [];
    } catch (error) {
      console.error('Error fetching active investment packages:', error);
      throw error;
    }
  }

  /**
   * Get comprehensive investment data including packages and balances
   */
  async getComprehensiveInvestmentData(): Promise<{
    balances: InvestmentBalance[];
    packages: InvestmentPackage[];
    totalEarnings: number;
    totalWithdrawn: number;
  }> {
    try {
      const response = await api.get('/investment-packages/comprehensive');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching comprehensive investment data:', error);
      throw error;
    }
  }

  /**
   * Request withdrawal from investment earnings
   */
  async requestWithdrawal(params: {
    amount: number;
    currency: string;
    targetAddress: string;
    withdrawalType: 'interest' | 'commission';
    network?: string;
    memo?: string;
  }): Promise<{
    success: boolean;
    transactionId: string;
    estimatedProcessingTime: string;
    message: string;
  }> {
    try {
      const response = await api.post('/investment-packages/withdraw', params);
      return response.data;
    } catch (error) {
      console.error('Error requesting withdrawal:', error);
      throw error;
    }
  }

  /**
   * Get withdrawal history
   */
  async getWithdrawalHistory(params?: {
    page?: number;
    limit?: number;
    currency?: string;
    status?: string;
  }): Promise<{
    withdrawals: any[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  }> {
    try {
      const response = await api.get('/investment-packages/withdrawals', { params });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching withdrawal history:', error);
      throw error;
    }
  }

  /**
   * Calculate potential earnings for an amount
   */
  async calculatePotentialEarnings(amount: number, currency: string, days: number = 30): Promise<{
    dailyEarnings: number;
    monthlyEarnings: number;
    annualEarnings: number;
    dailyRate: number;
  }> {
    try {
      const response = await api.post('/investment-packages/calculate-earnings', {
        amount,
        currency: currency.toUpperCase(),
        days
      });
      return response.data.data;
    } catch (error) {
      console.error('Error calculating potential earnings:', error);
      throw error;
    }
  }

  /**
   * Get time lock status for withdrawals
   */
  async getTimeLockStatus(): Promise<{
    isLocked: boolean;
    nextUnlockTime?: string;
    currentTime: string;
    message: string;
  }> {
    try {
      const response = await api.get('/investment-packages/time-lock-status');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching time lock status:', error);
      throw error;
    }
  }

  /**
   * Get minimum withdrawal amounts for all currencies
   */
  async getMinimumWithdrawals(): Promise<Record<string, number>> {
    try {
      const response = await api.get('/investment-packages/minimum-withdrawals');
      return response.data.data || {};
    } catch (error) {
      console.error('Error fetching minimum withdrawals:', error);
      // Return default minimums if API fails
      return {
        USDT: 50,
        BTC: 0.001,
        ETH: 0.01,
        BNB: 0.1,
        DOGE: 100,
        TRX: 100
      };
    }
  }

  /**
   * Validate withdrawal request before submission
   */
  async validateWithdrawalRequest(params: {
    amount: number;
    currency: string;
    targetAddress: string;
    withdrawalType: 'interest' | 'commission';
  }): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
    estimatedFee: number;
    netAmount: number;
  }> {
    try {
      const response = await api.post('/investment-packages/validate-withdrawal', params);
      return response.data.data;
    } catch (error) {
      console.error('Error validating withdrawal request:', error);
      throw error;
    }
  }
}

export const investmentBalanceService = new InvestmentBalanceService();
export default investmentBalanceService;
